const express = require("express")
const router = express.Router()
const B2BAccount = require("../models/B2BAccount")
const B2BIndividualAccounts = require("../models/B2BIndividualAccounts")
const {sendPostRequest} = require("../config/ApiInstense")
const {registerUser} = require("../controllers/userController");
const Account = require("../models/Account");
const sendCardCreationWebhook = require("../config/webhook");
const Card = require("../models/B2BCard");
const IndividualOnboarding = require("../models/IndividualOnboarding");
const ProductVersion = require("../models/productVersions");

// Get all B2B accounts
router.get("/", async (req, res) => {
    try {
        const accounts = await B2BAccount.find().populate("parentCompany", "company_name").sort({createdAt: -1})

        res.status(200).json({
            success: true,
            count: accounts.length,
            data: accounts,
        })
    } catch (error) {
        res.status(500).json({
            success: false,
            message: "Server error",
            error: error.message,
        })
    }
})

// Get a single B2B account
router.get("/:id", async (req, res) => {
    try {
        const account = await B2BAccount.findById(req.params.id).populate("parentCompany")
            .populate("products")

        if (!account) {
            return res.status(404).json({
                success: false,
                message: "B2B account not found",
            })
        }

        const debitAccount =await Account.findOne({b2bCompany: req.params.id});

        res.status(200).json({
            success: true,
            data: account, debitAccount: debitAccount,
        })
    } catch (error) {
        res.status(500).json({
            success: false,
            message: "Server error",
            error: error.message,
        })
    }
})

// Create a new B2B account

router.post("/", async (req, res) => {
    try {
        const {
            companyName,
            clientCode,
            phoneNumber,
            authPhoneNumber,
            email,
            address, // incoming single address
            nip,
            regon,
            embossedName,
            parentCompany,
        } = req.body;

        // Prepare two address entries with different types
        const addressArray = [
            {
                ...address,
                type: "registration_address"
            },
            {
                ...address,
                type: "delivery_address"
            }
        ];

        const newAccount = new B2BAccount({
            companyName,
            clientCode,
            phoneNumber,
            authPhoneNumber,
            email,
            addresses: addressArray,
            nip,
            regon,
            embossedName,
            parentCompany,
        });

        const clientPayload = {
            companyName,
            clientCode,
            phoneNumber,
            authPhoneNumber,
            email,
            address: {
                street: address.street,
                buildingNumber: address.buildingNumber,
                apartmentNumber: address.apartmentNumber,
                city: address.city,
                zipCode: address.zipCode,
                country: address.country,
            },
            nip,
            regon,
            embossedName,
            company: true,
            customer: false,
        };

        console.log("Sending to external client API:", clientPayload);
        const result = await sendPostRequest("https://apifintech.sandbox.itcardpaymentservice.pl:30080/v1/clients", clientPayload);
        console.log("Client API response:", result);

        if (result.status?.toLowerCase() === "approved") {
            const savedAccount = await newAccount.save();

            await registerUser(companyName, email, [], "active", "corporate", savedAccount._id);

            return res.status(201).json({
                success: true,
                message: "B2B account created successfully",
                data: savedAccount,
                apiResponse: result,
            });
        } else {
            return res.status(400).json({
                success: false,
                message: "Failed to create B2B account in external system",
                error: result,
            });
        }

    } catch (error) {
        console.error("Error creating B2B account:", error);

        if (error.name === "ValidationError") {
            const messages = Object.values(error.errors).map((val) => val.message);
            return res.status(400).json({
                success: false,
                message: "Validation error",
                errors: messages,
            });
        }

        if (error.code === 11000) {
            return res.status(400).json({
                success: false,
                message: "Duplicate entry found",
                error: `${Object.keys(error.keyValue)[0]} already exists`,
            });
        }

        return res.status(500).json({
            success: false,
            message: "Server error",
            error: error.message,
        });
    }
});


// Update a B2B account
router.put("/:id", async (req, res) => {
    try {
        const updatedAccount = await B2BAccount.findByIdAndUpdate(
            req.params.id,
            {...req.body, updatedAt: Date.now()},
            {new: true, runValidators: true},
        )

        if (!updatedAccount) {
            return res.status(404).json({
                success: false,
                message: "B2B account not found",
            })
        }

        res.status(200).json({
            success: true,
            message: "B2B account updated successfully",
            data: updatedAccount,
        })
    } catch (error) {
        // Handle validation errors
        if (error.name === "ValidationError") {
            const messages = Object.values(error.errors).map((val) => val.message)
            return res.status(400).json({
                success: false,
                message: "Validation error",
                errors: messages,
            })
        }

        res.status(500).json({
            success: false,
            message: "Server error",
            error: error.message,
        })
    }
})

// Delete a B2B account
router.delete("/:id", async (req, res) => {
    try {
        const deletedAccount = await B2BAccount.findByIdAndDelete(req.params.id)

        if (!deletedAccount) {
            return res.status(404).json({
                success: false,
                message: "B2B account not found",
            })
        }

        res.status(200).json({
            success: true,
            message: "B2B account deleted successfully",
        })
    } catch (error) {
        res.status(500).json({
            success: false,
            message: "Server error",
            error: error.message,
        })
    }
})

const {body, validationResult} = require("express-validator");
router.post(
    '/:id/updatePermissions',
    [
        body('permissions').isArray().withMessage('Permissions must be an array'),
        body('permissionAudit').isArray().withMessage('Permission audit must be an array'),
    ],
    async (req, res) => {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({ errors: errors.array() });
        }

        const { id } = req.params;
        const { permissions, permissionAudit } = req.body;

        try {
            const currentCompany = await B2BAccount.findById(id);
            if (!currentCompany) {
                return res.status(404).json({ success: false, message: 'Company not found' });
            }

            // Old permissions
            const oldPermissions = currentCompany.permissions || [];

            // Track changes
            const addedPermissions = permissions.filter(p => !oldPermissions.includes(p));
            const removedPermissions = oldPermissions.filter(p => !permissions.includes(p));

            let changeDetails = [];
            if (addedPermissions.length) changeDetails.push(`Added: ${addedPermissions.join(", ")}`);
            if (removedPermissions.length) changeDetails.push(`Removed: ${removedPermissions.join(", ")}`);
            if (!addedPermissions.length && !removedPermissions.length) changeDetails.push("No changes in permissions");

            // Update permissions
            currentCompany.permissions = permissions;
            currentCompany.permissionAudit = permissionAudit;

            // Log permission update (from logged-in user info)
            currentCompany.permissionsLog.push({
                action: "Permission Update",
                details: changeDetails.join(" | "),
                username: req.user?.name || "Unknown User",
                email: req.user?.email || "Unknown Email"
            });

            await currentCompany.save();

            return res.status(200).json({
                success: true,
                message: 'Permissions updated successfully',
                changes: changeDetails
            });

        } catch (error) {
            console.error(error);
            res.status(400).json({ success: false, message: error.message || 'An error occurred' });
        }
    }
);



// Optimized create physical card route
router.post("/createCard/physical", async (req, res) => {
    try {
        const {
            clientCode, b2bClientId, embossName1, embossName2, phoneNumber,
            nickname, productCode, deliveryMethod, userId
        } = req.body;

        const individual = await IndividualOnboarding.findById(userId).populate("b2bClient");
        const account = await B2BIndividualAccounts.findOne({ parent: individual._id });

        if (!account) {
            return res.status(404).json({
                success: false,
                message: "Account not found for the provided b2bClientId"
            });
        }

        const cardData = {
            holder: account.owners[0].clientCode,
            embossName2: individual.b2bClient.clientCode.split("-")[1] || "",
            productCode,
            visual: productCode,
            delivery: { deliveryType: "LETTER" },
            pinDelivery: { deliveryType: "NONE" },
            extAppId: "testextAppId",
            account: {
                accNo: "**********************",
                currencyCode: account.currencyCode,
                productCode: "RYV1",
                owners: account.owners.map(owner => ({
                    clientCode: owner.clientCode,
                    relationship: owner.relationship || "OWN"
                }))
            },
            "3dsAuthMethods": ["MOBILE_APP"]
        };

        console.log("Sending data to external API:", JSON.stringify(cardData, null, 2));

        const url = "https://apifintech.sandbox.itcardpaymentservice.pl:30080/v1/cards/debitCard";
        const result = await sendPostRequest(url, cardData);

        if (!result.cardKey) {
            return res.status(400).json({
                success: false,
                message: "Card creation failed",
                error: result
            });
        }

        const DbCardData = {
            cardHash: result.cardHash,
            cardKey: result.cardKey,
            expDate: result.expDate,
            status: result.status,
            statusCode: result.statusCode,
            kind: result.kind,
            productCode: result.productCode,
            productDesc: result.productDesc,
            main: result.main,
            holder: result.holder,
            accNo: result.accNo,
            embossName1: result.embossName1,
            cardMask: result.cardMask,
            parent: b2bClientId,
            authPhoneNumber: phoneNumber,
            cardholder: individual._id,
            nickName: nickname,
            deliveryMethod
        };

        const newCard = new Card(DbCardData);
        await newCard.save();

        await sendCardCreationWebhook({ ...result, deliveryMethod });

        return res.status(201).json({
            success: true,
            message: "Physical card created successfully",
            data: newCard
        });
    } catch (error) {
        console.error("Error creating physical card:", error);
        return res.status(500).json({
            success: false,
            message: "Internal Server Error",
            error: error.message
        });
    }
});
router.post("/createCard/virtual", async (req, res) => {
    try {
        const {
            clientCode, clientId, userId, b2bClientId,
            nickname,productCode
        } = req.body;



        // Step 2: Fetch individual and populate b2bClient
        const individual = await IndividualOnboarding.findOne({clientID:clientId}).populate("b2bClient");
        // Step 1: Fetch account by clientCode from owners
        const account = await B2BIndividualAccounts.findOne({
           parent: individual._id
        });
        if (!account) {
            return res.status(404).json({
                success: false,
                message: "Account not found for the provided clientId"
            });
        }

        // Step 3: Prepare cardData payload
        const cardData = {
            holder: account.owners[0].clientCode,
            embossName2: individual.b2bClient.clientCode.split("-")[1] || "",
            productCode: productCode,
            visual: productCode,
            delivery: { deliveryType: "LETTER" },
            pinDelivery: { deliveryType: "NONE" },
            extAppId: "testextAppId",
            account: {
                accNo: "**********************",
                currencyCode: account.currencyCode,
                productCode: "RYV1",
                owners: account.owners.map(owner => ({
                    clientCode: owner.clientCode,
                    relationship: owner.relationship || "OWN"
                }))
            },
            "3dsAuthMethods": ["MOBILE_APP"]
        };

        console.log("Sending card creation payload:", JSON.stringify(cardData, null, 2));

        // Step 4: Send to external API
        const url = "https://apifintech.sandbox.itcardpaymentservice.pl:30080/v1/cards/debitCard";
        const result = await sendPostRequest(url, cardData);

        if (!result.cardKey) {
            return res.status(400).json({
                success: false,
                message: "Card creation failed",
                error: result
            });
        }

        // Step 5: Save response to DB
        const DbCardData = {
            cardHash: result.cardHash,
            cardKey: result.cardKey,
            expDate: result.expDate,
            status: result.status,
            statusCode: result.statusCode,
            kind: result.kind,
            productCode: result.productCode,
            productDesc: result.productDesc,
            main: result.main,
            holder: result.holder,
            accNo: result.accNo,
            embossName1: result.embossName1,
            cardMask: result.cardMask,
            parent: b2bClientId,
            cardholder: individual._id,
            nickName: nickname,
        };

        const newCard = new Card(DbCardData);
        await newCard.save();

        // Step 6: Optional webhook
        await sendCardCreationWebhook({ ...result });

        // Step 7: Return success
        return res.status(201).json({
            success: true,
            message: "Virtual card created successfully",
            data: newCard
        });

    } catch (error) {
        console.error("Error creating virtual card:", error);
        return res.status(500).json({
            success: false,
            message: "Internal Server Error",
            error: error.message
        });
    }
});


// Get all B2B Cards
// Get all B2B Cards by parent (b2bClientId)
router.get("/cards/parent/:parentId", async (req, res) => {
    try {

        const account = await B2BAccount.findById(req.params.parentId);

        const cards = await Card.find({parent:req.params.parentId});


        if (!cards || cards.length === 0) {
            return res.status(404).json({
                success: false,
                message: "No cards found for this parent ID",
            });
        }

        res.status(200).json({
            success: true,
            count: cards.length,
            data: cards,
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: "Failed to fetch cards by parent ID",
            error: error.message,
        });
    }
});


// Get single B2B Card by ID
router.get("/cards/:id", async (req, res) => {
    try {
        const card = await Card.findById(req.params.id);
        if (!card) {
            return res.status(404).json({
                success: false,
                message: "Card not found"
            });
        }

        res.status(200).json({
            success: true,
            data: card
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: "Failed to fetch card",
            error: error.message
        });
    }
});

router.get("/all/", async (req, res) => {

 return    res.status(200).json()
});
router.get("/:id/cardholders/", async (req, res) => {
    try {
        const accounts = await IndividualOnboarding.find({b2bClient: req.params.id});
        if (!accounts) {
            return res.status(404).json({
                success: false,
                message: "Accounts not found"
            });
        }


        res.status(200).json({
            success: true,
            data: accounts
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: "Failed to fetch cardholders",
            error: error.message
        });
    }
})


router.get("/cardholder/:id/cards/", async (req, res) => {
    try {
        // const account = await IndividualOnboarding.findById(req.params.id);
        // if (!account) {
        //     return res.status(404).json({
        //         success: false,
        //         message: "Cardholder not found"
        //     });
        // }

        // Use clientID, which matches the 'holder' field in cards
        const cards = await Card.find({ cardholder: req.params.id });

        res.status(200).json({
            success: true,
            data: cards
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: "Failed to fetch cards",
            error: error.message
        });
    }
});





const mongoose = require("mongoose");

router.post("/:id/products/assign", async (req, res) => {
    try {
        const { productIds } = req.body;

        if (!Array.isArray(productIds) || productIds.length === 0) {
            return res.status(400).json({
                success: false,
                message: "No product IDs provided.",
            });
        }

        // Validate ObjectIds
        const validIds = productIds.filter(id => mongoose.Types.ObjectId.isValid(id));

        if (validIds.length === 0) {
            return res.status(400).json({
                success: false,
                message: "No valid product IDs provided.",
            });
        }

        console.log("Assigning products:", validIds);

        const updatedAccount = await B2BAccount.findByIdAndUpdate(
            req.params.id,
            { $addToSet: { products: { $each: validIds } }, updatedAt: Date.now() },
            { new: true }
        ).populate("products");


        if (!updatedAccount) {
            return res.status(404).json({
                success: false,
                message: "B2B account not found",
            });
        }

        console.log("Updated Account:", updatedAccount);

        res.status(200).json({
            success: true,
            message: "Products assigned successfully",
            data: updatedAccount,
        });
    } catch (error) {
        console.error("Error assigning products:", error);
        res.status(500).json({
            success: false,
            message: "Internal server error",
            error: error.message,
        });
    }
});


router.get("/:id/products", async (req, res) => {
    try {
        const account = await B2BAccount.findById(req.params.id).populate("products");
        if (!account) {
            return res.status(404).json({
                success: false,
                message: "B2B account not found",
            });
        }

        res.status(200).json({
            success: true,
            account: account,
            products: account.products,
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: "Failed to fetch assigned products",
            error: error.message,
        });
    }
});

router.delete("/:id/products/:productId", async (req, res) => {
    try {
        const { id, productId } = req.params;

        // Validate ObjectIds
        if (!mongoose.Types.ObjectId.isValid(id) || !mongoose.Types.ObjectId.isValid(productId)) {
            return res.status(400).json({
                success: false,
                message: "Invalid B2B account ID or Product ID.",
            });
        }

        const updatedAccount = await B2BAccount.findByIdAndUpdate(
            id,
            { $pull: { products: productId }, updatedAt: Date.now() },
            { new: true }
        ).populate("products");

        if (!updatedAccount) {
            return res.status(404).json({
                success: false,
                message: "B2B account not found",
            });
        }

        res.status(200).json({
            success: true,
            message: "Product unassigned successfully",
            data: updatedAccount,
        });
    } catch (error) {
        console.error("Error unassigning product:", error);
        res.status(500).json({
            success: false,
            message: "Internal server error",
            error: error.message,
        });
    }
});

router.post("/updateAddress", async (req, res) => {
    const {
        onboardingId,
        building,
        street,
        apartment,
        city,
        stateProvince, // Not stored unless added in schema
        postalCode,
        country,
        type = "delivery_address", // Optional: fallback to delivery
    } = req.body;

    try {
        const client = await IndividualOnboarding.findById(onboardingId);
        if (!client) {
            return res.status(404).json({ success: false, message: "Client not found" });
        }

        // Prepare new address object
        client.address = {
            street,
            buildingNumber: building,
            apartmentNumber: apartment || "",
            city,
            country,
            zipCode: postalCode,
        };

        await client.save();

        return res.status(200).json({
            success: true,
            message: "Address updated successfully",
            data: client.addresses,
        });
    } catch (error) {
        console.error("Error updating address:", error);
        return res.status(500).json({
            success: false,
            message: "Internal server error",
            error: error.message,
        });
    }
});



module.exports = router
