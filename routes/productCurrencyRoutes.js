const express = require("express")
const router = express.Router()
const {
    getProductCurrencies,
    createProductCurrency, updateProductCurrency, getProductCurrency,
} = require("../controllers/ProductCurrencyController")
const ProductCurrency= require("../models/ProductCurrency");

// Route to get all product currencies
router.get("/", getProductCurrencies)

// Route to create a new product currency
router.post("/", createProductCurrency)

// Route to update the status of a product currency
router.patch("/:id", async (req, res) => {
    try {
        const { id } = req.params;

        // Fetch the current product currency
        const productCurrency = await getProductCurrency(id);

        if (!productCurrency) {
            return res.status(404).json({ message: "Product currency not found" });
        }

        // Toggle the is_active status
        const updatedStatus = !productCurrency.is_active;

        // Update the product currency with the new status
        const updatedProductCurrency = await updateProductCurrency(id, { is_active: updatedStatus });

        res.status(200).json({
            message: "Product currency status updated successfully",
            productCurrency: updatedProductCurrency
        });
    } catch (error) {
        console.error("Error updating product currency status:", error);
        res.status(500).json({ message: "Internal server error" });
    }
});

router.delete("/:id", async (req, res) => {
    const { id } = req.params;

    try {
        const deletedUser = await ProductCurrency.findByIdAndUpdate(
            id,
            { deleted_at: new Date() },
            { new: true }
        )
        console.dir(deletedUser)

        if (!deletedUser) {
            return res.status(404).json({ message: 'Record not found' });
        }

        res.json({ message: 'Record deleted successfully' });
    } catch (error) {
        res.status(500).json({ message: 'Error deleting Record', error });
    }
});

router.post("/approve", async (req, res) => {
    try {
        const {entityId} = req.body;

        // Perform approval logic, e.g., update the database
        if (!entityId) {
            return res.status(400).json({message: "record ID is required."});
        }
        const record = await ProductCurrency.findById(entityId);
        if (!record) {
            return res.status(404).json({message: "record not found."});
        }
        // Parse and increment the version number
        let currentVersion = parseFloat(record.version) || 0.0; // Default to 0.0 if version is not set
        let updatedVersion = (currentVersion + 0.1).toFixed(1); // Increment and format to one decimal place

        // Update the record status and version
        const updatedrecord = await ProductCurrency.findByIdAndUpdate(
            entityId,
            {status: "active", version: updatedVersion},
            {new: true} // Return the updated document
        );

        console.log(`Approving record with ID: ${entityId}`);

        res.status(200).json({message: "record approved successfully."});
    } catch (error) {
        console.error("Error approving record:", error);
        res.status(500).json({message: "Internal server error."});
    }
});
router.post("/decline", async (req, res) => {
    try {
        const {entityId, reason} = req.body;

        if (!entityId || !reason) {
            return res.status(400).json({message: "record ID and reason are required."});
        }
        const record = await ProductCurrency.findById(entityId);
        if (!record) {
            return res.status(404).json({message: "record not found."});
        }
        // Parse and increment the version number
        let currentVersion = parseFloat(record.version) || 0.0; // Default to 0.0 if version is not set
        let updatedVersion = (currentVersion + 0.1).toFixed(1); // Increment and format to one decimal place

        // Update the record status and version
        const updatedrecord = await ProductCurrency.findByIdAndUpdate(
            entityId,
            {status: "decline", reason: reason, version: updatedVersion},
            {new: true} // Return the updated document
        );
        // Perform decline logic (e.g., update the database)
        console.log(`Declining record with ID: ${entityId} for reason: ${reason}`);

        res.status(200).json({message: "record declined successfully."});
    } catch (error) {
        console.error("Error declining record:", error);
        res.status(500).json({message: "Internal server error."});
    }
});
router.post("/modify", async (req, res) => {
    try {
        const {entityId, instructions} = req.body;

        if (!entityId || !instructions) {
            return res.status(400).json({message: "record ID and modification instructions are required."});
        }
        const record = await ProductCurrency.findById(entityId);
        if (!record) {
            return res.status(404).json({message: "record not found."});
        }
        // Parse and increment the version number
        let currentVersion = parseFloat(record.version) || 0.0; // Default to 0.0 if version is not set
        let updatedVersion = (currentVersion + 0.2).toFixed(1); // Increment and format to one decimal place

        // Update the record status and version
        const updatedrecord = await ProductCurrency.findByIdAndUpdate(
            entityId,
            {status: "modify", reason: instructions, version: updatedVersion},
            {new: true} // Return the updated document
        );
        // Perform modify logic (e.g., update the database)
        console.log(`Modifying record with ID: ${entityId} with instructions: ${instructions}`);

        res.status(200).json({message: "record modification request submitted successfully."});
    } catch (error) {
        console.error("Error modifying record:", error);
        res.status(500).json({message: "Internal server error."});
    }
});
module.exports = router;

