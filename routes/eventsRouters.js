const express = require("express");
const Task = require("../models/EventTask");

const {
    createTask,
    updateTaskStatus,
    declineTask,
    modifyTask,
    getTaskById,
    getAllTasks
} = require('../config/EventHandler');
const router = express.Router();
router.get("/", async (req, res) => {
    try {
        // Fetch tasks where deleted_at is null and sort by created_at in descending order
        const tasks = await Task.find({deleted_at: null})
            .populate("user")
            .sort({createdAt: -1}); // Sort by created_at in descending order (latest first)
        res.json(tasks);
    } catch (error) {
        console.error("Error fetching tasks:", error);
        res.status(500).json({message: "Failed to fetch tasks"});
    }
});


router.put("/:id/complete", async (req, res) => {
    try {
        const {id} = req.params
        const {refId} = req.body


        updateTaskStatus(id, 'Done')
            .then(task => console.log('Updated Task:', task))
            .catch(err => console.error('Error:', err.message));

        res.status(200).json({message: "Success", data: id})
    } catch (error) {
        console.error("Error fetching tasks:", error)
        res.status(500).json({message: "Failed to fetch tasks"})
    }
})

// Existing routes
router.post('/', async (req, res) => {
    try {
        const task = await createTask(req.body);
        res.status(201).json(task);
    } catch (error) {
        res.status(400).json({ error: error.message });
    }
});

router.get('/', async (req, res) => {
    try {
        const tasks = await getAllTasks(req.query);
        res.status(200).json(tasks);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

router.get('/:id', async (req, res) => {
    try {
        const task = await getTaskById(req.params.id);
        res.status(200).json(task);
    } catch (error) {
        res.status(404).json({ error: error.message });
    }
});

// Complete task (existing functionality)
router.put('/:id/complete', async (req, res) => {
    try {
        const { actionPerformedBy } = req.body;
        const updatedTask = await updateTaskStatus(req.params.id, 'Done', actionPerformedBy);
        res.status(200).json(updatedTask);
    } catch (error) {
        res.status(400).json({ error: error.message });
    }
});

// Decline task with reason
router.put('/:id/decline', async (req, res) => {
    try {
        const { reason, actionPerformedBy } = req.body;
        if (!reason || reason.trim() === '') {
            return res.status(400).json({ error: 'Decline reason is required' });
        }

        const updatedTask = await declineTask(req.params.id, reason.trim(), actionPerformedBy);
        res.status(200).json(updatedTask);
    } catch (error) {
        res.status(400).json({ error: error.message });
    }
});

// Request task modification with reason
router.put('/:id/modify', async (req, res) => {
    try {
        const { reason, actionPerformedBy } = req.body;
        if (!reason || reason.trim() === '') {
            return res.status(400).json({ error: 'Modification reason is required' });
        }

        const updatedTask = await modifyTask(req.params.id, reason.trim(), actionPerformedBy);
        res.status(200).json(updatedTask);
    } catch (error) {
        res.status(400).json({ error: error.message });
    }
});

// Get records by type with status filtering
router.get('/records/:type', async (req, res) => {
    try {
        const records = await getRecordsByType(req.params.type, req.query);
        res.status(200).json(records);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});


module.exports = router;