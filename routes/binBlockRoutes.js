const express = require("express");
const router = express.Router();
const BinBlock = require("../models/BinBlocks");
const Cards = require("../models/AccountCard");

// ✅ Create a new BinBlock
router.post("/", async (req, res) => {
    try {
        const { binStart, binEnd, company, programme,product_version, cardRange} = req.body;
        const newBinBlock = new BinBlock({ binStart, binEnd, company, programme,product_version ,cardRange});
        await newBinBlock.save();
        res.status(201).json({ message: "BinBlock created successfully", newBinBlock });
    } catch (error) {
        console.error("Error creating BinBlock:", error);
        res.status(500).json({ error: "Internal server error" });
    }
});

// ✅ Get all BinBlocks
router.get("/", async (req, res) => {
    try {
        const binBlocks = await BinBlock.find().populate("company programme product_version");
        res.json(binBlocks);
    } catch (error) {
        console.error("Error fetching BinBlocks:", error);
        res.status(500).json({ error: "Internal server error" });
    }
});
// ✅ Place this before the /:id route
router.get("/global-usage", async (req, res) => {
    try {
        const all_cards = await Cards.find({});
        const total_cards = all_cards.length;

        const all_bins = await BinBlock.find({});

        let total_bins = 0;

        all_bins.forEach(bin => {


            const binStart =  Number.parseInt(bin.binStart.replace(/\D/g, ""));
            const binEnd = Number.parseInt(bin.binEnd.replace(/\D/g, ""))


            if (!isNaN(binStart) && !isNaN(binEnd) && binEnd >= binStart) {
                total_bins += (binEnd - binStart + 1);
            }
        });

        return res.status(200).json({
            totalCards: total_cards,
            totalBins: total_bins
        });

    } catch (error) {
        console.error("Error calculating global usage:", error);
        return res.status(500).json({ message: "Server Error", error });
    }
});
router.get("/bins-per-company", async (req, res) => {
    try {
        const all_bins = await BinBlock.find()
            .populate("company")
            .populate("product_version");

        const companyBinStats = {};

        for (const bin of all_bins) {

            const binStart =  Number.parseInt(bin.binStart.replace(/\D/g, ""));
            const binEnd = Number.parseInt(bin.binEnd.replace(/\D/g, ""))
            const company = bin.company;
            const productVersion = bin.product_version;

            if (!company || isNaN(binStart) || isNaN(binEnd) || binEnd < binStart) continue;

            const binCount = binEnd - binStart + 1;
            const companyId = company._id.toString();
            const companyName = company.company_name || "Unknown";

            // Initialize company data if not exists
            if (!companyBinStats[companyId]) {
                companyBinStats[companyId] = {
                    companyId,
                    companyName,
                    totalBins: 0,
                    usedBins: 0
                };
            }

            companyBinStats[companyId].totalBins += binCount;

            // Count used bins (cards issued under product_version)
            if (productVersion && productVersion.version_code) {
                const cardCount = await Cards.countDocuments({ productCode: productVersion.version_code });
                companyBinStats[companyId].usedBins += cardCount;
            }
        }

        res.status(200).json(Object.values(companyBinStats));

    } catch (error) {
        console.error("Error fetching bins per company:", error);
        res.status(500).json({ message: "Server Error", error });
    }
});


router.get("/cip/:id", async (req, res) => {
    try {
        const binBlocks = await BinBlock.find({ programme: req.params.id })
            .populate("company")
            .populate("product_version")
            .populate({
                path: "programme",
                populate: {
                    path: "productVersionName",
                    model: "ProductVersion"
                }
            });

        // Use for...of loop to allow await inside
        for (const block of binBlocks) {
            const versionCode = block?.product_version?.version_code;

            if (versionCode) {
                const cards = await Cards.find({ productCode: versionCode });
                block._doc.total_usage = cards.length; // attach total_usage to response
                block._doc.cards = cards; // optionally attach card data too
            } else {
                block._doc.total_usage = 0;
                block._doc.cards = [];
            }
        }
        res.json(binBlocks);
    } catch (error) {
        console.error("Error fetching BinBlocks:", error);
        res.status(500).json({ error: "Internal server error" });
    }
});


// ✅ Get a single BinBlock by ID
router.get("/:id", async (req, res) => {
    try {
        const binBlock = await BinBlock.findById(req.params.id).populate("company programme");
        if (!binBlock) {
            return res.status(404).json({ error: "BinBlock not found" });
        }
        res.json(binBlock);
    } catch (error) {
        console.error("Error fetching BinBlock:", error);
        res.status(500).json({ error: "Internal server error" });
    }
});

// ✅ Update a BinBlock
router.put("/:id", async (req, res) => {
    try {
        const updatedBinBlock = await BinBlock.findByIdAndUpdate(
            req.params.id,
            req.body,
            { new: true, runValidators: true }
        );

        if (!updatedBinBlock) {
            return res.status(404).json({ error: "BinBlock not found" });
        }

        res.json({ message: "BinBlock updated successfully", updatedBinBlock });
    } catch (error) {
        console.error("Error updating BinBlock:", error);
        res.status(500).json({ error: "Internal server error" });
    }
});

// ✅ Delete a BinBlock
router.delete("/:id", async (req, res) => {
    try {
        const deletedBinBlock = await BinBlock.findByIdAndDelete(req.params.id);
        if (!deletedBinBlock) {
            return res.status(404).json({ error: "BinBlock not found" });
        }
        res.json({ message: "BinBlock deleted successfully" });
    } catch (error) {
        console.error("Error deleting BinBlock:", error);
        res.status(500).json({ error: "Internal server error" });
    }
});



module.exports = router;
