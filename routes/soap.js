import express from 'express';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { simpleSoapHandler, getWsdl } from '../services/soapService.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const router = express.Router();

// SOAP endpoint
router.post('/soap', simpleSoapHandler);

// WSDL endpoint
router.get('/wsdl', getWsdl);

export default router;