const express = require("express");
const router = express.Router();
const {
    createIndividualOnboarding, getAllIndividualOnboardings, getIndividualOnboardingById
} = require("../controllers/onboardingPersonalController");
const IndividualOnboarding = require('../models/IndividualOnboarding');
const Onboarding = require('../models/Account');
// // POST route to create a new individual onboarding application
router.post('/', createIndividualOnboarding);

router.post('/activate', async function (req, res) {
    try {
        const { recordId } = req.body;

        if (!recordId) {
            return res.status(400).json({ error: 'recordId is required' });
        }

        const updatedRecord = await IndividualOnboarding.findByIdAndUpdate(
            recordId,
            { dashboardStatus: 'ACTIVE' },
            { new: true } // Returns the updated document
        );

        if (!updatedRecord) {
            return res.status(404).json({ error: 'Record not found' });
        }

        res.status(200).json({ message: 'Record activated successfully', data: updatedRecord });
    } catch (error) {
        res.status(500).json({ error: 'An error occurred while activating the record', details: error.message });
    }
});




// GET route to retrieve all onboarding applications

router.get('/accounts', async (req, res) => {
    try {
        const x = await Onboarding.find({});
        res.status(200).json(x);
    } catch (error) {
        console.error('Error retrieving onboarding:', error);
        res.status(500).json({ success: false, message: 'An error occurred while retrieving onboarding data' });
    }
})



// // GET route to retrieve all onboarding applications
router.get('/', getAllIndividualOnboardings);

// // GET route to retrieve a specific onboarding application by ID
router.get('/:id', getIndividualOnboardingById);


module.exports = router;