const {sendGetRequest, sendPostRequest, sendPutRequest, sendPatchRequest} = require("../config/ApiInstense");
const express = require("express");
const crypto = require('crypto');
const {getDecryptedCardNumber} = require("../utils/cardUtils");
const Card = require("../models/AccountCard");
const IndividualOnboarding = require('../models/IndividualOnboarding');
 const bcrypt = require('bcryptjs');
const router = express.Router();

// Get all cards
router.get("/", async function (req, res) {
    try {
        const cards = await Card.find();
        res.status(200).json({
            success: true, cards
        });
    } catch (error) {
        console.error("Error fetching cards:", error);
        res.status(500).json({
            success: false, message: "Error fetching cards", error: error.message
        });
    }
});
router.post("/:id/lock", async function (req, res) {
    const {id} = req.params;
    const {expDate, reason, description} = req.body;
    // API URL
    try {
        const url = "https://apifintech.sandbox.itcardpaymentservice.pl:30080/v1/cards/" + id + "/lock";

        const request = `{ "expDate": "${expDate}","reason": "${reason}","description": "${description}"}`;



        const apiResponse = await sendPostRequest(url, request);


        // console.log("Account saved to database:", apiResponse);
        res.status(200).json({
            success: true, card: apiResponse
        });

    } catch (error) {
        console.error("Error sending request:", error);
        res.status(500).json({
            success: false, message: "Error creating card or saving account", error: error.message
        });
    }
});
router.post("/:id/unlock", async function (req, res) {
    const {id} = req.params;
    const {expDate} = req.body;
    // API URL
    try {
        const url = "https://apifintech.sandbox.itcardpaymentservice.pl:30080/v1/cards/" + id + "/unlock";
        const request = `{"expDate": "${expDate}"}`;
        const apiResponse = await sendPostRequest(url, request);
        res.status(200).json({
            success: true, card: apiResponse
        });

    } catch (error) {
        console.error("Error sending request:", error);
        res.status(500).json({
            success: false, message: "Error creating card or saving account", error: error.message
        });
    }
});
router.get("/:id/status", async function (req, res) {
    const {id} = req.params;
    // API URL
    try {
        const url = "https://apifintech.sandbox.itcardpaymentservice.pl:30080/v1/cards/" + id + "/status";

        const apiResponse = await sendGetRequest(url);
        res.status(200).json({
            success: true, card: apiResponse
        });

    } catch (error) {
        console.error("Error sending request:", error);
        res.status(500).json({
            success: false, message: "Error creating card or saving account", error: error.message
        });
    }
});




router.get("/:id/limits", async function (req, res) {
    const {id} = req.params;
    // API URL
    try {
        const url = "https://apifintech.sandbox.itcardpaymentservice.pl:30080/v1/cards/" + id + "/limits";

        const apiResponse = await sendGetRequest(url);
        res.status(200).json({
            success: true, limits: apiResponse
        });

    } catch (error) {
        console.error("Error sending request:", error);
        res.status(500).json({
            success: false, message: "Error creating card or saving account", error: error.message
        });
    }
});

router.post("/:id/patch-card-limits", async function (req, res) {
    const {id} = req.params;

        try {

            const url = "https://apifintech.sandbox.itcardpaymentservice.pl:30080/v1/cards/" + id + "/limits";
       //      const request = `
       // [ {
       //     "name":"${embossName1}",
       //     "value":"${embossName2}"
       //  }  ]
       //  `;

        const apiResponse = await sendPatchRequest(url, req.body);
        res.status(200).json({
            success: true, card: apiResponse
        });

    } catch (error) {
        console.error("Error sending request:", error);
        res.status(500).json({
            success: false, message: "Error while calling api", error: error.message
        });
    }
});


router.post("/:id/close", async function (req, res) {
    const {id} = req.params;
    const {expDate} = req.body;
    try {
        const url = "https://apifintech.sandbox.itcardpaymentservice.pl:30080/v1/cards/" + id + "/restrictClosed";
        const request = `{"expDate": "${expDate}"}`;

        const apiResponse = await sendPostRequest(url, request);
        res.status(200).json({
            success: true, card: apiResponse
        });

    } catch (error) {
        console.error("Error sending request:", error);
        res.status(500).json({
            success: false, message: "Error creating card or saving account", error: error.message
        });
    }
});
router.post("/:id/restrict", async function (req, res) {
    const {id} = req.params;
    const {expDate} = req.body;
    try {
        const url = "https://apifintech.sandbox.itcardpaymentservice.pl:30080/v1/cards/" + id + "/restrict";
        const request = `{"expDate": "${expDate}"}`;

        const apiResponse = await sendPostRequest(url, request);
        res.status(200).json({
            success: true, card: apiResponse
        });

    } catch (error) {
        console.error("Error sending request:", error);
        res.status(500).json({
            success: false, message: "Error creating card or saving account", error: error.message
        });
    }
});
router.post("/:id/restrictFraud", async function (req, res) {
    const {id} = req.params;
    const {expDate} = req.body;
    try {
        const url = "https://apifintech.sandbox.itcardpaymentservice.pl:30080/v1/cards/" + id + "/restrictFraud";
        const request = `{"expDate": "${expDate}"}`;

        const apiResponse = await sendPostRequest(url, request);
        res.status(200).json({
            success: true, card: apiResponse
        });

    } catch (error) {
        console.error("Error sending request:", error);
        res.status(500).json({
            success: false, message: "Error creating card or saving account", error: error.message
        });
    }
});
router.post("/:id/restrictStolen", async function (req, res) {
    const {id} = req.params;
    const {expDate} = req.body;
    try {
        const url = "https://apifintech.sandbox.itcardpaymentservice.pl:30080/v1/cards/" + id + "/restrictStolen";
        const request = `{"expDate": "${expDate}"}`;

        const apiResponse = await sendPostRequest(url, request);
        res.status(200).json({
            success: true, card: apiResponse
        });

    } catch (error) {
        console.error("Error sending request:", error);
        res.status(500).json({
            success: false, message: "Error creating card or saving account", error: error.message
        });
    }
});
router.post("/:id/restrictLost", async function (req, res) {
    const {id} = req.params;
    const {expDate} = req.body;
    try {
        const url = "https://apifintech.sandbox.itcardpaymentservice.pl:30080/v1/cards/" + id + "/restrictLost";
        const request = `{"expDate": "${expDate}"}`;

        const apiResponse = await sendPostRequest(url, request);
        res.status(200).json({
            success: true, card: apiResponse
        });

    } catch (error) {
        console.error("Error sending request:", error);
        res.status(500).json({
            success: false, message: "Error creating card or saving account", error: error.message
        });
    }
});
router.post("/:id/forcePinLock", async function (req, res) {
    const {id} = req.params;
    const {expDate} = req.body;
    try {
        const url = "https://apifintech.sandbox.itcardpaymentservice.pl:30080/v1/cards/" + id + "/forcePinLock";
        const request = `{"expDate": "${expDate}"}`;

        const apiResponse = await sendPostRequest(url, request);
        res.status(200).json({
            success: true, card: apiResponse
        });

    } catch (error) {
        console.error("Error sending request:", error);
        res.status(500).json({
            success: false, message: "Error creating card or saving account", error: error.message
        });
    }
});
router.post("/:id/restrictMobileLost", async function (req, res) {
    const {id} = req.params;
    const {expDate} = req.body;
    try {
        const url = "https://apifintech.sandbox.itcardpaymentservice.pl:30080/v1/cards/" + id + "/restrictMobileLost";
        const request = `{"expDate": "${expDate}"}`;

        const apiResponse = await sendPostRequest(url, request);
        res.status(200).json({
            success: true, card: apiResponse
        });

    } catch (error) {
        console.error("Error sending request:", error);
        res.status(500).json({
            success: false, message: "Error creating card or saving account", error: error.message
        });
    }
});
router.get("/:id/resetPinTries", async function (req, res) {
    const {id} = req.params;
    try {
        const url = "https://apifintech.sandbox.itcardpaymentservice.pl:30080/v1/cards/" + id + "/resetPinTries";
        const request = ``;

        const apiResponse = await sendPostRequest(url, request);
        res.status(200).json({
            success: true, card: apiResponse
        });

    } catch (error) {
        console.error("Error sending request:", error);
        res.status(500).json({
            success: false, message: "Error creating card or saving account", error: error.message
        });
    }
});
router.post("/:id/resign", async function (req, res) {
    const {id} = req.params;
    const {expDate, reason, description} = req.body;
    try {
        const url = "https://apifintech.sandbox.itcardpaymentservice.pl:30080/v1/cards/" + id + "/resignCard";
        const request = `{ "expDate": "${expDate}","reason": "${reason}","description": "${description}"}`;

        const apiResponse = await sendPostRequest(url, request);
        res.status(200).json({
            success: true, card: apiResponse
        });

    } catch (error) {
        console.error("Error sending request:", error);
        res.status(500).json({
            success: false, message: "Error creating card or saving account", error: error.message
        });
    }
});
router.post("/:id/cancel-resign", async function (req, res) {
    const {id} = req.params;
    const {expDate} = req.body;
    try {
        const url = "https://apifintech.sandbox.itcardpaymentservice.pl:30080/v1/cards/" + id + "/cancelResignCard";
        const request = `{"expDate": "${expDate}"}`;

        const apiResponse = await sendPostRequest(url, request);
        res.status(200).json({
            success: true, card: apiResponse
        });

    } catch (error) {
        console.error("Error sending request:", error);
        res.status(500).json({
            success: false, message: "Error creating card or saving account", error: error.message
        });
    }
});
router.post("/:id/replaceCard", async function (req, res) {
    const {id} = req.params;
    const {expDate} = req.body;
    try {
        const url = "https://apifintech.sandbox.itcardpaymentservice.pl:30080/v1/cards/" + id + "/replaceCard";
        const request = `{"expDate": "${expDate}"}`;

        const apiResponse = await sendPostRequest(url, request);
        res.status(200).json({
            success: true, card: apiResponse
        });

    } catch (error) {
        console.error("Error sending request:", error);
        res.status(500).json({
            success: false, message: "Error creating card or saving account", error: error.message
        });
    }
});
router.post("/:id/activate", async function (req, res) {
    const {id} = req.params;
    const {expDate} = req.body;
    try {
        const url = "https://apifintech.sandbox.itcardpaymentservice.pl:30080/v1/cards/" + id + "/activate";
        const request = `{"expDate": "${expDate}"}`;

        const apiResponse = await sendPostRequest(url, request);
        res.status(200).json({
            success: true, card: apiResponse
        });

    } catch (error) {
        console.error("Error sending request:", error);
        res.status(500).json({
            success: false, message: "Error creating card or saving account", error: error.message
        });
    }
});
router.post("/:id/changeName", async function (req, res) {
    const {id} = req.params;
    const {embossName1,embossName2} = req.body;
    try {
        const url = "https://apifintech.sandbox.itcardpaymentservice.pl:30080/v1/cards/" + id + "/embossName";
        const request = `{"embossName1": "${embossName1}", "embossName2": "${embossName2}"}`;

        const apiResponse = await sendPostRequest(url, request);
        res.status(200).json({
            success: true, card: apiResponse
        });

    } catch (error) {
        console.error("Error sending request:", error);
        res.status(500).json({
            success: false, message: "Error while calling api", error: error.message
        });
    }
});
router.get("/:id/associatedClients", async function (req, res) {
    const {id} = req.params;
    try {
        const url = "https://apifintech.sandbox.itcardpaymentservice.pl:30080/v1/cards/" + id + "/associatedClients";


        const apiResponse = await sendGetRequest(url);
        res.status(200).json({
            success: true, card: apiResponse
        });

    } catch (error) {
        console.error("Error sending request:", error);
        res.status(500).json({
            success: false, message: "Error creating card or saving account", error: error.message
        });
    }
});
router.get("/:id/embossInfo", async function (req, res) {
    const {id} = req.params;
    try {
        const url = "https://apifintech.sandbox.itcardpaymentservice.pl:30080/v1/cards/" + id + "/embossInfo";


        const apiResponse = await sendPostRequest(url, '');
        res.status(200).json({
            success: true, card: apiResponse
        });

    } catch (error) {
        console.error("Error sending request:", error);
        res.status(500).json({
            success: false, message: "Error creating card or saving account", error: error.message
        });
    }
});
router.get("/:id/actionLog", async function (req, res) {
    const { id } = req.params;

    try {
        // Get today's date
        const today = new Date();
        const endDate = today.toISOString().split("T")[0]; // Current date in YYYY-MM-DD format

        // Get the date 30 days ago
        const last30Days = new Date(today);
        last30Days.setDate(today.getDate() - 30);
        const startDate = last30Days.toISOString().split("T")[0]; // 30 days ago in YYYY-MM-DD format

        const url = `https://apifintech.sandbox.itcardpaymentservice.pl:30080/v1/cards/${id}/actionLog/?start_date=${startDate}&end_date=${endDate}&language=EN`;

        const apiResponse = await sendGetRequest(url);
        console.log(url);

        res.status(200).json({
            success: true,
            actionLog: apiResponse,
        });

    } catch (error) {
        console.error("Error sending request:", error);
        res.status(500).json({
            success: false,
            message: "Error fetching action log",
            error: error.message,
        });
    }
});
router.get("/:id/transactions", async function (req, res) {
    const {id} = req.params;

    try {
        // Get today's date
        const today = new Date();
        const endDate = today.toISOString().split('T')[0]; // Get the current date in YYYY-MM-DD format

        // Get the date 30 days ago
        const last30Days = new Date(today);
        last30Days.setDate(today.getDate() - 30);
        const startDate = last30Days.toISOString().split('T')[0]; // Get the date 30 days ago in YYYY-MM-DD format

        const url = `https://apifintech.sandbox.itcardpaymentservice.pl:30080/v1/cards/${id}/transactions?id_type=CARD_KEY&start_date=${startDate}&end_date=${endDate}&start_amount=0&end_amount=10000&successful=false&current_account=false&page=1&per_page=10`;

        const apiResponse = await sendGetRequest(url);
        console.log(apiResponse)
        res.status(200).json({
            success: true, transactions: apiResponse
        });

    } catch (error) {
        console.error("Error sending request:", error);
        res.status(500).json({
            success: false, message: "Error creating card or saving account", error: error.message
        });
    }
});

router.get("/:id/card-number", async function (req, res) {
    const { id } = req.params;

    const response = await getDecryptedCardNumber(id);

    if (response.success) {
        res.status(200).json(response);
    } else {
        res.status(500).json({
            success: false,
            message: "Error creating card or saving account",
            error: response.message
        });
    }
});



router.post("/:id/change-3d-ans", async function (req, res) {
    const {id} = req.params;
    const {newAnswer} = req.body;
    try {
        const bcrypt = require('bcryptjs');
        const newPassword = await bcrypt.hash(newAnswer, 10)

        await Card.findOneAndUpdate(
            { cardKey: id },   // assuming cardId is actually the cardKey
            {set_pin: true, current_password: newPassword}, // Set set_pin and current_password to true with newAnswer
        );
        res.status(200).json({
            success: true
        });

    } catch (error) {
        console.error("Error sending request:", error);
        res.status(500).json({
            success: false, message: "Error while calling api", error: error.message
        });
    }
});
router.post("/:id/change-3d-phone", async (req, res) => {
    const { id } = req.params
    const { newPhoneNumber } = req.body
    try {
        const c_card = await Card.findOneAndUpdate(
            { cardKey: id }, // assuming cardId is actually the cardKey
            { authPhoneNumber: newPhoneNumber },
        )

        // Fixed syntax for nested field update
        const onb = await IndividualOnboarding.findByIdAndUpdate(c_card.onboarding, {
            $set: { "personalInfo.authPhoneNumber": newPhoneNumber },
        })

        res.status(200).json({
            success: true,
        })
    } catch (error) {
        console.error("Error sending request:", error)
        res.status(500).json({
            success: false,
            message: "Error while calling api",
            error: error.message,
        })
    }
})



router.post("/:id/change-nickname", async (req, res) => {
    const { id } = req.params
    const { nickname } = req.body
    try {
        const c_card = await Card.findOneAndUpdate(
            { cardKey: id }, // assuming cardId is actually the cardKey
            { nickName: nickname },
        )

        res.status(200).json({
            success: true,
        })
    } catch (error) {
        console.error("Error sending request:", error)
        res.status(500).json({
            success: false,
            message: "Error while calling api",
            error: error.message,
        })
    }
})

router.post("/:id/set-limit", async function (req, res) {
    const {id} = req.params;
    const {limitType,limitValue} = req.body;
    console.log(limitType,limitValue)
    try {
        const url = "https://apifintech.sandbox.itcardpaymentservice.pl:30080/v1/cards/" + id + "/limits/"+limitType;

        const request = `{"value": ${limitValue}}`;
        console.log(url, request)
        const apiResponse = await sendPutRequest(url, request);

        res.status(200).json({
            success: true, card: apiResponse
        });

    } catch (error) {
        console.error("Error sending request:", error);
        res.status(500).json({
            success: false, message: "Error while calling api", error: error.message
        });
    }
});



function encryptPin(pin) {
    // Step 1: Generate a random 6-digit number

    // Step 2: Compute SHA-256 hash
    const hash = crypto.createHash('sha256').update(pin).digest();

    // Step 3: Encode in Base64

    return hash.toString('base64');

}
module.exports = router;