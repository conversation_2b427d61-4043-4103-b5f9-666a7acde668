const express = require('express');
const router = express.Router();
const ProgrammeManagerType = require('../models/ProgrammeManagerType')
const {createTask} = require("../config/EventHandler");
const User = require("../models/user");

router.post('/', async (req, res) => {
    try {
        const bin = new ProgrammeManagerType(req.body);

        var p = await bin.save();
        const user = await User.findById(req.body.created_by);
        const ip =
            req.headers["x-forwarded-for"]?.split(",").shift() ||
            req.socket?.remoteAddress;
        const taskData = {
            refId: p._id,
            type: 'Programme Manager Type',
            title: user.name + ' requested a new Programme Manager Type "' + req.body.manager_type + '"',
            date: new Date(),
            user: user._id,
            ipAddress: ip,
        };
        await createTask(taskData)


        res.status(201).json({success: true, data: bin});
    } catch (err) {
        res.status(400).json({success: false, message: err.message});
    }
});

router.get('/', async (req, res) => {
    try {
        const bin = await ProgrammeManagerType.find({deleted_at: null})
            .populate("created_by")
            .populate("bin_currency")
            .populate({
                path: "bin_type",
                populate: [
                    {  path: "binCategory"  },
                    {  path: "binVariant"  },
                ]
            })
            .sort({created_at: -1});
        res.json(bin)
    } catch (err) {
        res.status(400).json({success: false, message: err.message});
    }
});


router.delete("/:id", async (req, res) => {
    const {id} = req.params;

    try {
        const deletedUser = await ProgrammeManagerType.findByIdAndUpdate(
            id,
            {deleted_at: new Date()}, // Set deleted_at field
            {new: true}
        )

        if (!deletedUser) {
            return res.status(404).json({message: 'Record not found'});
        }

        res.json({message: 'Record deleted successfully'});
    } catch (error) {
        res.status(500).json({message: 'Error deleting Record', error});
    }
});


router.post("/approve", async (req, res) => {
    try {
        const {entityId} = req.body;

        // Perform approval logic, e.g., update the database
        if (!entityId) {
            return res.status(400).json({message: "record ID is required."});
        }
        const record = await ProgrammeManagerType.findById(entityId);
        if (!record) {
            return res.status(404).json({message: "record not found."});
        }
        // Parse and increment the version number
        let currentVersion = parseFloat(record.version) || 0.0; // Default to 0.0 if version is not set
        let updatedVersion = (currentVersion + 0.1).toFixed(1); // Increment and format to one decimal place

        // Update the record status and version
        const updatedrecord = await ProgrammeManagerType.findByIdAndUpdate(
            entityId,
            {status: "active", version: updatedVersion},
            {new: true} // Return the updated document
        );

        console.log(`Approving record with ID: ${entityId}`);

        res.status(200).json({message: "record approved successfully."});
    } catch (error) {
        console.error("Error approving record:", error);
        res.status(500).json({message: "Internal server error."});
    }
});


router.post("/decline", async (req, res) => {
    try {
        const {entityId, reason} = req.body;

        if (!entityId || !reason) {
            return res.status(400).json({message: "record ID and reason are required."});
        }
        const record = await ProgrammeManagerType.findById(entityId);
        if (!record) {
            return res.status(404).json({message: "record not found."});
        }
        // Parse and increment the version number
        let currentVersion = parseFloat(record.version) || 0.0; // Default to 0.0 if version is not set
        let updatedVersion = (currentVersion + 0.1).toFixed(1); // Increment and format to one decimal place

        // Update the record status and version
        const updatedrecord = await ProgrammeManagerType.findByIdAndUpdate(
            entityId,
            {status: "decline", reason: reason, version: updatedVersion},
            {new: true} // Return the updated document
        );
        // Perform decline logic (e.g., update the database)
        console.log(`Declining record with ID: ${entityId} for reason: ${reason}`);

        res.status(200).json({message: "record declined successfully."});
    } catch (error) {
        console.error("Error declining record:", error);
        res.status(500).json({message: "Internal server error."});
    }
});
router.post("/modify", async (req, res) => {
    try {
        const {entityId, instructions} = req.body;

        if (!entityId || !instructions) {
            return res.status(400).json({message: "record ID and modification instructions are required."});
        }
        const record = await ProgrammeManagerType.findById(entityId);
        if (!record) {
            return res.status(404).json({message: "record not found."});
        }
        // Parse and increment the version number
        let currentVersion = parseFloat(record.version) || 0.0; // Default to 0.0 if version is not set
        let updatedVersion = (currentVersion + 0.2).toFixed(1); // Increment and format to one decimal place

        // Update the record status and version
        const updatedrecord = await ProgrammeManagerType.findByIdAndUpdate(
            entityId,
            {status: "modify", reason: instructions, version: updatedVersion},
            {new: true} // Return the updated document
        );
        // Perform modify logic (e.g., update the database)
        console.log(`Modifying record with ID: ${entityId} with instructions: ${instructions}`);

        res.status(200).json({message: "record modification request submitted successfully."});
    } catch (error) {
        console.error("Error modifying record:", error);
        res.status(500).json({message: "Internal server error."});
    }
});


module.exports = router;