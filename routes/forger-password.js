const express = require('express');
const bcrypt = require('bcrypt');
const crypto = require('crypto');
const nodemailer = require('nodemailer');
const OtpToken = require('../models/OtpToken');
const User = require('../models/user');
const {sign} = require("jsonwebtoken");

const router = express.Router();

// Configure email transporter (replace with your SMTP settings)
const transporter = nodemailer.createTransport({
    service: 'gmail',
    secure: 'true',
    auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS
    },
});

// Login route


// Request password reset OTP
router.post('/forgot-password', async (req, res) => {
    try {
        const {email} = req.body;

        // Validate email
        if (!email) {
            return res.status(400).json({message: 'Email is required'});
        }

        // Find user by email
        const user = await User.findOne({email: email.toLowerCase()});
        if (!user) {
            // For security reasons, don't reveal that the email doesn't exist
            return res.status(200).json({message: 'If your email exists in our system, you will receive a password reset OTP'});
        }

        // Generate a 6-digit OTP
        const otp = crypto.randomInt(100000, 999999).toString();

        // Hash the OTP for storage
        const hashedOtp = await bcrypt.hash(otp, 10);

        // Set expiration time (e.g., 15 minutes from now)
        const expiresAt = new Date();
        expiresAt.setMinutes(expiresAt.getMinutes() + 15);

        // Delete any existing OTPs for this user
        await OtpToken.deleteMany({userId: user._id, type: 'password_reset'});

        // Create new OTP record
        await OtpToken.create({
            userId: user._id,
            token: hashedOtp,
            type: 'password_reset',
            expiresAt
        });

        // Send email with OTP
        const mailOptions = {
            from: process.env.EMAIL_FROM,
            to: email,
            subject: 'Password Reset OTP',
            html: `
        <h1>Password Reset</h1>
        <p>You requested a password reset. Use the following OTP to reset your password:</p>
        <h2 style="font-size: 24px; letter-spacing: 2px; text-align: center; padding: 10px; background-color: #f0f0f0; border-radius: 4px;">${otp}</h2>
        <p>This OTP will expire in 15 minutes.</p>
        <p>If you didn't request this, please ignore this email.</p>
      `
        };

        await transporter.sendMail(mailOptions);

        res.status(200).json({message: 'Password reset OTP has been sent to your email'});
    } catch (error) {
        console.error('Forgot password error:', error);
        res.status(500).json({message: 'An error occurred while processing your request'});
    }
});

// Reset password with OTP
router.post('/reset-password', async (req, res) => {
    try {
        const {email, otp, password} = req.body;

        // Validate input
        if (!email || !otp || !password) {
            return res.status(400).json({message: 'Email, OTP, and new password are required'});
        }

        // Validate password strength
        if (password.length < 8) {
            return res.status(400).json({message: 'Password must be at least 8 characters long'});
        }

        // Find user by email
        const user = await User.findOne({email: email.toLowerCase()});
        if (!user) {
            return res.status(404).json({message: 'User not found'});
        }

        // Find the most recent OTP for this user
        const otpRecord = await OtpToken.findOne({
            userId: user._id,
            type: 'password_reset'
        }).sort({createdAt: -1});

        if (!otpRecord) {
            return res.status(400).json({message: 'No OTP found. Please request a new one'});
        }

        // Check if OTP is expired
        if (new Date() > otpRecord.expiresAt) {
            await OtpToken.deleteOne({_id: otpRecord._id}); // Clean up expired OTP
            return res.status(400).json({message: 'OTP has expired. Please request a new one'});
        }

        // Verify OTP
        const isOtpValid = await bcrypt.compare(otp, otpRecord.token);
        if (!isOtpValid) {
            return res.status(400).json({message: 'Invalid OTP'});
        }

        // Hash the new password
        const hashedPassword = await bcrypt.hash(password, 10);

        // Update user's password
        await User.updateOne({_id: user._id}, {password: hashedPassword});

        // Delete the used OTP
        await OtpToken.deleteOne({_id: otpRecord._id});

        res.status(200).json({message: 'Password has been reset successfully'});
    } catch (error) {
        console.error('Reset password error:', error);
        res.status(500).json({message: 'An error occurred while resetting your password'});
    }
});

// Helper function to generate JWT token (implement with your JWT library)
function generateToken(user) {
    // This is a placeholder - implement with jsonwebtoken or similar library
    return sign({id: user._id}, process.env.JWT_SECRET, {expiresIn: '1d'});

}

module.exports = router;