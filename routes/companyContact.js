const express = require("express");
const router = express.Router();
const CompanyContact = require("../models/Contact");

// CREATE a new contact
router.post("/", async (req, res) => {
    try {
        const contact = await CompanyContact.create(req.body);
        res.status(201).json(contact);
    } catch (error) {
        res.status(400).json({ error: error.message });
    }
});

// GET all contacts
router.get("/", async (req, res) => {
    try {
        const contacts = await CompanyContact.find().populate("company");
        res.status(200).json(contacts);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// GET a single contact by ID
router.get("/:id", async (req, res) => {
    try {
        const contact = await CompanyContact.findById(req.params.id).populate("company");
        if (!contact) return res.status(404).json({ error: "Contact not found" });
        res.status(200).json(contact);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// UPDATE a contact by ID
router.put("/:id", async (req, res) => {
    try {
        const contact = await CompanyContact.findByIdAndUpdate(req.params.id, req.body, {
            new: true,
            runValidators: true,
        });
        if (!contact) return res.status(404).json({ error: "Contact not found" });
        res.status(200).json(contact);
    } catch (error) {
        res.status(400).json({ error: error.message });
    }
});

// DELETE a contact by ID
router.delete("/:id", async (req, res) => {
    try {
        const contact = await CompanyContact.findByIdAndDelete(req.params.id);
        if (!contact) return res.status(404).json({ error: "Contact not found" });
        res.status(200).json({ message: "Contact deleted successfully" });
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

module.exports = router;
