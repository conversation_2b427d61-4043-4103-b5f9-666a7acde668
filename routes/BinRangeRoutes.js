const express = require("express");

const {
    createBinRang<PERSON>,
    getAllBinRang<PERSON>,
    getBinRangeById,
    updateBinRange,
    deleteBinRange,
} = require("../controllers/binRangeController");
const BinRange = require("../models/BinRange");

const router = express.Router();



// Routes
router.route("/").post(createBinRange).get(getAllBinRanges)

router
    .route("/:id")
    .get(getBinRangeById)
    .put(updateBinRange)
    .delete(deleteBinRange)

// Approval route
router.post("/approve", async (req, res) => {
    try {
        const {entityId} = req.body;

        // Perform approval logic, e.g., update the database
        if (!entityId) {
            return res.status(400).json({message: "record ID is required."});
        }
        const record = await BinRange.findById(entityId);
        if (!record) {
            return res.status(404).json({message: "record not found."});
        }
        // Parse and increment the version number
        let currentVersion = parseFloat(record.version) || 0.0; // Default to 0.0 if version is not set
        let updatedVersion = (currentVersion + 0.1).toFixed(1); // Increment and format to one decimal place

        // Update the record status and version
        const updatedrecord = await BinRange.findByIdAndUpdate(entityId, {
                status: "active",
                version: updatedVersion
            }, {new: true} // Return the updated document
        );

        console.log(`Approving record with ID: ${entityId}`);

        res.status(200).json({message: "record approved successfully."});
    } catch (error) {
        console.error("Error approving record:", error);
        res.status(500).json({message: "Internal server error."});
    }
});

module.exports = router;

