const express = require('express');
const router = express.Router();
const axios = require('axios');
const https = require('https');
const crypto = require('crypto');
const jwt = require('jsonwebtoken');
const fs = require('fs');
const path = require("path");
const Card = require("../models/AccountCard");

const {sendPostRequest} = require("../config/ApiInstense");
function createHttpsAgent() {
    const certPath = path.join(__dirname, '..', 'config', 'RYVL_API.crt');
    const keyPath = path.join(__dirname, '..', 'config', 'RYVL_API.key');
    return new https.Agent({
        cert: fs.readFileSync(certPath),
        key: fs.readFileSync(keyPath),
    });
}
async function changePin(cardId, expDate, newPIN) {
    const host = 'apifintech.sandbox.itcardpaymentservice.pl';
    const port = '30080';
    const baseUrl = `https://${host}:${port}`;

    const url = `${baseUrl}/v1/cards/${cardId}/auth`;
    const headers = {
        "Content-Type": "application/json;charset=UTF-8",
        "Accept": "application/json;charset=UTF-8",
    };



    const agent = createHttpsAgent()

    const data = JSON.stringify({resource: "CHANGE_PIN"});

    try {
        const response = await axios.post(url, data, {headers, httpsAgent: agent});
        const accessToken = response.data.accessToken;
        console.log(response.data);

        const decodedJwt = jwt.decode(accessToken, {complete: true});
        const pubKey = decodedJwt.payload.pubKey;

        const buffer = Buffer.from(newPIN, 'utf-8');
        const encryptedPin = crypto.publicEncrypt(
            {
                key: pubKey,
                padding: crypto.constants.RSA_PKCS1_OAEP_PADDING,
                oaepHash: "sha256",  // Ensure SHA-256 is used
            },
            buffer
        );
        const encryptedPinBase64 = encryptedPin.toString('base64');


        const secondUrl = decodedJwt.payload.vsu;
        const secondHeaders = {
            "Content-Type": "application/json;charset=UTF-8",
            "Accept": "application/json;charset=UTF-8",
            "Authorization": `Bearer ${accessToken}`,
        };

        const secondData = JSON.stringify({
            expDate: expDate,
            pin: encryptedPinBase64,
        });

        const secondResponse = await axios.post(secondUrl, secondData, {headers: secondHeaders, httpsAgent: agent});
        console.log(secondResponse)


        await Card.findOneAndUpdate(
            { cardKey: cardId },   // assuming cardId is actually the cardKey
            { set_pin: true }
        );


        return secondResponse.data;
    } catch (error) {
        console.error(error);
        throw new Error(error);
    }
}

router.post("/:id/change-pin", async function (req, res) {
    try {
        const {id} = req.params;
        const {expDate, newPin} = req.body;
        const result = await changePin(id, expDate, newPin);
        console.dir(result);
        res.json({success: true, data: result});
    } catch (error) {
        res.status(500).json({success: false, message: error.message});
    }
});


router.post("/:id/auth-pin", async function (req, res) {
    try {
        const {id} = req.params;
        const {pin} = req.body;
        const url = "https://apifintech.sandbox.itcardpaymentservice.pl:30080/v1/cards/" + id + "/holder/authorize";
        const request = `{"pin": "${pin}"}`;

        const apiResponse = await sendPostRequest(url, request);
        console.dir(apiResponse)
        res.status(200).json({
            success: true,  apiResponse
        });
    } catch (error) {
        res.status(500).json({success: false, message: error.message});
    }
});

router.post("/:id/changeName", async function (req, res) {
    const {id} = req.params;
    const {embossName1,embossName2} = req.body;
    try {
        const url = "https://apifintech.sandbox.itcardpaymentservice.pl:30080/v1/cards/" + id + "/embossName";
        const request = `{"embossName1": "${embossName1}", "embossName2": "${embossName2}"}`;

        const apiResponse = await sendPostRequest(url, request);
        res.status(200).json({
            success: true, card: apiResponse
        });

    } catch (error) {
        console.error("Error sending request:", error);
        res.status(500).json({
            success: false, message: "Error while calling api", error: error.message
        });
    }
});


module.exports = router;
