const express = require('express');
const multer = require('multer');
const mongoose = require('mongoose');
const fs = require('fs');
const path = require('path');
const CardImage = require('../models/CardImages'); // Import your Mongoose model

const router = express.Router();

// Configure Multer for file uploads
const storage = multer.diskStorage({
    destination: (req, file, cb) => {
        cb(null, 'uploads/');
    },
    filename: (req, file, cb) => {
        const extension = file.originalname.split('.').pop();
        const timestamp = Date.now();

        let namePrefix = '';
        if (file.fieldname === 'front_side') {
            namePrefix = req.body.front_side_name || 'front';
        } else if (file.fieldname === 'back_side') {
            namePrefix = req.body.back_side_name || 'back';
        }

        const safeName = namePrefix.replace(/\s+/g, '_'); // Remove spaces
        const newFilename = `${safeName}_${timestamp}.${extension}`;
        cb(null, newFilename);
    }
});

const upload = multer({ storage });

// Create card image
router.post('/', upload.fields([
    { name: 'front_side', maxCount: 1 },
    { name: 'back_side', maxCount: 1 },
]), async (req, res) => {
    try {
        const { product_version, company, front_side_name, back_side_name } = req.body;
        const frontSideFile = req.files['front_side']?.[0];
        const backSideFile = req.files['back_side']?.[0];

        if (!product_version || !frontSideFile || !backSideFile) {
            return res.status(400).json({ message: 'Missing required fields' });
        }

        const newCardImage = new CardImage({
            company,
            front_side_name,
            back_side_name,
            product_version,
            front_side: `/uploads/${frontSideFile.filename}`,
            back_side: `/uploads/${backSideFile.filename}`,
        });

        const savedCardImage = await newCardImage.save();
        res.status(201).json(savedCardImage);
    } catch (error) {
        console.error('Error saving card image:', error);
        res.status(500).json({ message: 'Internal server error' });
    }
});

// Get all card images
router.get('/', async (req, res) => {
    try {
        const cardImages = await CardImage.find()
            .populate('company')
            .populate('product_version')
            .sort({ created_at: -1 });

        res.json(cardImages);
    } catch (error) {
        console.error('Error retrieving card images:', error);
        res.status(500).json({ message: 'Internal server error' });
    }
});

// Delete card image
router.delete("/:id", async (req, res) => {
    try {
        const { id } = req.params;

        const cardImage = await CardImage.findById(id);
        if (!cardImage) {
            return res.status(404).json({ message: "Card image not found" });
        }

        // Delete image files
        if (cardImage.front_side) {
            const frontSidePath = path.join(__dirname, "..", cardImage.front_side);
            if (fs.existsSync(frontSidePath)) fs.unlinkSync(frontSidePath);
        }

        if (cardImage.back_side) {
            const backSidePath = path.join(__dirname, "..", cardImage.back_side);
            if (fs.existsSync(backSidePath)) fs.unlinkSync(backSidePath);
        }

        await CardImage.findByIdAndDelete(id);
        res.status(200).json({ message: "Card image deleted successfully" });
    } catch (error) {
        console.error("Error deleting card image:", error);
        res.status(500).json({ message: "Internal server error" });
    }
});

module.exports = router;
