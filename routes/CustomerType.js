const express = require("express");
const router = express.Router();
const { createCustomerType, getCustomerTypes } = require("../controllers/customerTypeController");
const  CustomerType = require("../models/CustomerType");

// Route to create a new card programme type
router.post("/", createCustomerType);

// Route to fetch all card programme types
router.get("/", getCustomerTypes);




router.delete("/:id", async (req, res) => {
    const { id } = req.params;

    try {
        const deletedUser = await CustomerType.findByIdAndUpdate(
            id,
            { deleted_at: new Date() }, // Set deleted_at field
            { new: true }
        )

        if (!deletedUser) {
            return res.status(404).json({ message: 'Record not found' });
        }

        res.json({ message: 'Record deleted successfully' });
    } catch (error) {
        res.status(500).json({ message: 'Error deleting Record', error });
    }
});

module.exports = router;