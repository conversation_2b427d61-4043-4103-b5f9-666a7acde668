const express = require('express');
const { generateJwtToken } = require("../config/LegecyService");
const axios = require("axios");

const router = express.Router();

// ✅ CHECK FEE
router.post('/checkFee', async (req, res) => {
    try {
        const {
            OperationType,
            CustomerType,
            ProductType,
            Location,
            Modifier
        } = req.body;

        const token = generateJwtToken();

        const data =JSON.stringify( {
            OperationType,
            CustomerType,
            ProductType,
            Location ,
            Modifier
        });

        // {
        //     "OperationType": "Card Issuing Fee",
        //     "CustomerType": "Consumer",
        //     "ProductType": "Debit Physical Card",
        //     "Location": "BG",
        //     "Modifier": "DHL"
        // }

        console.log(data);
        const response = await axios.post(
            'https://card-auth-staging.ryvyl.eu/api/cip/check-tx-fee',
            data,
            {
                headers: {
                    Authorization: `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            }
        );

        res.json(response.data);
    } catch (error) {
        console.error('Error fetching fee check:', error.response?.data || error.message);
        res.status(500).json({ error: error.response?.data || error.message });
    }
});

// ✅ APPLY FEE with auto-generated retrievalReferenceNumber
router.post('/applyFee', async (req, res) => {
    try {
        const {
            OperationType,
            CustomerType,
            ProductType,
            Location,
            Modifier,
            transactionAmount,
            region,
            IBAN,
            cardID,
            mcc
        } = req.body;

        const token = generateJwtToken();

        // ✅ generate unique timestamp
        const retrievalReferenceNumber = Date.now().toString();

        const response = await axios.post(
            'https://card-auth-staging.ryvyl.eu/api/cip/apply-tx-fee',
            {
                OperationType,
                CustomerType,
                ProductType,
                Location,
                Modifier,
                transactionAmount,
                region,
                retrievalReferenceNumber,
                IBAN,
                cardID,
                mcc
            },
            {
                headers: {
                    Authorization: `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            }
        );

        res.json(response.data);
    } catch (error) {
        console.error('Error applying fee:', error.response?.data || error.message);
        res.status(500).json({ error: error.response?.data || error.message });
    }
});

module.exports = router;
