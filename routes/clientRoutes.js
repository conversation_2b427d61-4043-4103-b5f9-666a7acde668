const express = require('express');
const router = express.Router();
const mongoose = require('mongoose');

const Account = require('../models/Account');
const Card = require("../models/AccountCard");
const IndividualOnboarding = require("../models/IndividualOnboarding");
const ProductVersion = require("../models/productVersions");

const { sendPostRequest, sendGetRequest, sendPutRequest } = require("../config/ApiInstense");
const sendCardCreationWebhook = require("../config/webhook");
const { country_currency } = require("../data");
const { generateJwtToken } = require("../config/LegecyService");
const axios = require("axios");
const WebhookLog = require("../models/WebhookLog"); // ✅ adjust the path as needed


// ✅ Create Account
router.post("/createAccount", async (req, res) => {
    try {
        const { clientId, userId } = req.body;

        const onboardingData = await IndividualOnboarding.findOne({ clientID: clientId }).populate("productVersion");
        const code = onboardingData?.productVersion?.find(v => v.version_name.toLowerCase().includes("vtl"))?.version_code;

        const requestBody = {
            productCode: code,
            currencyCode: "EUR",
            accNo: "**********************",
            owners: [{ clientCode: clientId, relationship: "OWN" }]
        };

        const url = "https://apifintech.sandbox.itcardpaymentservice.pl:30080/v1/accounts/debitAccount";
        const result = await sendPostRequest(url, JSON.stringify(requestBody));

        if (result?.accNo) {
            const accountData = {
                onboarding: userId,
                accountNumber: result.accNo,
                status: result.status,
                currencyCode: result.currencyCode,
                currencyName: result.currencyName,
                owners: result.owners.map(owner => ({
                    clientCode: clientId,
                    relationship: owner.relationship,
                    mainOwner: owner.mainOwner
                }))
            };

            await new Account(accountData).save();
            return res.status(200).json({ success: true, message: "Account created", account: result });
        }

        throw new Error("Account creation failed");

    } catch (error) {
        console.error("Account creation error:", error);
        return res.status(500).json({ success: false, message: "Error creating account", error: error.message });
    }
});


// ✅ Create Virtual Card
router.post("/createCard/virtual", async (req, res) => {

        let { clientId, currencyCode, embossName1, accNo, nickname, userId, productCode } = req.body;

    //     if (!productCode) {
    //         const onboardingData = await IndividualOnboarding.findOne({ clientID: clientId }).populate("productVersion");
    //         if (!onboardingData) return res.status(404).json({ message: "Invalid Client ID" , success: false});
    //         if (!onboardingData.productVersion) return res.status(404).json({ message: "Product Not Assigned!" , success: false});
    //
    //         productCode = onboardingData.productVersion?.[0]?.version_code;
    //     }
    // if (!productCode) return res.status(404).json({ message: "Product Not Assigned!" , success: false });



    const requestBody = {
            holder: clientId,
            productCode:  productCode,
            visual: productCode,
            delivery: { deliveryType: "LETTER" },
            pinDelivery: { deliveryType: "LETTER" },
            account: {
                currencyCode:   currencyCode.toString() ,
                productCode,
                owners: [{ clientCode: clientId, relationship: "OWN" }],
                accNo
            },
            "3dsAuthMethods": ["MOBILE_APP"]
        };
    console.log(requestBody);
    try {
        const url = "https://apifintech.sandbox.itcardpaymentservice.pl:30080/v1/cards/debitCard";
        const apiResponse = await sendPostRequest(url, JSON.stringify(requestBody));


        const cardData = {
            cardHash: apiResponse.cardHash,
            cardKey: apiResponse.cardKey,
            expDate: apiResponse.expDate,
            status: apiResponse.status,
            statusCode: apiResponse.statusCode,
            kind: apiResponse.kind,
            productCode: apiResponse.productCode,
            productDesc: apiResponse.productDesc,
            main: apiResponse.main,
            holder: apiResponse.holder,
            accNo: apiResponse.accNo,
            embossName1: apiResponse.embossName1,
            cardMask: apiResponse.cardMask,
            onboarding: userId,
            nickName: nickname,
        };

        await new Card(cardData).save();
        await sendCardCreationWebhook(apiResponse);

        return res.status(200).json({ success: true, message: "Virtual Card created", account: cardData });

    } catch (error) {
        console.error("Virtual Card creation error:", error);
        return res.status(500).json({ success: false, message: "Error creating virtual card", error: error.message });
    }
});


// ✅ Create Physical Card
router.post("/createCard/physical", async (req, res) => {
    try {
        const { clientId, accNo, nickname, deliveryMethod, userId, productCode, phoneNumber, country } = req.body;

        const version = await ProductVersion.findOne({ version_code: productCode });
        const currencyCode = country_currency.find(v =>
            v.alphabeticcode === version.version_name.slice(-7, -4))?.numericcode;

        const requestBody = {
            holder: clientId,
            productCode,
            visual: productCode,
            delivery: { deliveryType: "LETTER" },
            pinDelivery: { deliveryType: "LETTER" },
            account: {
                currencyCode,
                productCode,
                owners: [{ clientCode: clientId, relationship: "OWN" }],
                accNo
            },
            "3dsAuthMethods": ["MOBILE_APP"]
        };

        const url = "https://apifintech.sandbox.itcardpaymentservice.pl:30080/v1/cards/debitCard";
        const apiResponse = await sendPostRequest(url, JSON.stringify(requestBody));

        const cardData = {
            cardHash: apiResponse.cardHash,
            cardKey: apiResponse.cardKey,
            expDate: apiResponse.expDate,
            status: apiResponse.status,
            statusCode: apiResponse.statusCode,
            kind: apiResponse.kind,
            productCode: apiResponse.productCode,
            productDesc: apiResponse.productDesc,
            main: apiResponse.main,
            holder: apiResponse.holder,
            accNo: apiResponse.accNo,
            embossName1: apiResponse.embossName1,
            cardMask: apiResponse.cardMask,
            onboarding: userId,
            authPhoneNumber: phoneNumber,
            nickName: nickname,
            deliveryMethod
        };

        await new Card(cardData).save();
        await sendCardCreationWebhook(apiResponse);

        const fee = await applyFee("Consumer", "BG", "DHL", country, apiResponse.accNo, apiResponse.cardKey, deliveryMethod.price);
        console.log("Card fee:", fee);

        return res.status(200).json({ success: true, message: "Physical Card created", account: cardData });

    } catch (error) {
        console.error("Physical Card creation error:", error);
        return res.status(500).json({ success: false, message: "Error creating physical card", error: error.message });
    }
});


// ✅ Fee Application
async function applyFee(customerType, location, modifier, region, iban, cardID, fee) {
    const token = generateJwtToken();
    const webhookUrl = 'https://card-auth-staging.ryvyl.eu/api/cip/fee-transaction';

    const payload = {
        feeType: "1",
        feeNarrative: "Card Delivery Fee",
        feeAmount: fee,
        FXFeeAmount: "0",
        currency: "EUR",
        transactionAmount: "0",
        region,
        retrievalReferenceNumber: Date.now().toString(),
        IBAN: iban,
        cardID,
        mcc: "4215"
    };

    try {
        const response = await axios.post(
            webhookUrl,
            JSON.stringify(payload),
            {
                headers: {
                    Authorization: `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            }
        );

        // ✅ Log successful webhook to MongoDB
        await WebhookLog.create({
            event: "fee",
            webhookUrl,
            payloadSent: payload,
            responseStatus: response.status,
            responseBody: response.data,
            error: null
        });

        return response.data;
    } catch (error) {
        // ✅ Log failed webhook to MongoDB
        await WebhookLog.create({
            event: "fee",
            webhookUrl,
            payloadSent: payload,
            responseStatus: error.response?.status || 500,
            responseBody: error.response?.data || null,
            error: error.message || error.toString()
        });

        throw error; // Optionally rethrow so the caller knows it failed
    }
}


// ✅ Get Card Details by ID
router.get("/card/:id", async (req, res) => {
    try {
        const { id } = req.params;
        const url = `https://apifintech.sandbox.itcardpaymentservice.pl:30080/v1/cards/${id}`;
        const apiResponse = await sendGetRequest(url);

        const dbCard = await Card.findOne({ cardKey: id }).populate("onboarding");

        return res.status(200).json({ success: true, card: apiResponse, dbCard });

    } catch (error) {
        console.error("Card detail error:", error);
        return res.status(500).json({ success: false, message: "Error fetching card details", error: error.message });
    }
});


// ✅ Get Client Info from ITCard API
router.get('/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const url = `https://apifintech.sandbox.itcardpaymentservice.pl:30080/v1/clients/${id}`;
        const apiResponse = await sendGetRequest(url);

        return res.status(200).json({ success: true, client: apiResponse });

    } catch (error) {
        console.error("Client fetch error:", error);
        return res.status(500).json({ success: false, message: "Error fetching client", error: error.message });
    }
});


// ✅ Add or Update Address
router.post("/addAddress", async (req, res) => {
    try {
        const { clientId, building, street, apartment, city, stateProvince, postalCode, country } = req.body;

        const existingClient = await IndividualOnboarding.findOne({ clientID: clientId });
        if (!existingClient) return res.status(404).json({ error: "Client not found" });

        const addressUpdate = {
            street,
            buildingNumber: building,
            apartmentNumber: apartment || "",
            stateProvince,
            city,
            country,
            zipCode: postalCode
        };

        existingClient.address = { ...existingClient.address.toObject(), ...addressUpdate };
        await existingClient.save();

        const url = `https://apifintech.sandbox.itcardpaymentservice.pl:30080/v1/clients/${clientId}/address`;
        const apiResponse = await sendPutRequest(url, JSON.stringify({ address: addressUpdate }));

        if (apiResponse.status !== "APPROVED") {
            return res.status(500).json({ success: false, message: "Address change failed" });
        }

        return res.status(200).json({ success: true, message: "Address added successfully" });

    } catch (e) {
        console.error("Address update error:", e);
        return res.status(500).json({ success: false, message: "Error adding address", error: e.message });
    }
});

module.exports = router;
