const express = require("express");
const router = express.Router();
const GenericMessage = require("../models/GenericMessage");

// Save any structure into MongoDB
router.post("/message", async (req, res) => {
    try {
        const newMessage = new GenericMessage({ data: req.body });
        const saved = await newMessage.save();

        res.status(201).json({
            success: true,
            message: "Webhook received successfully",
        });
    } catch (err) {
        res.status(500).json({
            success: false,
            error: err.message || "Something went wrong",
        });
    }
});

module.exports = router;
