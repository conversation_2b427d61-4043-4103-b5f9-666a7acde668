const express = require("express")
const router = express.Router()
const BinType = require("../models/BinType")
const mongoose = require("mongoose")
const User = require("../models/user");
const {createTask} = require("../config/EventHandler");
const {isValidObjectId} = mongoose

// Middleware to check if ID is valid
const validateObjectId = (req, res, next) => {
    if (!isValidObjectId(req.params.id)) {
        return res.status(400).json({success: false, message: "Invalid ID format"})
    }
    next()
}

// GET all bin types (with optional filters)
router.get("/", async (req, res) => {
    try {
        const {type, status, programmeType, binVariant, binCategory} = req.query

        // Build filter object
        const filter = {deleted_at: null} // Only get non-deleted items

        if (type) filter.type = type
        if (status) filter.status = status
        if (programmeType && isValidObjectId(programmeType)) filter.programmeType = programmeType
        if (binVariant && isValidObjectId(binVariant)) filter.binVariant = binVariant
        if (binCategory && isValidObjectId(binCategory)) filter.binCategory = binCategory

        const binTypes = await BinType.find(filter)
            .populate("programmeType")
            .populate("binVariant")
            .populate({
                path: "binCategory",
                populate: {
                    path: "currency"
                }
            })
            .populate("company", "company_name")
            .populate("created_by", "name email")
            .sort({ created_at: -1 });

        res.status(200).json({
            success: true,
            count: binTypes.length,
            data: binTypes,
        })
    } catch (error) {
        res.status(500).json({
            success: false,
            message: "Server Error",
            error: error.message,
        })
    }
})

// GET a single bin type by ID
router.get("/:id", validateObjectId, async (req, res) => {
    try {
        const binType = await BinType.findOne({
            _id: req.params.id,
            deleted_at: null,
        })
            .populate("programmeType")
            .populate("binVariant")
            .populate("binCategory")
            .populate("created_by", "name email")

        if (!binType) {
            return res.status(404).json({
                success: false,
                message: "Bin type not found",
            })
        }

        res.status(200).json({
            success: true,
            data: binType,
        })
    } catch (error) {
        res.status(500).json({
            success: false,
            message: "Server Error",
            error: error.message,
        })
    }
})

// POST create a new bin type
router.post("/", async (req, res) => {
    try {
        // Extract data from request body
        const {type, programmeType, binVariant, binCategory, status, version, reason,company} = req.body

        // Validate required fields
        if (!type || !programmeType || !binVariant || !binCategory) {
            return res.status(400).json({
                success: false,
                message: "Please provide all required fields",
            })
        }

        // Validate ObjectIds
        if (!isValidObjectId(programmeType) || !isValidObjectId(binVariant) || !isValidObjectId(binCategory)) {
            return res.status(400).json({
                success: false,
                message: "Invalid ID format for reference fields",
            })
        }

        // Create new bin type
        // Note: In a real app, you would get created_by from authenticated user
        const created_by = req.body.created_by

        if (!created_by || !isValidObjectId(created_by)) {
            return res.status(400).json({
                success: false,
                message: "Valid created_by user ID is required",
            })
        }

        const newBinType = new BinType({
            type,
            programmeType,
            binVariant,
            binCategory,
            status: status || "pending",
            version,
            reason,company,
            created_by,
        })

        const savedBinType = await newBinType.save()


        const user = await User.findById(req.body.created_by);
        const ip =
            req.headers["x-forwarded-for"]?.split(",").shift() ||
            req.socket?.remoteAddress;
        const taskData = {
            refId: savedBinType._id,
            type: 'Bin Type',
            title: user.name + ' requested a new Bin Type "' + savedBinType.type + '"',
            date: new Date(),
            user: user._id,
            ipAddress:ip,
        };
        await createTask(taskData)
        res.status(201).json({
            success: true,
            data: savedBinType,
        })
    } catch (error) {
        res.status(500).json({
            success: false,
            message: "Server Error",
            error: error.message,
        })
    }
})

// PUT update a bin type
router.put("/:id", validateObjectId, async (req, res) => {
    try {
        const {type, programmeType, binVariant, binCategory, status, version, reason} = req.body

        // Find bin type first to check if it exists and isn't deleted
        const binType = await BinType.findOne({
            _id: req.params.id,
            deleted_at: null,
        })

        if (!binType) {
            return res.status(404).json({
                success: false,
                message: "Bin type not found",
            })
        }

        // Validate ObjectIds if provided
        if (programmeType && !isValidObjectId(programmeType)) {
            return res.status(400).json({
                success: false,
                message: "Invalid programmeType ID format",
            })
        }

        if (binVariant && !isValidObjectId(binVariant)) {
            return res.status(400).json({
                success: false,
                message: "Invalid binVariant ID format",
            })
        }

        if (binCategory && !isValidObjectId(binCategory)) {
            return res.status(400).json({
                success: false,
                message: "Invalid binCategory ID format",
            })
        }

        // Update fields
        if (type) binType.type = type
        if (programmeType) binType.programmeType = programmeType
        if (binVariant) binType.binVariant = binVariant
        if (binCategory) binType.binCategory = binCategory
        if (status) binType.status = status
        if (version) binType.version = version
        if (reason) binType.reason = reason

        // Save updated bin type
        const updatedBinType = await binType.save()

        res.status(200).json({
            success: true,
            data: updatedBinType,
        })
    } catch (error) {
        res.status(500).json({
            success: false,
            message: "Server Error",
            error: error.message,
        })
    }
})

// PATCH update bin type status
router.patch("/:id/status", validateObjectId, async (req, res) => {
    try {
        const {status, reason} = req.body

        if (!status) {
            return res.status(400).json({
                success: false,
                message: "Status is required",
            })
        }

        // Validate status value
        const validStatuses = ["active", "inactive", "pending", "modify", "decline"]
        if (!validStatuses.includes(status)) {
            return res.status(400).json({
                success: false,
                message: `Status must be one of: ${validStatuses.join(", ")}`,
            })
        }

        const binType = await BinType.findOne({
            _id: req.params.id,
            deleted_at: null,
        })

        if (!binType) {
            return res.status(404).json({
                success: false,
                message: "Bin type not found",
            })
        }

        binType.status = status
        if (reason) binType.reason = reason

        const updatedBinType = await binType.save()

        res.status(200).json({
            success: true,
            data: updatedBinType,
        })
    } catch (error) {
        res.status(500).json({
            success: false,
            message: "Server Error",
            error: error.message,
        })
    }
})

// DELETE a bin type (soft delete)
router.delete("/:id", validateObjectId, async (req, res) => {
    try {
        const binType = await BinType.findOne({
            _id: req.params.id,
            deleted_at: null,
        })

        if (!binType) {
            return res.status(404).json({
                success: false,
                message: "Bin type not found",
            })
        }

        // Soft delete by setting deleted_at
        binType.deleted_at = new Date()
        await binType.save()

        res.status(200).json({
            success: true,
            message: "Bin type deleted successfully",
        })
    } catch (error) {
        res.status(500).json({
            success: false,
            message: "Server Error",
            error: error.message,
        })
    }
})

// GET bin types by programme type
router.get("/programme/:programmeTypeId", async (req, res) => {
    try {
        if (!isValidObjectId(req.params.programmeTypeId)) {
            return res.status(400).json({
                success: false,
                message: "Invalid programme type ID format",
            })
        }

        const binTypes = await BinType.find({
            programmeType: req.params.programmeTypeId,
            deleted_at: null,
        })
            .populate("programmeType")
            .populate("binVariant")
            .populate("binCategory")
            .populate("created_by", "name email")

        res.status(200).json({
            success: true,
            count: binTypes.length,
            data: binTypes,
        })
    } catch (error) {
        res.status(500).json({
            success: false,
            message: "Server Error",
            error: error.message,
        })
    }
})


router.post("/approve", async (req, res) => {
    try {
        const {entityId} = req.body;

        // Perform approval logic, e.g., update the database
        if (!entityId) {
            return res.status(400).json({message: "record ID is required."});
        }
        const record = await BinType.findById(entityId);
        if (!record) {
            return res.status(404).json({message: "record not found."});
        }
        // Parse and increment the version number
        let currentVersion = parseFloat(record.version) || 0.0; // Default to 0.0 if version is not set
        let updatedVersion = (currentVersion + 0.1).toFixed(1); // Increment and format to one decimal place

        // Update the record status and version
        const updatedrecord = await BinType.findByIdAndUpdate(
            entityId,
            {status: "active", version: updatedVersion},
            {new: true} // Return the updated document
        );

        console.log(`Approving record with ID: ${entityId}`);

        res.status(200).json({message: "record approved successfully."});
    } catch (error) {
        console.error("Error approving record:", error);
        res.status(500).json({message: "Internal server error."});
    }
});


router.post("/decline", async (req, res) => {
    try {
        const {entityId, reason} = req.body;

        if (!entityId || !reason) {
            return res.status(400).json({message: "record ID and reason are required."});
        }
        const record = await BinType.findById(entityId);
        if (!record) {
            return res.status(404).json({message: "record not found."});
        }
        // Parse and increment the version number
        let currentVersion = parseFloat(record.version) || 0.0; // Default to 0.0 if version is not set
        let updatedVersion = (currentVersion + 0.1).toFixed(1); // Increment and format to one decimal place

        // Update the record status and version
        const updatedrecord = await BinType.findByIdAndUpdate(
            entityId,
            {status: "decline", reason: reason, version: updatedVersion},
            {new: true} // Return the updated document
        );
        // Perform decline logic (e.g., update the database)
        console.log(`Declining record with ID: ${entityId} for reason: ${reason}`);

        res.status(200).json({message: "record declined successfully."});
    } catch (error) {
        console.error("Error declining record:", error);
        res.status(500).json({message: "Internal server error."});
    }
});
router.post("/modify", async (req, res) => {
    try {
        const {entityId, instructions} = req.body;

        if (!entityId || !instructions) {
            return res.status(400).json({message: "record ID and modification instructions are required."});
        }
        const record = await BinType.findById(entityId);
        if (!record) {
            return res.status(404).json({message: "record not found."});
        }
        // Parse and increment the version number
        let currentVersion = parseFloat(record.version) || 0.0; // Default to 0.0 if version is not set
        let updatedVersion = (currentVersion + 0.2).toFixed(1); // Increment and format to one decimal place

        // Update the record status and version
        const updatedrecord = await BinType.findByIdAndUpdate(
            entityId,
            {status: "modify", reason: instructions, version: updatedVersion},
            {new: true} // Return the updated document
        );
        // Perform modify logic (e.g., update the database)
        console.log(`Modifying record with ID: ${entityId} with instructions: ${instructions}`);

        res.status(200).json({message: "record modification request submitted successfully."});
    } catch (error) {
        console.error("Error modifying record:", error);
        res.status(500).json({message: "Internal server error."});
    }
});


module.exports = router

