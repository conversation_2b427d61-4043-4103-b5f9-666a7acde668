// routes/countries.js

const express = require("express")
const router = express.Router()
const Country = require("../models/Countries")
const {createTask} = require("../config/EventHandler");
const User = require("../models/user")


//Route to get active countries only
router.get("/", async (req, res) => {
    try {
        // Fetch only countries where deleted_at is null
        const activeCountries = await Country.find({deleted_at: null}).sort({created_at: -1});
        res.json(activeCountries)
    } catch (error) {
        console.error("Error fetching countries:", error)
        res.status(500).json({message: "Failed to fetch countries"})
    }
})


// Route to create a country
router.post("/", async (req, res) => {
    const {country_name, country_code, currency_code, created_by} = req.body

    try {
        const recordCount = await Country.countDocuments();
        const x = recordCount + 1
        // Calculate the new version number
        const version = x + `.0`;
        const newCountry = new Country({
            country_name, country_code, currency_code, version, created_by, // Save created_by
        })

        const savedCountry = await newCountry.save()
        const user = await User.findById(created_by);
        const ip =
            req.headers["x-forwarded-for"]?.split(",").shift() ||
            req.socket?.remoteAddress;
        const taskData = {
            refId: savedCountry._id,
            type: 'Country',
            title: user.name + ' requested a new Country "' +country_name + '"',
            date: new Date(),
            user: created_by,
            ipAddress: ip,
        };

        await createTask(taskData)
        res.status(201).json(savedCountry)
    } catch (error) {
        console.error("Error creating country:", error)
        res.status(500).json({message: "Failed to create country"})
    }
})

// Route to delete a country (soft delete)
router.delete("/:id", async (req, res) => {
    const {id} = req.params

    try {
        const updatedCountry = await Country.findByIdAndUpdate(id, {deleted_at: new Date()}, // Set deleted_at field
            {new: true})

        if (!updatedCountry) {
            return res.status(404).json({message: "Country not found"})
        }

        res.json(updatedCountry)
    } catch (error) {
        console.error("Error deleting country:", error)
        res.status(500).json({message: "Failed to delete country"})
    }
})


// PATCH request to update country status
router.patch("/:id/status", async (req, res) => {
    const {id} = req.params;
    const {is_active} = req.body;

    try {
        const updatedCountry = await Country.findByIdAndUpdate(id, {is_active}, {new: true} // Return the updated document
        );

        if (!updatedCountry) {
            return res.status(404).json({message: "Country not found"});
        }

        res.status(200).json({message: "Country status updated", country: updatedCountry});
    } catch (error) {
        console.error("Error updating country status:", error);
        res.status(500).json({message: "Internal server error"});
    }
});
router.post("/approve", async (req, res) => {
    try {
        const {entityId} = req.body;

        // Perform approval logic, e.g., update the database
        if (!entityId) {
            return res.status(400).json({message: "record ID is required."});
        }
        const record = await Country.findById(entityId);
        if (!record) {
            return res.status(404).json({message: "record not found."});
        }
        // Parse and increment the version number
        let currentVersion = parseFloat(record.version) || 0.0; // Default to 0.0 if version is not set
        let updatedVersion = (currentVersion + 0.1).toFixed(1); // Increment and format to one decimal place

        // Update the record status and version
        const updatedrecord = await Country.findByIdAndUpdate(
            entityId,
            {status: "active", version: updatedVersion},
            {new: true} // Return the updated document
        );

        console.log(`Approving record with ID: ${entityId}`);

        res.status(200).json({message: "record approved successfully."});
    } catch (error) {
        console.error("Error approving record:", error);
        res.status(500).json({message: "Internal server error."});
    }
});
router.post("/decline", async (req, res) => {
    try {
        const {entityId, reason} = req.body;

        if (!entityId || !reason) {
            return res.status(400).json({message: "record ID and reason are required."});
        }
        const record = await Country.findById(entityId);
        if (!record) {
            return res.status(404).json({message: "record not found."});
        }
        // Parse and increment the version number
        let currentVersion = parseFloat(record.version) || 0.0; // Default to 0.0 if version is not set
        let updatedVersion = (currentVersion + 0.1).toFixed(1); // Increment and format to one decimal place

        // Update the record status and version
        const updatedrecord = await Country.findByIdAndUpdate(
            entityId,
            {status: "decline", reason: reason, version: updatedVersion},
            {new: true} // Return the updated document
        );
        // Perform decline logic (e.g., update the database)
        console.log(`Declining record with ID: ${entityId} for reason: ${reason}`);

        res.status(200).json({message: "record declined successfully."});
    } catch (error) {
        console.error("Error declining record:", error);
        res.status(500).json({message: "Internal server error."});
    }
});
router.post("/modify", async (req, res) => {
    try {
        const {entityId, instructions} = req.body;

        if (!entityId || !instructions) {
            return res.status(400).json({message: "record ID and modification instructions are required."});
        }
        const record = await Country.findById(entityId);
        if (!record) {
            return res.status(404).json({message: "record not found."});
        }
        // Parse and increment the version number
        let currentVersion = parseFloat(record.version) || 0.0; // Default to 0.0 if version is not set
        let updatedVersion = (currentVersion + 0.2).toFixed(1); // Increment and format to one decimal place

        // Update the record status and version
        const updatedrecord = await Country.findByIdAndUpdate(
            entityId,
            {status: "modify", reason: instructions, version: updatedVersion},
            {new: true} // Return the updated document
        );
        // Perform modify logic (e.g., update the database)
        console.log(`Modifying record with ID: ${entityId} with instructions: ${instructions}`);

        res.status(200).json({message: "record modification request submitted successfully."});
    } catch (error) {
        console.error("Error modifying record:", error);
        res.status(500).json({message: "Internal server error."});
    }
});

module.exports = router
