
const express = require("express")
const router = express.Router()
const User = require("../models/user")
const bcrypt = require("bcrypt")
const jwt = require("jsonwebtoken")
const speakeasy = require("speakeasy")

// Login route
router.post("/login", async (req, res) => {
    try {
        const { email, password } = req.body;

        if (!email || !password) {
            return res.status(400).json({
                success: false,
                message: "Email and password are required",
            });
        }

        const user = await User.findOne({ email });

        if (!user) {
            return res.status(401).json({
                success: false,
                message: "Invalid email or password",
            });
        }

        const isMatch = await bcrypt.compare(password, user.password);

        if (!isMatch) {
            return res.status(401).json({
                success: false,
                message: "Invalid email or password",
            });
        }

        // Get IP address from request
        const ip =
            req.headers["x-forwarded-for"]?.split(",").shift() ||
            req.socket?.remoteAddress;

        // If 2FA is enabled
        if (user.twoFactorEnabled) {
            const tempToken = jwt.sign(
                {
                    id: user._id,
                    email: user.email,
                    requires2FA: true,
                },
                process.env.JWT_SECRET,
                { expiresIn: "5m" }
            );

            res.cookie("token", tempToken, {
                httpOnly: true,
                secure: process.env.NODE_ENV === "production",
                sameSite: "strict",
                maxAge: 5 * 60 * 1000,
            });

            return res.status(200).json({
                success: true,
                twoFactorEnabled: true,
                tempToken,
                message: "Please verify with 2FA",
            });
        }

        const token = jwt.sign(
            {
                id: user._id,
                email: user.email,
                dashboard: user.dashboard,
            },
            process.env.JWT_SECRET,
            { expiresIn: "1d" }
        );

        res.cookie("token", token, {
            httpOnly: true,
            secure: process.env.NODE_ENV === "production",
            sameSite: "strict",
            maxAge: 24 * 60 * 60 * 1000,
        });

        // 🔥 Update last login time and IP
        user.lastLoginAt = new Date();
        user.lastLoginIP = ip;
        await user.save();
        return res.status(200).json({
            success: true,
            token,
            user: {
                id: user._id,
                company: user.dashboard === "api" ? user.recordId: "N/A",
                email: user.email,
                name: user.name,
                dashboard: user.dashboard,
            },
        });
    } catch (error) {
        console.error("Login error:", error);
        return res.status(500).json({
            success: false,
            message: "Server error",
        });
    }
});

// Verify 2FA code during login
router.post("/verify-login-2fa", async (req, res) => {
    try {
        const { email, verificationCode, tempToken } = req.body

        // Validate input
        if (!email || !verificationCode || !tempToken) {
            return res.status(400).json({
                success: false,
                message: "Email, verification code, and token are required",
            })
        }

        // Verify the temporary token
        let decoded
        try {
            decoded = jwt.verify(tempToken, process.env.JWT_SECRET)
        } catch (err) {
            return res.status(401).json({
                success: false,
                message: "Invalid or expired token. Please login again.",
            })
        }

        // Check if token is for the correct user and has the requires2FA flag
        if (decoded.email !== email || !decoded.requires2FA) {
            return res.status(401).json({
                success: false,
                message: "Invalid token. Please login again.",
            })
        }

        // Find user
        const user = await User.findOne({ email })

        if (!user || !user.twoFactorSecret) {
            return res.status(401).json({
                success: false,
                message: "User not found or 2FA not set up",
            })
        }

        // Verify the 2FA code
        const verified = speakeasy.totp.verify({
            secret: user.twoFactorSecret,
            encoding: "base32",
            token: verificationCode,
            window: 1, // Allow 1 period before and after for clock drift
        })

        if (!verified) {
            return res.status(401).json({
                success: false,
                message: "Invalid verification code",
            })
        }

        // Generate a full access token
        const token = jwt.sign(
            {
                id: user._id,
                email: user.email,
                dashboard: user.dashboard,
            },
            process.env.JWT_SECRET,
            { expiresIn: "1d" },
        )

        // Set the token as an HTTP-only cookie
        res.cookie("token", token, {
            httpOnly: true,
            secure: process.env.NODE_ENV === "production",
            sameSite: "strict",
            maxAge: 24 * 60 * 60 * 1000, // 1 day
        })

        // Return user data and token
        return res.status(200).json({
            success: true,
            token,
            user: {
                id: user._id,
                email: user.email,
                name: user.name,
                dashboard: user.dashboard,
            },
        })
    } catch (error) {
        console.error("2FA verification error:", error)
        return res.status(500).json({
            success: false,
            message: "Server error",
        })
    }
})

module.exports = router
