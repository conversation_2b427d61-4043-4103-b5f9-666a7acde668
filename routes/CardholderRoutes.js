const express = require("express")
const router = express.Router()
const IndividualOnboarding = require("../models/IndividualOnboarding")



const {body, validationResult} = require("express-validator");



router.post(
    '/:id/updatePermissions',
    [
        body('permissions').isArray().withMessage('Permissions must be an array'),
        body('permissionAudit').isArray().withMessage('Permission audit must be an array'),
    ],
    async (req, res) => {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({ errors: errors.array() });
        }

        const { id } = req.params;
        const { permissions, permissionAudit } = req.body;

        try {
            const currentCompany = await IndividualOnboarding.findById(id);
            if (!currentCompany) {
                return res.status(404).json({ success: false, message: 'Account not found' });
            }

            // Old permissions
            const oldPermissions = currentCompany.permissions || [];

            // Track changes
            const addedPermissions = permissions.filter(p => !oldPermissions.includes(p));
            const removedPermissions = oldPermissions.filter(p => !permissions.includes(p));

            let changeDetails = [];
            if (addedPermissions.length) changeDetails.push(`Added: ${addedPermissions.join(", ")}`);
            if (removedPermissions.length) changeDetails.push(`Removed: ${removedPermissions.join(", ")}`);
            if (!addedPermissions.length && !removedPermissions.length) changeDetails.push("No changes in permissions");

            // Update permissions
            currentCompany.permissions = permissions;
            currentCompany.permissionAudit = permissionAudit;

            // Log permission update (from logged-in user info)
            currentCompany.permissionsLog.push({
                action: "Permission Update",
                details: changeDetails.join(" | "),
                username: req.user?.name || "Unknown User",
                email: req.user?.email || "Unknown Email"
            });

            await currentCompany.save();

            return res.status(200).json({
                success: true,
                message: 'Permissions updated successfully',
                changes: changeDetails
            });

        } catch (error) {
            console.error(error);
            res.status(400).json({ success: false, message: error.message || 'An error occurred' });
        }
    }
);


module.exports = router