const express = require("express")
const router = express.Router()
const Log = require("../models/Log")

/**
 * @route GET /api/logs
 * @desc Get logs with pagination and filtering
 */
router.get("/", async (req, res) => {
    try {
        // Get query parameters
        const page = Number.parseInt(req.query.page) || 1
        const limit = Number.parseInt(req.query.limit) || 10
        const method = req.query.method
        const status = req.query.status
        const search = req.query.search
        const from = req.query.from
        const to = req.query.to

        // Build filter query
        const filter = {
            // Exclude logs with URL /api/users/me and OPTIONS method
            $and: [
                { url: { $ne: "/api/users/me" } },
                { method: { $ne: "OPTIONS" } },
                { url: { $not: /^\/api\/logs/ } }, // Exclude URLs starting with /api/logs
            ],
        }

        // Method filter
        if (method && method !== "all") {
            filter.method = method
        }

        // Status filter
        if (status && status !== "all") {
            // Handle status ranges like 2xx, 3xx, etc.
            if (status.endsWith("xx")) {
                const statusPrefix = status.charAt(0)
                filter.status = {
                    $gte: Number.parseInt(statusPrefix + "00"),
                    $lt: Number.parseInt(statusPrefix + "99") + 1,
                }
            } else {
                filter.status = Number.parseInt(status)
            }
        }

        // Date range filter
        if (from || to) {
            filter.timestamp = {}
            if (from) filter.timestamp.$gte = new Date(from)
            if (to) filter.timestamp.$lte = new Date(to)
        }

        // Search filter (across multiple fields)
        if (search) {
            filter.$or = [
                { url: { $regex: search, $options: "i" } },
                { remoteAddr: { $regex: search, $options: "i" } },
                { userIdentity: { $regex: search, $options: "i" } },
                { referer: { $regex: search, $options: "i" } },
                { userAgent: { $regex: search, $options: "i" } },
            ]
        }

        // Calculate pagination
        const skip = (page - 1) * limit

        // Execute query with pagination
        const logs = await Log.find(filter)
            .sort({ timestamp: -1 }) // Most recent first
            .skip(skip)
            .limit(limit)
            .lean()

        // Get total count for pagination
        const total = await Log.countDocuments(filter)
        const totalPages = Math.ceil(total / limit)

        res.json({
            success: true,
            logs,
            total,
            totalPages,
            page,
            limit,
        })
    } catch (error) {
        console.error("Error fetching logs:", error)
        res.status(500).json({ success: false, message: "Failed to fetch logs" })
    }
})

/**
 * @route GET /api/logs/export
 * @desc Export logs as CSV
 */
router.get("/export", async (req, res) => {
    try {
        // Get query parameters for filtering
        const method = req.query.method
        const status = req.query.status
        const search = req.query.search
        const from = req.query.from
        const to = req.query.to

        // Build filter query
        const filter = {
            // Exclude logs with URL /api/users/me, OPTIONS method, and URLs starting with /api/logs
            $and: [
                { url: { $ne: "/api/users/me" } },
                { method: { $ne: "OPTIONS" } },
                { url: { $not: /^\/api\/logs/ } }, // Exclude URLs starting with /api/logs
            ],
        }

        // Method filter
        if (method && method !== "all") {
            filter.method = method
        }

        // Status filter
        if (status && status !== "all") {
            // Handle status ranges like 2xx, 3xx, etc.
            if (status.endsWith("xx")) {
                const statusPrefix = status.charAt(0)
                filter.status = {
                    $gte: Number.parseInt(statusPrefix + "00"),
                    $lt: Number.parseInt(statusPrefix + "99") + 1,
                }
            } else {
                filter.status = Number.parseInt(status)
            }
        }

        // Date range filter
        if (from || to) {
            filter.timestamp = {}
            if (from) filter.timestamp.$gte = new Date(from)
            if (to) filter.timestamp.$lte = new Date(to)
        }

        // Search filter (across multiple fields)
        if (search) {
            filter.$or = [
                { url: { $regex: search, $options: "i" } },
                { remoteAddr: { $regex: search, $options: "i" } },
                { userIdentity: { $regex: search, $options: "i" } },
                { referer: { $regex: search, $options: "i" } },
                { userAgent: { $regex: search, $options: "i" } },
            ]
        }

        // Get all logs matching the filter
        const logs = await Log.find(filter).sort({ timestamp: -1 }).lean()

        // Convert to CSV
        const fields = [
            "timestamp",
            "method",
            "url",
            "status",
            "remoteAddr",
            "userIdentity",
            "responseTime",
            "responseSize",
            "referer",
        ]

        // Create CSV header
        let csv = fields.join(",") + "\n"

        // Add data rows
        logs.forEach((log) => {
            const row = fields
                .map((field) => {
                    let value = log[field]

                    // Format timestamp
                    if (field === "timestamp") {
                        value = new Date(value).toISOString()
                    }

                    // Escape commas and quotes
                    if (typeof value === "string" && (value.includes(",") || value.includes('"'))) {
                        value = `"${value.replace(/"/g, '""')}"`
                    }

                    return value
                })
                .join(",")

            csv += row + "\n"
        })

        // Set headers for CSV download
        res.setHeader("Content-Type", "text/csv")
        res.setHeader(
            "Content-Disposition",
            `attachment; filename="logs-export-${new Date().toISOString().split("T")[0]}.csv"`,
        )

        // Send CSV data
        res.send(csv)
    } catch (error) {
        console.error("Error exporting logs:", error)
        res.status(500).json({ success: false, message: "Failed to export logs" })
    }
})

/**
 * @route GET /api/logs/:id
 * @desc Get a single log by ID
 */
router.get("/:id", async (req, res) => {
    try {
        const log = await Log.findById(req.params.id).lean()

        if (!log) {
            return res.status(404).json({ success: false, message: "Log not found" })
        }

        // Don't return logs with URL /api/users/me
        if (log.url === "/api/users/me") {
            return res.status(403).json({ success: false, message: "Access to this log is restricted" })
        }

        // Don't return logs with method OPTIONS
        if (log.method === "OPTIONS") {
            return res.status(403).json({ success: false, message: "Access to this log is restricted" })
        }

        // Don't return logs where URL starts with /api/logs
        if (log.url.startsWith("/api/logs")) {
            return res.status(403).json({ success: false, message: "Access to this log is restricted" })
        }

        res.json({ success: true, log })
    } catch (error) {
        console.error("Error fetching log:", error)
        res.status(500).json({ success: false, message: "Failed to fetch log" })
    }
})

module.exports = router
