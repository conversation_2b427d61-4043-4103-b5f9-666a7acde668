const express = require('express');
const router = express.Router();
const Company = require('../models/company');
const CardProgram = require('../models/CardProgram');
const IndividualOnboarding = require('../models/IndividualOnboarding');
const { registerUser } = require("../controllers/userController");
const CompanyContact = require("../models/Contact");
const B2BAccount = require("../models/B2BAccount");
const Account = require("../models/Account");
const {Types} = require("mongoose");

// Generate ryvyl_id in format RYVL-xxxxxxxx
const generateRyvylId = () => {
    const randomNumber = Math.floor(******** + Math.random() * ********); // 8-digit number
    return `RYVL-${randomNumber}`;
};

// Ensure ryvyl_id is unique
const generateUniqueRyvylId = async () => {
    let id;
    let exists = true;

    while (exists) {
        id = generateRyvylId();
        const existing = await Company.findOne({ ryvyl_id: id });
        if (!existing) exists = false;
    }

    return id;
};

// POST route to save company information
router.post('/save', async (req, res) => {
    try {
        const ryvyl_id = await generateUniqueRyvylId();
        const companyData = { ...req.body, ryvyl_id };

        const company = new Company(companyData);
        company.dashboardStatus ="active";

        // const response = await registerUser(company.company_name, company.company_email, [], "active", "programmeManager", company._id);


        await user.save();
        await company.save();
        res.status(201).json({ success: true, data: company });
    } catch (err) {
        res.status(400).json({ success: false, message: err.message });
    }
});

// GET route to fetch all company data B2BAccount.js
router.get('/', async (req, res) => {
    try {
        const companies = await Company.find().sort({ created_at: -1 });
        const cardPrograms = await CardProgram.find().sort({ created_at: -1 });
        const b2bList = await B2BAccount.find();

        // Get all cardProgram IDs
        const cardProgramIds = cardPrograms.map(program => program._id.toString());

        // Filter companies not in cardPrograms
        const uniqueFromArray1 = companies.filter(item => !cardProgramIds.includes(item._id.toString()));

        // Attach B2B data to each company
        const companiesWithB2B = uniqueFromArray1.map(company => {
            const b2bForCompany = b2bList.filter(b2b => b2b.parentCompany.toString() === company._id.toString());
            return {
                ...company._doc,
                b2bList: b2bForCompany
            };
        });

        res.status(200).json({
            success: true,
            data: companiesWithB2B,
            programme: cardPrograms
        });
    } catch (err) {
        res.status(500).json({
            success: false,
            message: 'Failed to fetch company data',
            error: err.message
        });
    }
});



// GET a company by ID and add ryvyl_id if missing
router.get('/:id', async (req, res) => {
    const { id } = req.params;

    try {
        const user = await Company.findById(id);
        const contacts = await CompanyContact.find({ company: id }).sort({ created_at: -1 });;

        if (!user) {
            return res.status(404).json({ message: 'Company not found' });
        }

        // Assign ryvyl_id if not present
        if (!user.ryvyl_id) {
            user.ryvyl_id = await generateUniqueRyvylId();
            await user.save();
        }

        const cardProgram = await CardProgram.find({ company: id }).sort({ created_at: -1 })
            .populate('company')
            .populate('cardScheme')
            .populate('programmeType')
            .populate('binType')
            .populate('binRangeId')
            .populate('created_by')
            .populate('programManagerType')
            .populate('productVersionName');

        const companyAccount = await Account.find({
            company:id
        });
        console.log(companyAccount);

        res.json({ company: user, cip: cardProgram, contacts:contacts,companyAccount });
    } catch (error) {
        res.status(500).json({ message: 'Error retrieving Company', error });
    }
});

router.get('/:ryvylID', async (req, res) => {
    const { ryvylID } = req.params;

    try {
        const user = await Company.findOne({ryvyl_id: ryvylID});

        if (!user) {
            return res.status(404).json({ message: 'Company not found' });
        }



        const cardProgram = await CardProgram.find({ company: user._id }).sort({ created_at: -1 })
            .populate('company')
            .populate('created_by')
            .populate('programManagerType')
            .populate('productVersionName');

        const companyAccount = await Account.find({
            company:id
        });


        res.json({ company: user, cip: cardProgram, companyAccount });
    } catch (error) {
        res.status(500).json({ message: 'Error retrieving Company', error });
    }
});

router.get('/:id/bankingClients', async (req, res) => {
    const { id } = req.params;

    try {
        const bankingCustomers = await IndividualOnboarding.find({ company: id })
            .populate('cardCurrency')
            .sort({ createdAt: -1 });

        const b2bList = await B2BAccount.find({parentCompany: id});

       return res.json({ users: bankingCustomers, b2b: b2bList });
    } catch (error) {
        res.status(500).json({ message: 'Error retrieving Company', error });
    }
});

router.get('/:recordId/activate', async (req, res) => {
    try {
        const { recordId } = req.params;

        if (!recordId) {
            return res.status(400).json({ error: 'recordId is required' });
        }

        const updatedRecord = await Company.findByIdAndUpdate(
            recordId,
            { dashboardStatus: 'ACTIVE' },
            { new: true }
        );

        if (!updatedRecord) {
            return res.status(404).json({ error: 'Record not found' });
        }

        res.status(200).json({ message: 'Record activated successfully', data: updatedRecord });
    } catch (error) {
        res.status(500).json({ error: 'An error occurred while activating the record', details: error.message });
    }
});

const User = require('../models/user');
const {body, validationResult} = require("express-validator");
router.post('/disable', async (req, res) => {
    try {
        const { id, email } = req.body;

        const companyData = await Company.findOneAndUpdate(
            { _id: id },
            { status: "disabled" },
            { new: true }
        );

        const user = await User.findOneAndUpdate(
            { email: email },
            { status: "disabled" },
            { new: true }
        );

        return res.status(200).json({ success: true, user, companyData });
    } catch (error) {
        console.error("Disable Error:", error);
        return res.status(500).json({ success: false, message: "Internal Server Error", error });
    }
});

router.post('/enable', async (req, res) => {
    try {
        const { id, email } = req.body;

        const companyData = await Company.findOneAndUpdate(
            { _id: id },
            { status: "active" },
            { new: true }
        );

        const user = await User.findOneAndUpdate(
            { email: email },
            { status: "active" },
            { new: true }
        );

        return res.status(200).json({ success: true, user, companyData });
    } catch (error) {
        console.error("Disable Error:", error);
        return res.status(500).json({ success: false, message: "Internal Server Error", error });
    }
});


// PUT route to update company information
router.put('/:id', async (req, res) => {
    const { id } = req.params;
    const updateData = req.body;

    try {
        const company = await Company.findById(id);

        if (!company) {
            return res.status(404).json({ success: false, message: 'Company not found' });
        }

        // Update only the provided fields
        Object.keys(updateData).forEach(key => {
            company[key] = updateData[key];
        });

        await company.save();

        res.status(200).json({ success: true, message: 'Company updated successfully', data: company });
    } catch (error) {
        res.status(500).json({ success: false, message: 'Failed to update company', error: error.message });
    }
});
router.post('/:id/alert-settings', async (req, res) => {
    try {
        const { id } = req.params;
        const updateData = req.body;

        const updatedCompany = await Company.findByIdAndUpdate(id, updateData, {
            new: true,
            runValidators: true,
        });

        if (!updatedCompany) {
            return res.status(404).json({ success: false, message: 'Company not found' });
        }

        return res.status(200).json({
            success: true,
            message: 'Company updated successfully',
            data: updatedCompany,
        });
    } catch (error) {
        console.error('Error updating company:', error);
        return res.status(500).json({ success: false, message: 'Server error', error: error.message });
    }
});



router.post(
    '/:id/createUser',
    [
        body('name').trim().notEmpty().withMessage('Name is required'),
        body('email').trim().isEmail().withMessage('Email is invalid'),
        body('status').notEmpty().withMessage('Status is required'),
    ],
    async (req, res) => {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({ errors: errors.array() });
        }

        const { id } = req.params;
        const { name, email, roles, status, dashboard, recordId, company: companyId, permissions, permissionAudit } = req.body;

        try {
            const currentCompany = await Company.findById(id);
            if (!currentCompany) {
                return res.status(404).json({ success: false, message: 'Company not found' });
            }

            // Track permission changes
            const oldPermissions = currentCompany.permissions || [];
            const addedPermissions = permissions.filter(p => !oldPermissions.includes(p));
            const removedPermissions = oldPermissions.filter(p => !permissions.includes(p));

            let changeDetails = [];
            if (addedPermissions.length) changeDetails.push(`Added: ${addedPermissions.join(", ")}`);
            if (removedPermissions.length) changeDetails.push(`Removed: ${removedPermissions.join(", ")}`);
            if (!addedPermissions.length && !removedPermissions.length) changeDetails.push("No changes in permissions");

            // Update permissions
            currentCompany.permissions = permissions;
            currentCompany.permissionAudit = permissionAudit;

            // Check if user exists
            const existingUser = await User.findOne({ email });
            if (existingUser) {
                // Log for permission update
                currentCompany.permissionsLog.push({
                    action: "Permission Update",
                    details: changeDetails.join(" | "),
                    username: name,
                    email: email,
                    userId: existingUser._id
                });

                await currentCompany.save();

                return res.status(200).json({
                    success: true,
                    message: 'Permissions updated, but user email already exists',
                    user: existingUser
                });
            }

            // Create new user
            const newUser = await registerUser(name, email, roles, status, dashboard, recordId, companyId);

            // Log for new user creation
            currentCompany.permissionsLog.push({
                action: "User Created",
                details: changeDetails.join(" | "),
                username: name,
                email: email,
                userId: newUser._id
            });

            await currentCompany.save();

            res.status(201).json({
                success: true,
                message: 'User registered successfully',
                user: newUser
            });

        } catch (error) {
            console.error(error);
            res.status(400).json({ success: false, message: error.message || 'An error occurred' });
        }
    }
);


module.exports = router;
