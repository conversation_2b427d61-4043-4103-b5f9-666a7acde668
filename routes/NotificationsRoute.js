const express = require("express");
const router = express.Router();
const Company = require("../models/company");



const postmark = require("postmark");
router.post("/critical-stock-email", async (req, res) => {
    try {
        const {
            companyId,
            criticalProducts,
            threshold,
            timestamp
        } = req.body;



        for (const product of criticalProducts) {
            const {
                productName,
                versionCode,
                cardRange,
                remaining,
                utilizationRate,
                threshold: productThreshold
            } = product;

            const company = await Company.findById(companyId);
            // Send an email:
            var client = new postmark.ServerClient("************************************");

            client.sendEmailWithTemplate({
                From: "<EMAIL>",
                To: company.company_email,
                "TemplateAlias": "low-bin-alert",
                "TemplateModel": {
                    "company_name": company.company_name,
                    "threshold": threshold,
                    "productName": `${productName} (${versionCode})`,
                    "remaining": remaining,

                }
            });
        }

        res.status(200).json({message: "Data received successfully."});
    } catch (error) {
        console.error("Error processing critical stock email:", error);
        res.status(500).json({error: "Internal Server Error"});
    }
});



module.exports = router;