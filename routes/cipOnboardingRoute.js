const express = require('express');
const router = express.Router();
const onboardingController = require('../controllers/CipOnboardingController');
const {getCipOnboardingById} = require("../controllers/CipOnboardingController");

const Onboarding = require('../models/Account');

// GET route to retrieve all onboarding applications

router.get('/', async (req, res) => {
    try {
        const x = await Onboarding.find({});
        res.status(200).json(x);
    } catch (error) {
        console.error('Error retrieving onboarding:', error);
        res.status(500).json({ success: false, message: 'An error occurred while retrieving onboarding data' });
    }
})

// router.post('/', onboardingController.saveOnboardingData);
// GET route to retrieve a specific onboarding application by ID
router.get('/:id', getCipOnboardingById);
module.exports = router;
