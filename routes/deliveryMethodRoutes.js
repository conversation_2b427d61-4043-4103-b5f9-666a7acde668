const express = require('express');
const router = express.Router();
const DeliveryMethod = require('../models/DeliveryMethod');

// Create a new delivery method
router.post('/', async (req, res) => {
    try {
        const deliveryMethod = new DeliveryMethod(req.body);
        await deliveryMethod.save();
        res.status(201).json(deliveryMethod);
    } catch (error) {
        res.status(400).json({ error: error.message });
    }
});

// Get all delivery methods
router.get('/', async (req, res) => {
    try {
        const methods = await DeliveryMethod.find();
        res.json(methods);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// Get a single delivery method by ID
router.get('/:id', async (req, res) => {
    try {
        const method = await DeliveryMethod.findById(req.params.id);
        if (!method) return res.status(404).json({ message: 'Not found' });
        res.json(method);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// Update a delivery method
router.put('/:id', async (req, res) => {
    try {
        const method = await DeliveryMethod.findByIdAndUpdate(req.params.id, req.body, { new: true });
        if (!method) return res.status(404).json({ message: 'Not found' });
        res.json(method);
    } catch (error) {
        res.status(400).json({ error: error.message });
    }
});

// Delete a delivery method
router.delete('/:id', async (req, res) => {
    try {
        const method = await DeliveryMethod.findByIdAndDelete(req.params.id);
        if (!method) return res.status(404).json({ message: 'Not found' });
        res.json({ message: 'Deleted successfully' });
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

module.exports = router;
