const express = require("express");
const { sendPostRequest } = require("../config/ApiInstense");
const router = express.Router();

// Send client data (POST route)
router.post("/", async (req, res) => {
    const url = "https://apifintech.sandbox.itcardpaymentservice.pl:30080/v1/clients";

    const clientData = {
        companyName: "FIRM001",
        clientCode: "RYVL-FIRM001",
        phoneNumber: "+48777888999",
        authPhoneNumber: "+48777888999",
        email: "<EMAIL>",
        address: {
            street: "letnia",
            buildingNumber: "1",
            apartmentNumber: "2",
            city: "Gdynia",
            zipCode: "55-365",
            country: "POL"
        },
        nip: "9027423929",
        regon: "*********",
        embossedName: "FIRM001",
        company: true,
        customer: false
    };

    try {
        const result = await sendPostRequest(url, clientData);
        console.log(result);
        res.json(result);
    } catch (error) {
        console.error(error);
        res.status(500).json({ message: "Server error" });
    }
});

module.exports = router;
