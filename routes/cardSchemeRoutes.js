const express = require("express");
const router = express.Router();
const multer = require("multer");
const path = require("path");
const CardScheme = require("../models/CardScheme");

const {createCardScheme, getCardSchemes} = require("../controllers/cardSchemeController");
const {updateTaskByRecordId} = require("../config/EventHandler");

// Multer setup for handling file uploads
const storage = multer.diskStorage({
    destination: (req, file, cb) => {
        cb(null, "uploads/"); // Directory to store uploaded logos
    },
    filename: (req, file, cb) => {
        cb(null, Date.now() + path.extname(file.originalname)); // File name
    },
});

const upload = multer({storage});

// Route to create a new card scheme (with logo upload)
router.post("/schemes", upload.single("scheme_logo"), createCardScheme);

// Route to fetch all card schemes
router.get("/schemes", getCardSchemes);


// Delete user by ID
router.delete("/schemes/:id", async (req, res) => {
    const {id} = req.params;

    try {
        const deletedUser = await CardScheme.findByIdAndUpdate(
            id,
            {deleted_at: new Date()}, // Set deleted_at field
            {new: true}
        )

        if (!deletedUser) {
            return res.status(404).json({message: 'Record not found'});
        }

        res.json({message: 'User deleted successfully'});
    } catch (error) {
        res.status(500).json({message: 'Error deleting user', error});
    }
});
router.post("/approve-scheme", async (req, res) => {
    try {
        const {schemeId} = req.body;

        // Perform approval logic, e.g., update the database
        if (!schemeId) {
            return res.status(400).json({message: "Scheme ID is required."});
        }
        const scheme = await CardScheme.findById(schemeId);
        if (!scheme) {
            return res.status(404).json({message: "Scheme not found."});
        }
        // Parse and increment the version number
        let currentVersion = parseFloat(scheme.version) || 0.0; // Default to 0.0 if version is not set
        let updatedVersion = (currentVersion + 0.1).toFixed(1); // Increment and format to one decimal place

        // Update the scheme status and version
        const updatedScheme = await CardScheme.findByIdAndUpdate(
            schemeId,
            {status: "active", version: updatedVersion},
            {new: true} // Return the updated document
        );

        console.log(`Approving scheme with ID: ${schemeId}`);

        res.status(200).json({message: "Scheme approved successfully."});
    } catch (error) {
        console.error("Error approving scheme:", error);
        res.status(500).json({message: "Internal server error."});
    }
});


router.post("/decline-scheme", async (req, res) => {
    try {
        const {schemeId, reason} = req.body;

        if (!schemeId || !reason) {
            return res.status(400).json({message: "Scheme ID and reason are required."});
        }
        const scheme = await CardScheme.findById(schemeId);
        if (!scheme) {
            return res.status(404).json({message: "Scheme not found."});
        }
        // Parse and increment the version number
        let currentVersion = parseFloat(scheme.version) || 0.0; // Default to 0.0 if version is not set
        let updatedVersion = (currentVersion + 0.1).toFixed(1); // Increment and format to one decimal place

        // Update the scheme status and version
        const updatedScheme = await CardScheme.findByIdAndUpdate(
            schemeId,
            {status: "decline", reason: reason, version: updatedVersion},
            {new: true} // Return the updated document
        );
        // Perform decline logic (e.g., update the database)
        console.log(`Declining scheme with ID: ${schemeId} for reason: ${reason}`);

        res.status(200).json({message: "Scheme declined successfully."});
    } catch (error) {
        console.error("Error declining scheme:", error);
        res.status(500).json({message: "Internal server error."});
    }
});
router.post("/modify-scheme", async (req, res) => {
    try {
        const {schemeId, instructions} = req.body;

        if (!schemeId || !instructions) {
            return res.status(400).json({message: "Scheme ID and modification instructions are required."});
        }
        const scheme = await CardScheme.findById(schemeId);
        if (!scheme) {
            return res.status(404).json({message: "Scheme not found."});
        }
        // Parse and increment the version number
        let currentVersion = parseFloat(scheme.version) || 0.0; // Default to 0.0 if version is not set
        let updatedVersion = (currentVersion + 0.2).toFixed(1); // Increment and format to one decimal place

        // Update the scheme status and version
        const updatedScheme = await CardScheme.findByIdAndUpdate(
            schemeId,
            {status: "modify", reason: instructions, version: updatedVersion},
            {new: true} // Return the updated document
        );
        // Perform modify logic (e.g., update the database)
        console.log(`Modifying scheme with ID: ${schemeId} with instructions: ${instructions}`);

        res.status(200).json({message: "Scheme modification request submitted successfully."});
    } catch (error) {
        console.error("Error modifying scheme:", error);
        res.status(500).json({message: "Internal server error."});
    }
});

// Add this route after the existing routes and before module.exports = router;

router.put("/:id", upload.single("scheme_logo"), async (req, res) => {
    try {
        const { id } = req.params;
        const { scheme_name, scheme_description, scheme_type } = req.body;

        // Find the existing scheme
        let scheme = await CardScheme.findById(id);

        if (!scheme) {
            return res.status(404).json({ message: "Card scheme not found." });
        }

        // Update scheme details
        scheme.scheme_name = scheme_name || scheme.scheme_name;
        scheme.scheme_description = scheme_description || scheme.scheme_description;
        scheme.scheme_type = scheme_type || scheme.scheme_type;

        // Handle logo update if a new file is uploaded
        if (req.file) {
            scheme.scheme_logo = req.file.path;
        }

        // Increment version
        let currentVersion = parseFloat(scheme.version) || 0.0;
        scheme.version = (currentVersion + 0.1).toFixed(1);

        // Set status to 'pending' as the scheme has been modified
        scheme.status = "pending";

        // Save the updated scheme
        const updatedScheme = await scheme.save();
         await updateTaskByRecordId( updatedScheme._id  ,scheme_name );

        res.status(200).json({
            message: "Card scheme updated successfully.",
            data: updatedScheme
        });
    } catch (error) {
        console.error("Error updating card scheme:", error);
        res.status(500).json({ message: "Internal server error.", error: error.message });
    }
});



module.exports = router;
