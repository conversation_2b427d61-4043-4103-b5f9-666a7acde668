const express = require('express');
const axios = require('axios');
const {generateJwtToken, fetchBalance} = require("../config/LegecyService");


const router = express.Router();

router.get('/transactions', async (req, res) => {
    try {
        const { accountIdentification, dateStart, dateEnd, page, limit,cardId } = req.query;

        let startDate = dateStart;
        let endDate = dateEnd;

        if (!dateStart || !dateEnd) {
            const today = new Date();
            const lastMonth = new Date();
            lastMonth.setMonth(today.getMonth() - 1);

            const formatDate = (date) => date.toISOString().split('T')[0];
            startDate = formatDate(lastMonth);
            endDate = formatDate(today);
        }

        const token = generateJwtToken();

        const response = await axios.get('https://card-auth-staging.ryvyl.eu/api/itcard/transactions', {
            headers: {
                Authorization: `Bear<PERSON> ${token}`
            },
            params: {
                accountIdentification,
                dateStart: startDate,
                cardId: cardId,
                dateEnd: endDate,
                page,
                limit
            }
        });

        res.json(response.data);
    } catch (error) {
        console.error('Error fetching transactions:', error.response?.data || error.message);
        res.status(500).json({ error: error.response?.data || error.message });
    }
});

router.get('/fetch-balance/:accountIdentification', async (req, res) => {
    try {
        const { accountIdentification } = req.params;
        const data = await fetchBalance(accountIdentification);
        res.json(data);
    } catch (error) {
        console.error('Error fetching balance:', error.message);
        res.status(500).json({ error: error.message });
    }
});




module.exports = router;
