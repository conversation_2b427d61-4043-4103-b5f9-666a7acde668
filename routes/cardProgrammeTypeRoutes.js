const express = require("express");
const router = express.Router();
const { createCardProgrammeType, getCardProgrammeTypes } = require("../controllers/cardProgrammeTypeController");
const CardProgrammeType = require("../models/CardProgrammeType");

// Route to create a new card programme type
router.post("/programme-types", createCardProgrammeType);

// Route to fetch all card programme types
router.get("/programme-types", getCardProgrammeTypes);
router.delete("/programme-types/:id", async (req, res) => {
    const { id } = req.params;
    console.log("Received ID:", id);  // Log the id to verify

    try {
        const deletedUser = await CardProgrammeType.findByIdAndUpdate(
            id,
            { deleted_at: new Date() },
            { new: true }
        );
        console.log("Deleted User:", deletedUser);  // Log result

        if (!deletedUser) {
            return res.status(404).json({ message: 'Record not found' });
        }

        res.json({ message: 'Card Programme deleted successfully' });
    } catch (error) {
        console.error("Error:", error);  // Log any error
        res.status(500).json({ message: 'Error deleting Record', error });
    }
});
router.post("/approve", async (req, res) => {
    try {
        const {entityId} = req.body;

        // Perform approval logic, e.g., update the database
        if (!entityId) {
            return res.status(400).json({message: "record ID is required."});
        }
        const record = await CardProgrammeType.findById(entityId);
        if (!record) {
            return res.status(404).json({message: "record not found."});
        }
        // Parse and increment the version number
        let currentVersion = parseFloat(record.version) || 0.0; // Default to 0.0 if version is not set
        let updatedVersion = (currentVersion + 0.1).toFixed(1); // Increment and format to one decimal place

        // Update the record status and version
        const updatedrecord = await CardProgrammeType.findByIdAndUpdate(
            entityId,
            {status: "active", version: updatedVersion},
            {new: true} // Return the updated document
        );

        console.log(`Approving record with ID: ${entityId}`);

        res.status(200).json({message: "record approved successfully."});
    } catch (error) {
        console.error("Error approving record:", error);
        res.status(500).json({message: "Internal server error."});
    }
});
router.post("/decline", async (req, res) => {
    try {
        const {entityId, reason} = req.body;

        if (!entityId || !reason) {
            return res.status(400).json({message: "record ID and reason are required."});
        }
        const record = await CardProgrammeType.findById(entityId);
        if (!record) {
            return res.status(404).json({message: "record not found."});
        }
        // Parse and increment the version number
        let currentVersion = parseFloat(record.version) || 0.0; // Default to 0.0 if version is not set
        let updatedVersion = (currentVersion + 0.1).toFixed(1); // Increment and format to one decimal place

        // Update the record status and version
        const updatedrecord = await CardProgrammeType.findByIdAndUpdate(
            entityId,
            {status: "decline", reason: reason, version: updatedVersion},
            {new: true} // Return the updated document
        );
        // Perform decline logic (e.g., update the database)
        console.log(`Declining record with ID: ${entityId} for reason: ${reason}`);

        res.status(200).json({message: "record declined successfully."});
    } catch (error) {
        console.error("Error declining record:", error);
        res.status(500).json({message: "Internal server error."});
    }
});
router.post("/modify", async (req, res) => {
    try {
        const {entityId, instructions} = req.body;

        if (!entityId || !instructions) {
            return res.status(400).json({message: "record ID and modification instructions are required."});
        }
        const record = await CardProgrammeType.findById(entityId);
        if (!record) {
            return res.status(404).json({message: "record not found."});
        }
        // Parse and increment the version number
        let currentVersion = parseFloat(record.version) || 0.0; // Default to 0.0 if version is not set
        let updatedVersion = (currentVersion + 0.2).toFixed(1); // Increment and format to one decimal place

        // Update the record status and version
        const updatedrecord = await CardProgrammeType.findByIdAndUpdate(
            entityId,
            {status: "modify", reason: instructions, version: updatedVersion},
            {new: true} // Return the updated document
        );


        // Perform modify logic (e.g., update the database)
        console.log(`Modifying record with ID: ${entityId} with instructions: ${instructions}`);

        res.status(200).json({message: "record modification request submitted successfully."});
    } catch (error) {
        console.error("Error modifying record:", error);
        res.status(500).json({message: "Internal server error."});
    }
});
module.exports = router;
