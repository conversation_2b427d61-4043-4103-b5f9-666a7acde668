import { soap } from 'strong-soap';
import fs from 'fs/promises';
import http from 'http';

// Function to test if the SOAP server is accessible
async function testSoapServer(url = 'http://localhost:3000/wsdl') {
    console.log(`🔍 Testing SOAP server at ${url}`);

    try {
        // Test basic HTTP connectivity first
        const httpResponse = await new Promise((resolve, reject) => {
            http.get(url, (res) => {
                let data = '';
                res.on('data', (chunk) => data += chunk);
                res.on('end', () => resolve({ statusCode: res.statusCode, headers: res.headers, data }));
            }).on('error', reject);
        });

        console.log(`✅ HTTP connection successful (Status: ${httpResponse.statusCode})`);
        console.log('Response headers:', httpResponse.headers);

        // If we can access the WSDL, try to create a client and make a test call
        if (url.includes('?wsdl') || httpResponse.headers['content-type']?.includes('xml')) {
            console.log('🔄 Attempting to create SOAP client...');

            const client = await new Promise((resolve, reject) => {
                soap.createClient(url, (err, client) => {
                    if (err) reject(err);
                    else resolve(client);
                });
            });

            console.log('✅ SOAP client created successfully');
            console.log('📋 Available methods:');

            // List available methods
            const methods = Object.keys(client).filter(key =>
                typeof client[key] === 'function' && !key.startsWith('_')
            );

            if (methods.length === 0) {
                console.log('⚠️ No methods found in the SOAP service');
            } else {
                methods.forEach(method => console.log(`- ${method}`));
            }
        }
    } catch (error) {
        console.error('❌ Error testing SOAP server:', error.message);

        // Provide specific guidance based on error
        if (error.code === 'ECONNREFUSED') {
            console.log('\n🔧 TROUBLESHOOTING SUGGESTIONS:');
            console.log('1. Ensure your server is running');
            console.log('2. Check if the port is correct');
            console.log('3. Verify there are no firewall issues');
        } else if (error.message.includes('WSDL')) {
            console.log('\n🔧 TROUBLESHOOTING SUGGESTIONS:');
            console.log('1. Verify your WSDL file is valid XML');
            console.log('2. Check if the WSDL path is correct');
            console.log('3. Ensure service definitions match implementation');
        }
    }
}

// Function to check the service implementation
async function analyzeServiceImplementation(servicePath = './service.js') {
    console.log(`\n🔍 Analyzing service implementation at ${servicePath}`);

    try {
        // Try to read the service file
        const serviceContent = await fs.readFile(servicePath, 'utf8');

        // Check if the service exports methods
        const hasExports = serviceContent.includes('exports') || serviceContent.includes('module.exports');
        if (!hasExports) {
            console.log('⚠️ Service file does not appear to export any methods');
        }

        // Check for common patterns in SOAP service implementations
        const hasServiceObject = serviceContent.includes('service:') || serviceContent.includes('Service');
        const hasMethods = serviceContent.match(/function\s+\w+\s*\(/g);

        if (!hasServiceObject) {
            console.log('⚠️ Service file may not define a proper service object');
        }

        if (!hasMethods) {
            console.log('⚠️ No method implementations found in service file');
        }

        console.log('\n🔧 RECOMMENDATIONS:');
        console.log('1. Ensure your service.js exports an object with the correct structure');
        console.log('2. Verify method names match those defined in your WSDL');
        console.log('3. Check that your service methods return responses in the expected format');
        console.log('4. Add console.log statements in your service methods to verify they are being called');

        console.log('\n📝 Example of a properly structured service:');
        console.log(`
const myService = {
  MyServiceName: {
    MyServicePort: {
      MyMethod: function(args, callback) {
        console.log('MyMethod called with:', args);
        // Process the request
        callback({
          MyMethodResult: {
            result: 'success',
            data: 'some data'
          }
        });
      }
    }
  }
};

module.exports = myService;
`);

    } catch (error) {
        console.error(`❌ Could not analyze service file: ${error.message}`);
    }
}

// Run the diagnostics
async function runDiagnostics() {
    console.log('🔧 SOAP Server Diagnostics Tool 🔧');
    console.log('=================================\n');

    await testSoapServer('http://localhost:3000/wsdl?wsdl');
    await analyzeServiceImplementation();

    console.log('\n🔍 ADDITIONAL TROUBLESHOOTING STEPS:');
    console.log('1. Check your WSDL structure matches your service implementation');
    console.log('2. Verify client requests are properly formatted');
    console.log('3. Test with a simple SOAP client like SoapUI');
    console.log('4. Add more detailed logging in your service methods');
    console.log('5. Ensure callback functions are being called in your service methods');
}

runDiagnostics();