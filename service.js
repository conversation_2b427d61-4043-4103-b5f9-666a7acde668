require('dotenv').config();

const twilio = require('twilio');
const { getCard } = require("./utils/cardUtils");
const Card = require("./models/AccountCard");

const client = twilio(process.env.TWILIO_ACCOUNT_SID, process.env.TWILIO_AUTH_TOKEN);

// Simple in-memory store for OTPs
const otpStore = {};

function generateOTP() {
    return Math.floor(100000 + Math.random() * 900000).toString(); // 6-digit OTP
}

function isOTPExpired(entry) {
    return !entry || Date.now() > entry.expiresAt;
}

function maskCardNumber(cardNumber) {
    const firstSix = cardNumber.slice(0, 6);
    const lastFour = cardNumber.slice(-4);
    return firstSix + '******' + lastFour;
}

async function getCardKey(args) {
    const cardNumber = args.card?.number;
    if (!cardNumber) {
        console.log("No card number provided.");
        return null;
    }

    const masked = maskCardNumber(cardNumber);
    console.log("Original:", cardNumber, "Masked:", masked);

    const accountCard = await Card.findOne({ cardMask: masked });

    if (accountCard) {
        console.log("Card Key:", accountCard.cardKey);
        return accountCard.cardKey;
    } else {
        console.log("Card not found");
        return null;
    }
}

module.exports = {
    VerifyService: {
        VerifyPort: {
            async verifyReg(args) {
                try {
                    const cardNumber = args.card?.number;
                    const cardKey = await getCardKey(args);

                    let cardDetails = null;
                    if (cardKey) {
                        cardDetails = await getCard(cardKey);
                    }

                    if (!cardDetails) {
                        return {
                            code: 0,
                            errorMessage: 'Card not found',
                            errorDetail: 'No details found for given card'
                        };
                    }


                    return {
                        cardInfo: {
                            card_ID: cardNumber,
                            context_Blob: 'xyz123',
                            regStatus: cardDetails.status === "active" ? 1 : 0,
                            authRequired: 1,
                            authTypeSup: [1, 2],
                            lanCode: '',
                            twoFA: true
                        },
                        code: 1,
                        errorMessage: 'Card found',
                        errorDetail: 'Card info fetched successfully'
                    };

                } catch (err) {
                    console.error(err);
                    return {
                        code: -1,
                        errorMessage: 'Server Error',
                        errorDetail: err.message
                    };
                }
            },

            preAuth(args) {
                return { code: 1 };
            },

            async initAuth(args) {
                const phoneNumber = "+91 98848 60077";
                const otp = generateOTP();
                const expiresAt = Date.now() + 2 * 60 * 1000; // 2 minutes

                try {
                    await client.messages.create({
                        body: `Your verification code is: ${otp}`,
                        from: process.env.TWILIO_PHONE_NUMBER,
                        to: phoneNumber
                    });

                    otpStore[phoneNumber] = { otp, expiresAt };
                    console.log(`✅ OTP sent to ${phoneNumber}: ${otp}`);
                } catch (err) {
                    console.error('❌ Failed to send OTP:', err);
                    return {
                        code: -1,
                        errorMessage: 'Failed to send OTP',
                        errorDetail: err.message
                    };
                }

                return {
                    authData: {
                        data: {
                            name: 'mobileNo',
                            authType: '2',
                            value: phoneNumber
                        }
                    },
                    code: 0
                };
            },

            verifyAuth(args) {
                const phoneNumber = "+91 98848 60077";
                const userOtp = args?.token?.$value;

                const stored = otpStore[phoneNumber];

                if (!stored) {
                    return {
                        code: -1,
                        errorMessage: 'No OTP found for this number',
                        errorDetail: 'OTP might have expired or was never generated'
                    };
                }

                if (isOTPExpired(stored)) {
                    delete otpStore[phoneNumber];
                    return {
                        code: -1,
                        errorMessage: 'OTP expired',
                        errorDetail: 'The OTP is no longer valid'
                    };
                }

                if (stored.otp !== userOtp) {
                    return {
                        code: -1,
                        errorMessage: 'Invalid OTP',
                        errorDetail: 'The provided OTP does not match'
                    };
                }

                // Success: delete OTP after successful verification
                delete otpStore[phoneNumber];

                return {
                    code: 1,
                    errorMessage: 'OTP Verified',
                    errorDetail: 'User authenticated successfully'
                };
            }
        }
    }
};
