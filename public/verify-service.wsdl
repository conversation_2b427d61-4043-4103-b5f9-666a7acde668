<?xml version="1.0" encoding="UTF-8"?>
<definitions
        xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
        xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/"
        xmlns:xsd="http://www.w3.org/2001/XMLSchema"
        xmlns:tns="http://www.gpayments.com/caas/"
        name="VerifyService"
        targetNamespace="http://www.gpayments.com/caas/"
        xmlns="http://schemas.xmlsoap.org/wsdl/"
>
    <!-- === Types === -->
    <types>
        <xsd:schema targetNamespace="http://www.gpayments.com/caas/">
            <!-- Card Type -->
            <xsd:complexType name="CardType">
                <xsd:sequence>
                    <xsd:element name="id" type="xsd:string" minOccurs="0"/>
                    <xsd:element name="number" type="xsd:string"/>
                    <xsd:element name="type" type="xsd:string" minOccurs="0"/>
                    <xsd:element name="context_Blob" type="xsd:string" minOccurs="0"/>
                    <xsd:element name="lanCode" type="xsd:string" minOccurs="0"/>
                </xsd:sequence>
            </xsd:complexType>

            <!-- Transaction Type -->
            <xsd:complexType name="TransactionType">
                <xsd:sequence>
                    <xsd:element name="purchaseAmount" type="xsd:string"/>
                    <xsd:element name="purchaseExponent" type="xsd:string"/>
                    <xsd:element name="purchaseCurrency" type="xsd:string"/>
                    <xsd:element name="purchaseDate" type="xsd:dateTime"/>
                    <xsd:element name="merchantId" type="xsd:string"/>
                    <xsd:element name="merchantName" type="xsd:string"/>
                    <xsd:element name="merchantCountry" type="xsd:string"/>
                    <xsd:element name="acqBin" type="xsd:string"/>
                    <xsd:element name="theeDSProtocolVersion" type="xsd:string"/>
                    <xsd:element name="cardExpiry" type="xsd:string"/>
                    <xsd:element name="issuerName" type="xsd:string"/>
                    <xsd:element name="acsTransId" type="xsd:string"/>
                    <xsd:element name="threeDSTransId" type="xsd:string"/>
                    <xsd:element name="dsTransId" type="xsd:string"/>
                    <xsd:element name="threeDSRequestorID" type="xsd:string"/>
                    <xsd:element name="threeDSRequestorName" type="xsd:string"/>
                    <xsd:element name="threeDSServerRefNumber" type="xsd:string"/>
                    <xsd:element name="threeDSServerOperatorID" type="xsd:string"/>
                    <xsd:element name="threeDSRequestorURL" type="xsd:string"/>
                    <xsd:element name="threeDSServerURL" type="xsd:string"/>
                    <xsd:element name="deviceChannel" type="xsd:string"/>
                    <xsd:element name="dsReferenceNumber" type="xsd:string"/>
                    <xsd:element name="payTokenInd" type="xsd:string"/>
                    <xsd:element name="mcc" type="xsd:string"/>
                    <xsd:element name="messageCategory" type="xsd:string"/>
                    <xsd:element name="transType" type="xsd:string"/>
                    <xsd:element name="acctType" type="xsd:string"/>
                    <xsd:element name="threeDSRequestorAuthenticationInd" type="xsd:string"/>
                </xsd:sequence>
            </xsd:complexType>

            <!-- Parameter Type -->
            <xsd:complexType name="ParamType">
                <xsd:attribute name="key" type="xsd:string" use="required"/>
                <xsd:attribute name="cookie" type="xsd:string"/>
            </xsd:complexType>

            <!-- Parameters Type -->
            <xsd:complexType name="ParamsType">
                <xsd:sequence>
                    <xsd:element name="param" type="tns:ParamType" minOccurs="0" maxOccurs="unbounded"/>
                </xsd:sequence>
            </xsd:complexType>

            <!-- Auth Data Type -->
            <xsd:complexType name="AuthDataType">
                <xsd:sequence>
                    <xsd:element name="data" minOccurs="0">
                        <xsd:complexType>
                            <xsd:sequence>
                                <xsd:element name="value" type="xsd:string"/>
                            </xsd:sequence>
                            <xsd:attribute name="name" type="xsd:string"/>
                            <xsd:attribute name="authType" type="xsd:string"/>
                        </xsd:complexType>
                    </xsd:element>
                </xsd:sequence>
            </xsd:complexType>

            <!-- verifyRegReq -->
            <xsd:element name="verifyRegReq">
                <xsd:complexType>
                    <xsd:sequence>
                        <xsd:element name="card" type="tns:CardType"/>
                        <xsd:element name="transaction" type="tns:TransactionType"/>
                        <xsd:element name="headerParams" type="tns:ParamsType" minOccurs="0"/>
                        <xsd:element name="extensionParams" type="tns:ParamsType" minOccurs="0"/>
                        <xsd:element name="additionalParams" type="tns:ParamsType" minOccurs="0"/>
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>

            <!-- verifyRegResp -->
            <xsd:element name="verifyRegResp">
                <xsd:complexType>
                    <xsd:sequence>
                        <xsd:element name="cardInfo" minOccurs="0">
                            <xsd:complexType>
                                <xsd:sequence>
                                    <xsd:element name="card_ID" type="xsd:string"/>
                                    <xsd:element name="context_Blob" type="xsd:string"/>
                                    <xsd:element name="regStatus" type="xsd:int"/>
                                    <xsd:element name="authRequired" type="xsd:int"/>
                                    <xsd:element name="authTypeSup" type="xsd:int" maxOccurs="unbounded"/>
                                    <xsd:element name="lanCode" type="xsd:string"/>
                                    <xsd:element name="twoFA" type="xsd:boolean"/>
                                </xsd:sequence>
                            </xsd:complexType>
                        </xsd:element>
                        <xsd:element name="code" type="xsd:int"/>
                        <xsd:element name="errorMessage" type="xsd:string"/>
                        <xsd:element name="errorDetail" type="xsd:string"/>
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>

            <!-- preAuthReq -->
            <xsd:element name="preAuthReq">
                <xsd:complexType>
                    <xsd:sequence>
                        <xsd:element name="card" type="tns:CardType"/>
                        <xsd:element name="transaction" type="tns:TransactionType"/>
                        <xsd:element name="headerParams" type="tns:ParamsType" minOccurs="0"/>
                        <xsd:element name="additionalParams" type="tns:ParamsType" minOccurs="0"/>
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>

            <!-- preAuthResp -->
            <xsd:element name="preAuthResp">
                <xsd:complexType>
                    <xsd:sequence>
                        <xsd:element name="code" type="xsd:int"/>
                        <xsd:element name="errorMessage" type="xsd:string" minOccurs="0"/>
                        <xsd:element name="errorDetail" type="xsd:string" minOccurs="0"/>
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>

            <!-- initAuthReq -->
            <xsd:element name="initAuthReq">
                <xsd:complexType>
                    <xsd:sequence>
                        <xsd:element name="card" type="tns:CardType"/>
                        <xsd:element name="transaction" type="tns:TransactionType"/>
                        <xsd:element name="SMS" type="xsd:string" minOccurs="0"/>
                        <xsd:element name="authType" type="xsd:string" minOccurs="0"/>
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>

            <!-- initAuthResp -->
            <xsd:element name="initAuthResp">
                <xsd:complexType>
                    <xsd:sequence>
                        <xsd:element name="authData" type="tns:AuthDataType"/>
                        <xsd:element name="code" type="xsd:int"/>
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>

            <!-- verifyAuthReq -->
            <xsd:element name="verifyAuthReq">
                <xsd:complexType>
                    <xsd:sequence>
                        <xsd:element name="card" type="tns:CardType"/>
                        <xsd:element name="token">
                            <xsd:complexType>
                                <xsd:simpleContent>
                                    <xsd:extension base="xsd:string">
                                        <xsd:attribute name="authType" type="xsd:string"/>
                                    </xsd:extension>
                                </xsd:simpleContent>
                            </xsd:complexType>
                        </xsd:element>
                        <xsd:element name="transaction" type="tns:TransactionType"/>
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>

            <!-- verifyAuthResp -->
            <xsd:element name="verifyAuthResp">
                <xsd:complexType>
                    <xsd:sequence>
                        <xsd:element name="code" type="xsd:int"/>
                        <xsd:element name="errorMessage" type="xsd:string" minOccurs="0"/>
                        <xsd:element name="errorDetail" type="xsd:string" minOccurs="0"/>
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>
        </xsd:schema>
    </types>

    <!-- === Messages === -->
    <message name="VerifyRegRequest">
        <part name="verifyRegReq" element="tns:verifyRegReq"/>
    </message>
    <message name="VerifyRegResponse">
        <part name="verifyRegResp" element="tns:verifyRegResp"/>
    </message>

    <message name="PreAuthRequest">
        <part name="preAuthReq" element="tns:preAuthReq"/>
    </message>
    <message name="PreAuthResponse">
        <part name="preAuthResp" element="tns:preAuthResp"/>
    </message>

    <message name="InitAuthRequest">
        <part name="initAuthReq" element="tns:initAuthReq"/>
    </message>
    <message name="InitAuthResponse">
        <part name="initAuthResp" element="tns:initAuthResp"/>
    </message>

    <message name="VerifyAuthRequest">
        <part name="verifyAuthReq" element="tns:verifyAuthReq"/>
    </message>
    <message name="VerifyAuthResponse">
        <part name="verifyAuthResp" element="tns:verifyAuthResp"/>
    </message>

    <!-- === Port Type === -->
    <portType name="VerifyPortType">
        <operation name="verifyReg">
            <input message="tns:VerifyRegRequest"/>
            <output message="tns:VerifyRegResponse"/>
        </operation>
        <operation name="preAuth">
            <input message="tns:PreAuthRequest"/>
            <output message="tns:PreAuthResponse"/>
        </operation>
        <operation name="initAuth">
            <input message="tns:InitAuthRequest"/>
            <output message="tns:InitAuthResponse"/>
        </operation>
        <operation name="verifyAuth">
            <input message="tns:VerifyAuthRequest"/>
            <output message="tns:VerifyAuthResponse"/>
        </operation>
    </portType>

    <!-- === Binding (SOAP 1.2) === -->
    <binding name="VerifyBinding" type="tns:VerifyPortType">
        <soap12:binding style="document" transport="http://www.w3.org/2003/05/soap/bindings/HTTP/"/>

        <operation name="verifyReg">
            <soap12:operation soapAction="http://www.gpayments.com/caas/verifyReg"/>
            <input><soap12:body use="literal"/></input>
            <output><soap12:body use="literal"/></output>
        </operation>
        <operation name="preAuth">
            <soap12:operation soapAction="http://www.gpayments.com/caas/preAuth"/>
            <input><soap12:body use="literal"/></input>
            <output><soap12:body use="literal"/></output>
        </operation>
        <operation name="initAuth">
            <soap12:operation soapAction="http://www.gpayments.com/caas/initAuth"/>
            <input><soap12:body use="literal"/></input>
            <output><soap12:body use="literal"/></output>
        </operation>
        <operation name="verifyAuth">
            <soap12:operation soapAction="http://www.gpayments.com/caas/verifyAuth"/>
            <input><soap12:body use="literal"/></input>
            <output><soap12:body use="literal"/></output>
        </operation>
    </binding>

    <!-- === Service === -->
    <service name="VerifyService">
        <documentation>Ryvyl SOAP Verification Service</documentation>
        <port name="VerifyPort" binding="tns:VerifyBinding">
            <soap12:address location="http://localhost:3000/api/soap"/>
        </port>
    </service>
</definitions>