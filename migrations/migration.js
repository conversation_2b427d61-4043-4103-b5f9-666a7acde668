const db = require('../db');

// Function to create the users table
const createUserTable = () => {
    db.run(`
        CREATE TABLE IF NOT EXISTS users
        (
            id                INTEGER PRIMARY KEY AUTOINCREMENT,
            name              TEXT NOT NULL,
            email             TEXT NOT NULL UNIQUE,
            role              TEXT NOT NULL CHECK (role IN ('admin', 'salesman'))    DEFAULT 'salesman',
            email_verified_at TEXT,
            password          TEXT NOT NULL,
            status            TEXT NOT NULL CHECK (status IN ('active', 'inactive')) DEFAULT 'inactive',
            remember_token    TEXT,
            created_at        TEXT                                                   DEFAULT (datetime('now', 'localtime')),
            updated_at        TEXT,
            deleted_at        TEXT
        )
    `, err => {
        if (err) {
            console.error('Error creating users table:', err.message);
        } else {
            console.log('Users table created successfully.');
        }
    });
};

// Function to create the brands table
const createBrandTable = () => {
    db.run(`
        CREATE TABLE IF NOT EXISTS brands
        (
            id          INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id     INTEGER NOT NULL,
            name        TEXT    NOT NULL,
            description TEXT,
            created_at  TEXT DEFAULT (datetime('now', 'localtime')),
            updated_at  TEXT,
            deleted_at  TEXT,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
    `, err => {
        if (err) {
            console.error('Error creating brands table:', err.message);
        } else {
            console.log('Brands table created successfully.');
        }
    });
};

// Function to create the categories table
const createCategoryTable = () => {
    db.run(`
        CREATE TABLE IF NOT EXISTS categories
        (
            id          INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id     INTEGER NOT NULL,
            name        TEXT    NOT NULL,
            description TEXT,
            created_at  TEXT DEFAULT (datetime('now', 'localtime')),
            updated_at  TEXT,
            deleted_at  TEXT,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
    `, err => {
        if (err) {
            console.error('Error creating categories table:', err.message);
        } else {
            console.log('Categories table created successfully.');
        }
    });
};

// Function to create the customers table
const createCustomerTable = () => {
    db.run(`
        CREATE TABLE IF NOT EXISTS customers
        (
            id         INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id    INTEGER NOT NULL,
            name       TEXT    NOT NULL,
            email      TEXT,
            phone      TEXT,
            address    TEXT,
            created_at TEXT DEFAULT (datetime('now', 'localtime')),
            updated_at TEXT,
            deleted_at TEXT,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
    `, err => {
        if (err) {
            console.error('Error creating customers table:', err.message);
        } else {
            console.log('Customers table created successfully.');
        }
    });
};

// Function to create the products table
const createProductTable = () => {
    db.run(`
        CREATE TABLE IF NOT EXISTS products
        (
            id            INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id       INTEGER NOT NULL,
            supplier_id   INTEGER,
            category_id   INTEGER NOT NULL,
            brand_id      INTEGER NOT NULL,
            name          TEXT    NOT NULL,
            serial_number TEXT    NOT NULL,
            description   TEXT,
            color         TEXT,
            created_at    TEXT DEFAULT (datetime('now', 'localtime')),
            updated_at    TEXT,
            deleted_at    TEXT,
            FOREIGN KEY (user_id) REFERENCES users (id),
            FOREIGN KEY (supplier_id) REFERENCES suppliers (id),
            FOREIGN KEY (category_id) REFERENCES categories (id),
            FOREIGN KEY (brand_id) REFERENCES brands (id)
        )
    `, err => {
        if (err) {
            console.error('Error creating products table:', err.message);
        } else {
            console.log('Products table created successfully.');
        }
    });
};

// Function to create the suppliers table
const createSupplierTable = () => {
    db.run(`
        CREATE TABLE IF NOT EXISTS suppliers
        (
            id         INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id    INTEGER NOT NULL,
            name       TEXT    NOT NULL,
            email      TEXT,
            phone      TEXT,
            address    TEXT,
            created_at TEXT DEFAULT (datetime('now', 'localtime')),
            updated_at TEXT,
            deleted_at TEXT,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
    `, err => {
        if (err) {
            console.error('Error creating suppliers table:', err.message);
        } else {
            console.log('Suppliers table created successfully.');
        }
    });
};


const createInventoryTable = () => {
    db.run(`
        CREATE TABLE IF NOT EXISTS inventory
        (
            id               INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id          INTEGER NOT NULL,
            product_id       INTEGER NOT NULL,
            date             TEXT    NOT NULL,
            quantity         TEXT,
            purchase_price   TEXT,
            sale_price       TEXT,
            whole_sale_price TEXT,
            created_at       TEXT DEFAULT (datetime('now', 'localtime')),
            updated_at       TEXT,
            deleted_at       TEXT,
            FOREIGN KEY (user_id) REFERENCES users (id),
            FOREIGN KEY (product_id) REFERENCES products (id)
        )
    `, err => {
        if (err) {
            console.error('Error creating inventory table:', err.message);
        } else {
            console.log('inventory table created successfully.');
        }
    });
};


const createSalesTables = () => {
    db.run(`
        CREATE TABLE IF NOT EXISTS sales
        (
            id         INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id    INTEGER NOT NULL,
            created_at TEXT DEFAULT (datetime('now', 'localtime')),
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
    `, err => {
        if (err) {
            console.error('Error creating sales table:', err.message);
        } else {
            console.log('Sales table created successfully.');
        }
    });

    db.run(`
        CREATE TABLE IF NOT EXISTS sale_items
        (
            id             INTEGER PRIMARY KEY AUTOINCREMENT,
            sale_id        INTEGER NOT NULL,
            product_id     INTEGER NOT NULL,
            quantity       INTEGER NOT NULL,
            purchase_price REAL    NOT NULL,
            sale_price     REAL    NOT NULL,
            FOREIGN KEY (sale_id) REFERENCES sales (id),
            FOREIGN KEY (product_id) REFERENCES products (id)
        )
    `, err => {
        if (err) {
            console.error('Error creating sale_items table:', err.message);
        } else {
            console.log('Sale items table created successfully.');
        }
    });
};

const createSettingsTable = () => {
    const query = `
        CREATE TABLE IF NOT EXISTS settings
        (
            id         INTEGER PRIMARY KEY AUTOINCREMENT,
            key        TEXT UNIQUE NOT NULL,
            value      TEXT        NOT NULL,
            created_at TEXT DEFAULT (datetime('now', 'localtime')),
            updated_at TEXT
        );
    `;
    return db.run(query, (err) => {
        if (err) {
            console.error('Error creating settings table:', err.message);
            throw err;
        } else {
            console.log('Settings table created successfully.');
        }
    });
};

module.exports = {
    runMigrations: () => {
        createUserTable();
        createBrandTable();
        createCategoryTable();
        createCustomerTable();
        createProductTable();
        createSupplierTable();
        createInventoryTable();
        createSalesTables();
        createSettingsTable();
        // Add more migration functions as needed
    }
};
