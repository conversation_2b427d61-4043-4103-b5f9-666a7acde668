const axios = require("axios");
const crypto = require("crypto");
const jwt = require("jsonwebtoken");
const fs = require("fs");

// Disable SSL verification (for sandbox environment)
process.env.NODE_TLS_REJECT_UNAUTHORIZED = "0";

async function main(cardId = "KDRYV10100037340", expDate = "01/2027") {
    try {
        // Define API endpoint
        const host = "apifintech.sandbox.itcardpaymentservice.pl";
        const port = "30080";
        const baseUrl = `https://${host}:${port}`;
        const authUrl = `${baseUrl}/v1/cards/${cardId}/auth`;

        // Generate EC key pair using SECP256R1
        const ecdh = crypto.createECDH("prime256v1");
        ecdh.generateKeys();

        // Convert public key to Base64
        const publicKeyBase64 = ecdh.getPublicKey().toString("base64");
        console.log("Client public key:", publicKeyBase64);

        // Create request payload
        const payload = {
            resource: "CARD_NUMBER",
            securityKey: publicKeyBase64,
        };

        // Load certificates
        const cert = fs.readFileSync("RYVL_API.crt");
        const key = fs.readFileSync("RYVL_API.key");

        // Send authentication request
        const authResponse = await axios.post(authUrl, payload, {
            headers: {
                "Content-Type": "application/json;charset=UTF-8",
                "Accept": "application/json;charset=UTF-8",
            },
            httpsAgent: new (require("https").Agent)({ cert, key }),
        });

        console.log("Auth Response Status:", authResponse.status);

        if (authResponse.status !== 200 || !authResponse.data.accessToken) {
            throw new Error("Authentication failed. No access token received.");
        }

        // Decode JWT and extract VSU URL
        const accessToken = authResponse.data.accessToken;
        const decodedJwt = jwt.decode(accessToken);
        const vsuUrl = decodedJwt.vsu;

        if (!vsuUrl) {
            throw new Error("Error: No VSU URL found in JWT");
        }
        console.log("VSU URL:", vsuUrl);

        // Request encrypted card number
        const encryptedResponse = await axios.post(
            vsuUrl,
            { expDate },
            {
                headers: {
                    "Content-Type": "application/json;charset=UTF-8",
                    "Accept": "application/json;charset=UTF-8",
                    Authorization: `Bearer ${accessToken}`,
                },
                httpsAgent: new (require("https").Agent)({ cert, key }),
            }
        );

        console.log("Card Data Response Status:", encryptedResponse.status);

        if (encryptedResponse.status !== 200) {
            throw new Error("Error retrieving encrypted card number.");
        }

        const { cardNumber, securityKey, iv } = encryptedResponse.data;

        if (!cardNumber || !securityKey || !iv) {
            throw new Error("Invalid encrypted data received.");
        }

        // Decode Base64 data
        const encryptedData = Buffer.from(cardNumber, "base64");
        const serverPublicKey = Buffer.from(securityKey, "base64");
        const ivBuffer = Buffer.from(iv, "base64");

        console.log("Encrypted card number:", encryptedData.toString("hex"));
        console.log("Public key:", serverPublicKey.toString("hex"));
        console.log("IV:", ivBuffer.toString("hex"));

        // Compute shared secret using ECDH
        const serverKey = crypto.createPublicKey({
            key: serverPublicKey,
            format: "der",
            type: "spki",
        });

        const sharedSecret = ecdh.computeSecret(serverKey);
        console.log("Shared Secret:", sharedSecret.toString("hex"));

        // Decrypt the card number using AES-GCM
        const authTag = encryptedData.slice(-16);
        const encryptedContent = encryptedData.slice(0, -16);

        if (authTag.length !== 16) throw new Error("AuthTag must be 16 bytes");

        console.log("AuthTag:", authTag.toString("hex"));

        const decipher = crypto.createDecipheriv("aes-256-gcm", sharedSecret, ivBuffer);
        decipher.setAuthTag(authTag);

        const decryptedCardNumber = Buffer.concat([
            decipher.update(encryptedContent),
            decipher.final(),
        ]).toString();

        console.log("Decryption Successful! Card Number:", decryptedCardNumber);
        return decryptedCardNumber;
    } catch (error) {
        console.error("Error:", error.message);
    }
}

main();
