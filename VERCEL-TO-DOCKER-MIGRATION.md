# Vercel to Docker Migration Guide

## 📋 Migration Overview

This guide covers migrating the Ryvyl Backend from **Vercel serverless deployment** to a **Docker-based containerized deployment**, addressing current limitations and implementing security improvements.

## 🚨 Why Migrate from Vercel?

### **Current Vercel Limitations**
1. **File Storage**: Read-only filesystem - uploads don't persist
2. **Dual Services**: Can't run both HTTP and SOAP servers together
3. **Execution Timeout**: 10-second limit affects long operations
4. **Memory Constraints**: Limited memory for file processing
5. **No Background Jobs**: Can't run background processing
6. **Cold Starts**: Performance impact from serverless cold starts
7. **SSL Certificates**: Local certificate files don't work
8. **Database Connections**: No persistent connection pooling

### **Security Issues with Current Setup**
- **Hardcoded credentials** in repository
- **9 NoSQL injection vulnerabilities**
- **Missing authentication** on file uploads
- **Insecure CORS** configuration
- **No input validation** on critical endpoints

## 🎯 Migration Strategy

### **Phase 1: Preparation (Week 1)**
1. Fix security vulnerabilities
2. Update application configuration
3. Prepare Docker environment
4. Set up proper environment management

### **Phase 2: Containerization (Week 2)**
1. Create Docker images
2. Set up Docker Compose stack
3. Configure persistent storage
4. Implement health checks

### **Phase 3: Deployment (Week 3)**
1. Deploy to staging environment
2. Migrate database (if needed)
3. Test all functionality
4. Deploy to production

### **Phase 4: Optimization (Week 4)**
1. Performance tuning
2. Monitoring setup
3. Backup implementation
4. Documentation updates

## 🔧 Step-by-Step Migration

### Step 1: Fix Security Vulnerabilities

#### **Remove Hardcoded Credentials**
```bash
# Create proper environment files
cp example.env .env.example

# Remove sensitive data from example.env
cat > .env.example << 'EOF'
# Application
NODE_ENV=production
PORT=3000

# Database
DATABASE_URL=*******************************************

# Authentication
JWT_SECRET=your-jwt-secret-minimum-32-characters
SESSION_SECRET=your-session-secret

# Email
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-email-password

# External Services
CARD_WEBHOOK=https://your-webhook-url.com
EOF

# Add to .gitignore
echo ".env" >> .gitignore
echo ".env.local" >> .gitignore
echo ".env.production" >> .gitignore
```

#### **Update Package.json Scripts**
```json
{
  "scripts": {
    "start": "node index.js",
    "start:dev": "nodemon index.js",
    "start:soap": "node server.js",
    "start:soap:dev": "nodemon server.js",
    "start:production": "NODE_ENV=production node index.js",
    "test": "echo \"Error: no test specified\" && exit 1",
    "lint": "eslint .",
    "security-audit": "npm audit"
  }
}
```

#### **Implement Input Validation**
```javascript
// middleware/validation.js
const { body, validationResult } = require('express-validator');

const validateInput = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      errors: errors.array()
    });
  }
  next();
};

// Sanitize MongoDB queries
const sanitizeQuery = (req, res, next) => {
  const sanitize = (obj) => {
    if (obj && typeof obj === 'object') {
      for (const key in obj) {
        if (key.startsWith('$') || key.includes('.')) {
          delete obj[key];
        } else if (typeof obj[key] === 'object') {
          sanitize(obj[key]);
        }
      }
    }
  };
  
  sanitize(req.query);
  sanitize(req.body);
  sanitize(req.params);
  next();
};

module.exports = { validateInput, sanitizeQuery };
```

### Step 2: Create Docker Configuration

#### **Create Dockerfile**
```dockerfile
# Use the Dockerfile from deployment/Dockerfile
# Copy it to the root directory
cp deployment/Dockerfile ./Dockerfile
```

#### **Create .dockerignore**
```bash
# Copy the optimized .dockerignore
cp deployment/.dockerignore ./.dockerignore
```

#### **Create docker-compose.yml**
```yaml
# Copy the complete docker-compose configuration
cp deployment/docker-compose.yml ./docker-compose.yml
```

### Step 3: Environment Configuration

#### **Create Environment Files**
```bash
# Production environment
cat > .env.production << 'EOF'
NODE_ENV=production
PORT=3000
DATABASE_URL=***********************************************************************
JWT_SECRET=GENERATE_SECURE_32_CHAR_SECRET
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-secure-email-password
CORS_ORIGINS=https://app.ryvyl.com,https://admin.ryvyl.com
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=100
LOG_LEVEL=warn
UPLOAD_MAX_SIZE=5242880
EOF

# Staging environment
cat > .env.staging << 'EOF'
NODE_ENV=staging
PORT=3000
DATABASE_URL=********************************************************************************
JWT_SECRET=STAGING_JWT_SECRET_32_CHARS
EMAIL_USER=<EMAIL>
EMAIL_PASS=staging-email-password
CORS_ORIGINS=https://staging.ryvyl.com
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=200
LOG_LEVEL=info
UPLOAD_MAX_SIZE=5242880
EOF
```

#### **Environment Variable Validation**
```javascript
// config/env-validation.js
const { z } = require('zod');

const envSchema = z.object({
  NODE_ENV: z.enum(['development', 'staging', 'production']),
  PORT: z.coerce.number().int().min(1).max(65535).default(3000),
  DATABASE_URL: z.string().url(),
  JWT_SECRET: z.string().min(32),
  EMAIL_USER: z.string().email(),
  EMAIL_PASS: z.string().min(1),
  CORS_ORIGINS: z.string().default('*'),
  RATE_LIMIT_WINDOW: z.coerce.number().int().min(60000).default(900000),
  RATE_LIMIT_MAX: z.coerce.number().int().min(1).default(100),
  LOG_LEVEL: z.enum(['error', 'warn', 'info', 'debug']).default('info'),
  UPLOAD_MAX_SIZE: z.coerce.number().int().min(1024).default(5242880)
});

try {
  const env = envSchema.parse(process.env);
  module.exports = env;
} catch (error) {
  console.error('Environment validation failed:', error.errors);
  process.exit(1);
}
```

### Step 4: Application Updates

#### **Update Main Entry Point (index.js)**
```javascript
// Add at the top of index.js
require('dotenv').config();
const config = require('./config/env-validation');
const { sanitizeQuery } = require('./middleware/validation');

// Add global middleware
app.use(sanitizeQuery);

// Add health check endpoint
app.get('/api/health', (req, res) => {
  res.status(200).json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version || '1.0.0',
    environment: process.env.NODE_ENV
  });
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  server.close(() => {
    console.log('Process terminated');
    process.exit(0);
  });
});
```

#### **Update SOAP Server (server.js)**
```javascript
// Add health check to SOAP server
app.get('/soap/health', (req, res) => {
  res.status(200).json({
    status: 'healthy',
    service: 'soap',
    timestamp: new Date().toISOString()
  });
});
```

### Step 5: Database Migration

#### **Create Database Initialization Script**
```javascript
// scripts/init-database.js
const mongoose = require('mongoose');
require('dotenv').config();

async function initializeDatabase() {
  try {
    await mongoose.connect(process.env.DATABASE_URL);
    
    // Create indexes for performance
    const db = mongoose.connection.db;
    
    // User indexes
    await db.collection('users').createIndex({ email: 1 }, { unique: true });
    await db.collection('users').createIndex({ status: 1 });
    
    // Add other indexes as needed
    console.log('Database initialized successfully');
    
  } catch (error) {
    console.error('Database initialization failed:', error);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
  }
}

if (require.main === module) {
  initializeDatabase();
}
```

### Step 6: Build and Test

#### **Build Docker Image**
```bash
# Build the application image
docker build -t ryvyl-backend:latest .

# Test the image
docker run --rm -p 3000:3000 \
  -e NODE_ENV=development \
  -e DATABASE_URL="mongodb://localhost:27017/ryvyl" \
  -e JWT_SECRET="test-jwt-secret-32-characters-long" \
  ryvyl-backend:latest
```

#### **Test with Docker Compose**
```bash
# Start the complete stack
docker-compose up -d

# Check logs
docker-compose logs -f app

# Test health endpoints
curl http://localhost:3000/api/health
curl http://localhost:30080/soap/health

# Test API functionality
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"testpass"}'
```

### Step 7: Production Deployment

#### **Prepare Production Environment**
```bash
# Create production environment file
cp .env.production .env

# Generate secure secrets
node -e "console.log('JWT_SECRET=' + require('crypto').randomBytes(32).toString('hex'))"
openssl rand -base64 32  # For session secret

# Update .env with real values
```

#### **Deploy to Production**
```bash
# Option 1: Docker Compose
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

# Option 2: Kubernetes
kubectl apply -f deployment/k8s/ -n ryvyl-backend

# Option 3: Docker Swarm
docker stack deploy -c deployment/portainer-stack.yml ryvyl-backend
```

### Step 8: Migration Verification

#### **Functionality Testing**
```bash
# Test all critical endpoints
./scripts/test-endpoints.sh

# Test file uploads
curl -X POST http://your-domain.com/api/upload \
  -H "Authorization: Bearer $TOKEN" \
  -F "file=@test-file.jpg"

# Test SOAP service
curl -X POST http://your-domain.com/soap/service \
  -H "Content-Type: text/xml" \
  -d @soap-request.xml
```

#### **Performance Testing**
```bash
# Load testing with Apache Bench
ab -n 1000 -c 10 http://your-domain.com/api/health

# Monitor resource usage
docker stats
```

### Step 9: DNS and SSL Setup

#### **Update DNS Records**
```bash
# Point your domain to the new server
# A record: api.ryvyl.com -> YOUR_SERVER_IP
# CNAME record: www.api.ryvyl.com -> api.ryvyl.com
```

#### **SSL Certificate Setup**
```bash
# Using Let's Encrypt with Certbot
sudo certbot --nginx -d api.ryvyl.com

# Or use the nginx configuration from deployment/nginx/
```

### Step 10: Monitoring and Maintenance

#### **Set Up Monitoring**
```bash
# Application monitoring
docker-compose -f docker-compose.yml -f docker-compose.monitoring.yml up -d

# Log monitoring
docker-compose logs -f --tail=100 app
```

#### **Backup Strategy**
```bash
# Database backup
docker-compose exec mongo mongodump --out /backup/$(date +%Y%m%d)

# File backup
docker-compose exec app tar -czf /backup/uploads-$(date +%Y%m%d).tar.gz /app/uploads
```

## 🔄 Rollback Plan

### **Emergency Rollback to Vercel**
```bash
# 1. Revert DNS changes
# Point domain back to Vercel

# 2. Restore Vercel deployment
git checkout main
vercel --prod

# 3. Update database connection
# Point to original database if changed
```

### **Partial Rollback**
```bash
# Rollback to previous Docker image
docker-compose down
docker-compose up -d --scale app=0
docker tag ryvyl-backend:previous ryvyl-backend:latest
docker-compose up -d
```

## ✅ Post-Migration Checklist

### **Functionality Verification**
- [ ] All API endpoints working
- [ ] Authentication system functional
- [ ] File uploads working and persisting
- [ ] SOAP service operational
- [ ] Database connections stable
- [ ] Email notifications working

### **Performance Verification**
- [ ] Response times acceptable
- [ ] Memory usage optimized
- [ ] Database queries performing well
- [ ] File upload/download speeds good
- [ ] No memory leaks detected

### **Security Verification**
- [ ] All security vulnerabilities fixed
- [ ] HTTPS working properly
- [ ] CORS configured correctly
- [ ] Rate limiting functional
- [ ] Input validation working
- [ ] No sensitive data in logs

### **Operational Verification**
- [ ] Health checks responding
- [ ] Logging working properly
- [ ] Monitoring alerts configured
- [ ] Backup strategy implemented
- [ ] Documentation updated

## 🎯 Benefits After Migration

### **Improved Performance**
- Persistent database connections
- Better memory management
- Optimized file handling
- Reduced cold start times

### **Enhanced Security**
- Fixed injection vulnerabilities
- Proper secrets management
- Input validation implemented
- Secure file upload handling

### **Better Scalability**
- Horizontal scaling capability
- Load balancing support
- Resource optimization
- Background job processing

### **Operational Excellence**
- Health monitoring
- Proper logging
- Backup strategies
- Disaster recovery

## 📞 Support and Troubleshooting

### **Common Issues**
1. **Database Connection**: Check connection string and network
2. **File Permissions**: Ensure proper volume mount permissions
3. **Memory Issues**: Adjust container resource limits
4. **SSL Problems**: Verify certificate configuration

### **Getting Help**
- Check logs: `docker-compose logs -f app`
- Monitor resources: `docker stats`
- Test connectivity: `docker-compose exec app ping mongo`
- Verify environment: `docker-compose exec app env`

The migration from Vercel to Docker provides a robust, scalable, and secure deployment platform that addresses all current limitations and security concerns.
