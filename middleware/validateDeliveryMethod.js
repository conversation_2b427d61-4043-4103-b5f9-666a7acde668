// Middleware to validate delivery method data
const validateDeliveryMethod = (req, res, next) => {
    const { name, code, deliveryTime, price } = req.body

    // Validate required fields
    if (!name || !code || !deliveryTime || price === undefined) {
        return res.status(400).json({
            error: "Validation Error",
            message: "Name, code, delivery time, and price are required fields",
        })
    }

    // Validate name length
    if (name.trim().length < 2) {
        return res.status(400).json({
            error: "Validation Error",
            message: "Name must be at least 2 characters long",
        })
    }

    // Validate code format
    if (!/^[A-Z0-9]{2,10}$/i.test(code.trim())) {
        return res.status(400).json({
            error: "Validation Error",
            message: "Code must be 2-10 alphanumeric characters",
        })
    }

    // Validate price
    if (isNaN(price) || price < 0) {
        return res.status(400).json({
            error: "Validation Error",
            message: "Price must be a non-negative number",
        })
    }

    next()
}

module.exports = validateDeliveryMethod
