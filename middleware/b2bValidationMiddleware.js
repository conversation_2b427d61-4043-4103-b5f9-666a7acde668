const { validateB2BRegistration } = require("../validations/b2bValidations")

const validateB2BRegistrationMiddleware = (req, res, next) => {
    const { error, value } = validateB2BRegistration(req.body)

    if (error) {
        const errorMessages = error.details.map((detail) => ({
            field: detail.path.join("."),
            message: detail.message,
        }))

        return res.status(400).json({
            success: false,
            message: "Validation error",
            errors: errorMessages,
        })
    }

    // Replace req.body with validated and sanitized data
    req.body = value
    next()
}

module.exports = validateB2BRegistrationMiddleware
