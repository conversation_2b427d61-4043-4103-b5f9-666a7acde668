// Middleware to validate zone data
const validateZone = (req, res, next) => {
    const { name, number } = req.body

    // Validate required fields
    if (!name || !number) {
        return res.status(400).json({
            error: "Validation Error",
            message: "Name and number are required fields",
        })
    }

    // Validate name length
    if (name.trim().length < 2) {
        return res.status(400).json({
            error: "Validation Error",
            message: "Name must be at least 2 characters long",
        })
    }

    // // Validate number format (optional, customize as needed)
    // if (!/^[A-Z0-9]{2,10}$/i.test(number.trim())) {
    //     return res.status(400).json({
    //         error: "Validation Error",
    //         message: "Number must be 2-10 alphanumeric characters",
    //     })
    // }

    next()
}

module.exports = validateZone
