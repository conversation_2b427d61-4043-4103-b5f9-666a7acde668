// Optional middleware for handling file uploads with multer
const multer = require("multer")
const path = require("path")

// Configure multer for temporary storage
const storage = multer.memoryStorage()

// File filter function
const fileFilter = (req, file, cb) => {
    // Accept images and PDFs
    if (
        file.mimetype === "image/jpeg" ||
        file.mimetype === "image/png" ||
        file.mimetype === "image/jpg" ||
        file.mimetype === "application/pdf"
    ) {
        cb(null, true)
    } else {
        cb(new Error("Unsupported file type"), false)
    }
}

// Create multer upload instance
const upload = multer({
    storage: storage,
    limits: {
        fileSize: 5 * 1024 * 1024, // 5MB
    },
    fileFilter: fileFilter,
})

module.exports = {
    upload,
}

