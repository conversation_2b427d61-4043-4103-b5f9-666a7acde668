<wsdl:definitions xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap12/"
	xmlns:tns="http://www.gpayments.com/caas/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
	 targetNamespace="http://www.gpayments.com/caas/"
	name="caas">
	<wsdl:types>
		<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="http://www.gpayments.com/caas/">
			<xs:include schemaLocation="schema.xsd" />
		</xs:schema>
	</wsdl:types>
	<wsdl:message name="verifyRegRequest">
		<wsdl:part name="parameters" element="tns:verifyRegReq" />
	</wsdl:message>
	<wsdl:message name="verifyRegResponse">
		<wsdl:part name="parameters" element="tns:verifyRegResp" />
	</wsdl:message>

	<wsdl:message name="preAuthRequest">
		<wsdl:part name="parameters" element="tns:preAuthReq" />
	</wsdl:message>
	<wsdl:message name="preAuthResp">
		<wsdl:part name="parameters" element="tns:preAuthResp" />
	</wsdl:message>


	<wsdl:message name="initAuthRequest">
		<wsdl:part name="parameters" element="tns:initAuthReq" />
	</wsdl:message>
	<wsdl:message name="initAuthResp">
		<wsdl:part name="parameters" element="tns:initAuthResp" />
	</wsdl:message>
	<wsdl:message name="verifyAuthRequest">
		<wsdl:part name="parameters" element="tns:verifyAuthReq" />
	</wsdl:message>
	<wsdl:message name="verifyAuthResp">
		<wsdl:part name="parameters" element="tns:verifyAuthResp" />
	</wsdl:message>
	<wsdl:message name="verifyIdentityRequest">
		<wsdl:part name="parameters" element="tns:verifyIdentityReq" />
	</wsdl:message>
	<wsdl:message name="verifyIdentityResp">
		<wsdl:part name="parameters" element="tns:verifyIdentityResp" />
	</wsdl:message>
	<wsdl:message name="registerRequest">
		<wsdl:part name="parameters" element="tns:registerReq" />
	</wsdl:message>
	<wsdl:message name="registerResp">
		<wsdl:part name="parameters" element="tns:registerResp" />
	</wsdl:message>
	<wsdl:message name="resetPasswordRequest">
		<wsdl:part name="parameters" element="tns:resetPasswordReq" />
	</wsdl:message>
	<wsdl:message name="resetPasswordResp">
		<wsdl:part name="parameters" element="tns:resetPasswordResp" />
	</wsdl:message>

	<wsdl:message name="void" />
	<wsdl:portType name="caas">
		<wsdl:operation name="verifyRegistration">
			<wsdl:input message="tns:verifyRegRequest" />
			<wsdl:output message="tns:verifyRegResponse" />
		</wsdl:operation>
		<wsdl:operation name="initAuthentication">
			<wsdl:input message="tns:initAuthRequest" />
			<wsdl:output message="tns:initAuthResp" />
		</wsdl:operation>
		<wsdl:operation name="verifyAuthentication">
			<wsdl:input message="tns:verifyAuthRequest" />
			<wsdl:output message="tns:verifyAuthResp" />
		</wsdl:operation>
		<wsdl:operation name="preAuthentication">
			<wsdl:input message="tns:preAuthRequest" />
			<wsdl:output message="tns:preAuthResp" />
		</wsdl:operation>
		<wsdl:operation name="verifyIdentity">
			<wsdl:input message="tns:verifyIdentityRequest" />
			<wsdl:output message="tns:verifyIdentityResp" />
		</wsdl:operation>
		<wsdl:operation name="register">
			<wsdl:input message="tns:registerRequest" />
			<wsdl:output message="tns:registerResp" />
		</wsdl:operation>
		<wsdl:operation name="resetPassword">
			<wsdl:input message="tns:resetPasswordRequest" />
			<wsdl:output message="tns:resetPasswordResp" />
		</wsdl:operation>
		<wsdl:operation name="ping">
			<wsdl:input message="tns:void" />
			<wsdl:output message="tns:void" />
		</wsdl:operation>
	</wsdl:portType>
	<wsdl:binding name="caasSOAP" type="tns:caas">
		<soap:binding style="document"
			transport="http://schemas.xmlsoap.org/soap/http" />
		<wsdl:operation name="verifyRegistration">
			<soap:operation soapAction="http://gpayments.com/caas/verifyRegistration" />
			<wsdl:input>
				<soap:body use="literal" />
			</wsdl:input>
			<wsdl:output>
				<soap:body use="literal" />
			</wsdl:output>
		</wsdl:operation>
		<wsdl:operation name="preAuthentication">
			<soap:operation soapAction="http://gpayments.com/caas/preAuthentication" />
			<wsdl:input>
				<soap:body use="literal" />
			</wsdl:input>
			<wsdl:output>
				<soap:body use="literal" />
			</wsdl:output>
		</wsdl:operation>
		<wsdl:operation name="verifyAuthentication">
			<soap:operation soapAction="http://gpayments.com/caas/verifyAuthentication" />
			<wsdl:input>
				<soap:body use="literal" />
			</wsdl:input>
			<wsdl:output>
				<soap:body use="literal" />
			</wsdl:output>
		</wsdl:operation>
		<wsdl:operation name="initAuthentication">
			<soap:operation soapAction="http://gpayments.com/caas/initAuthentication" />
			<wsdl:input>
				<soap:body use="literal" />
			</wsdl:input>
			<wsdl:output>
				<soap:body use="literal" />
			</wsdl:output>
		</wsdl:operation>
		<wsdl:operation name="verifyIdentity">
			<soap:operation soapAction="http://gpayments.com/caas/verifyIdentity" />
			<wsdl:input>
				<soap:body use="literal" />
			</wsdl:input>
			<wsdl:output>
				<soap:body use="literal" />
			</wsdl:output>
		</wsdl:operation>

		<wsdl:operation name="register">
			<soap:operation soapAction="http://gpayments.com/caas/register" />
			<wsdl:input>
				<soap:body use="literal" />
			</wsdl:input>
			<wsdl:output>
				<soap:body use="literal" />
			</wsdl:output>
		</wsdl:operation>
		<wsdl:operation name="resetPassword">
			<soap:operation soapAction="http://gpayments.com/caas/resetPassword" />
			<wsdl:input>
				<soap:body use="literal" />
			</wsdl:input>
			<wsdl:output>
				<soap:body use="literal" />
			</wsdl:output>
		</wsdl:operation>
		<wsdl:operation name="ping">
			<soap:operation soapAction="http://gpayments.com/caas/ping" />
			<wsdl:input>
				<soap:body use="literal" />
			</wsdl:input>
			<wsdl:output>
				<soap:body use="literal" />
			</wsdl:output>
		</wsdl:operation>
	</wsdl:binding>
	<wsdl:service name="caasService">
		<wsdl:port name="caas" binding="tns:caasSOAP">
			<soap:address location="http://www.gpayments.com/caas" />
		</wsdl:port>
	</wsdl:service>
</wsdl:definitions>
