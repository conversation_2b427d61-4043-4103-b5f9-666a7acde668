{"info": {"_postman_id": "597e89d3-e0cb-4873-ab66-41c4d0b48f8c", "name": "Bank API Collection", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "********", "_collection_link": "https://pixelmagnus.postman.co/workspace/PixelMagnus~d4da03c0-7c63-4c8f-8986-dbe5ada2c3ad/collection/********-597e89d3-e0cb-4873-ab66-41c4d0b48f8c?action=share&source=collection_link&creator=********"}, "item": [{"name": "Add Existing Debit Account", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n                        \"accountNumber\": \"1234456\",\n                        \"accountHolder\": \"John Doe\",\n                        \"balance\": 1000,\n                        \"currency\": \"EUR\",\n                        \"status\": \"active\"\n                    }"}, "url": {"raw": "localhost:3001/api/il/debit-account", "host": ["localhost"], "port": "3001", "path": ["api", "il", "debit-account"]}}, "response": []}, {"name": "Get Account Transactions", "request": {"method": "GET", "header": [], "url": {"raw": "localhost:3001/api/il/transactions/:accountNumber?startDate=YYYY-MM-DD&endDate=YYYY-MM-DD", "host": ["localhost"], "port": "3001", "path": ["api", "il", "transactions", ":accountNumber"], "query": [{"key": "startDate", "value": "YYYY-MM-DD"}, {"key": "endDate", "value": "YYYY-MM-DD"}], "variable": [{"key": "accountNumber", "value": ""}]}}, "response": []}, {"name": "Get Transaction Details", "request": {"method": "GET", "header": [], "url": {"raw": "localhost:3001/api/il/transaction/:transactionId", "host": ["localhost"], "port": "3001", "path": ["api", "il", "transaction", ":transactionId"], "variable": [{"key": "transactionId", "value": ""}]}}, "response": []}, {"name": "Close Account at Organization Request", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"closeCause\": \"Fraud suspicion\"}"}, "url": {"raw": "localhost:3001/api/il//close-account/organization/:accountNumber", "host": ["localhost"], "port": "3001", "path": ["api", "il", "", "close-account", "organization", ":accountNumber"], "variable": [{"key": "accountNumber", "value": ""}]}}, "response": []}, {"name": "Close Account at Client Request", "request": {"method": "PUT", "header": [], "url": {"raw": "/api/close-account/client/:accountNumber", "path": ["api", "close-account", "client", ":accountNumber"], "variable": [{"key": "accountNumber", "value": ""}]}}, "response": []}]}