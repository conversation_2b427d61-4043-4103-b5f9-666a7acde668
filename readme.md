To create a **client certificate for another user**, follow these steps. This ensures the user gets a unique certificate signed by your **Certificate Authority (CA)**, which they can use to authenticate against your API.

---

## ✅ **Step 1: Generate a Private Key for the New Client**
Run this command on your server:
```sh
openssl genpkey -algorithm RSA -out client2.key
```
This generates a new private key for the user.

---

## ✅ **Step 2: Generate a Certificate Signing Request (CSR)**
A **CSR (Certificate Signing Request)** is required to get a signed client certificate. Ask the user for their details (or fill them yourself).

Run:
```sh
openssl req -new -key client2.key -out client2.csr
```

You'll be prompted for details:
```
Country Name (2 letter code) [AU]: US
State or Province Name (full name) [Some-State]: New York
Locality Name (eg, city) []: New York
Organization Name (eg, company) [Internet Widgits Pty Ltd]: Client User Inc.
Common Name (eg, YOUR name) []: <EMAIL>
```
💡 **Make sure `Common Name (CN)` is the unique email or identifier for the user.**

---

## ✅ **Step 3: Sign the Client Certificate with Your CA**
Now, sign the user's CSR with your **Certificate Authority (CA)** to issue a valid certificate.

Run:
```sh
openssl x509 -req -days 365 -in client2.csr -CA ca.crt -CAkey ca.key -CAcreateserial -out client2.crt
```
This creates **client2.crt**, a valid certificate for the new user.

---

## ✅ **Step 4: Verify the New Client Certificate**
Check the issued certificate:
```sh
openssl x509 -in client2.crt -noout -subject
```
Expected output:
```
subject= /C=US/ST=New York/L=New York/O=Client User Inc./CN=<EMAIL>
```
---

## ✅ **Step 5: Send the Certificate to the User**
Share the following **securely**:
- `client2.crt` (Client Certificate)
- `client2.key` (Client Private Key)
- `ca.crt` (CA Certificate) – needed to verify the server

DO NOT share `ca.key`, as it is private to your authority.

---

## ✅ **Step 6: User Makes an API Request**
The new user can now call your API using their client certificate.

### **Using cURL**
```sh
curl -k --cert client2.crt --key client2.key https://yourserver.com/secure
```
Expected response:
```json
{ "message": "Secure API Access Granted" }
```

### **Using Node.js (Axios)**
If they are using **Node.js**, they can use the following code to make API requests:

```js
const axios = require("axios");
const fs = require("fs");

axios.get("https://yourserver.com/secure", {
    httpsAgent: new (require("https").Agent)({
        cert: fs.readFileSync("client2.crt"),
        key: fs.readFileSync("client2.key"),
        ca: fs.readFileSync("ca.crt")
    })
}).then(response => {
    console.log(response.data);
}).catch(error => {
    console.error("❌ Access Denied:", error.response.data);
});
```

---

## ✅ **Step 7: Log Authorized Users on the Server**
Update your middleware to log the new client’s details:

```js
const requireClientCertificate = (req, res, next) => {
    const cert = req.socket.getPeerCertificate();
    
    if (req.client.authorized) {
        console.log(`✅ Authorized User: ${cert.subject.CN} (${cert.subject.O})`);
        return next();
    } else {
        console.warn("❌ Unauthorized Access Attempt:", cert.subject || "Unknown");
        return res.status(403).json({ error: "Client certificate required." });
    }
};
```

---

### 🎯 **Final Outcome**
✅ You created and signed a unique **client certificate** for another user.  
✅ The user can now authenticate using their **certificate and key**.  
✅ Your server **verifies and logs authorized clients** before allowing access.

Let me know if you need further customization! 🚀