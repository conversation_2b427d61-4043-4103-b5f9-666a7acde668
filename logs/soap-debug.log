[2025-05-09T07:59:46.708Z] verifyReg called: {
  args: {
    card: {
      id: undefined,
      number: '****************',
      type: 'VbV',
      context_Blob: undefined
    },
    transaction: {
      purchaseAmount: '10000',
      purchaseExponent: '2',
      purchaseCurrency: '036',
      purchaseDate: '2023-06-27T11:26:38.000Z',
      merchantId: '***************',
      merchantName: 'Test Merchant',
      merchantCountry: '840',
      acqBin: '***********',
      theeDSProtocolVersion: '2.1.0',
      cardExpiry: '2508',
      issuerName: 'remote',
      acsTransId: '36e6c66a-3dc6-4a59-a722-96c718824981',
      threeDSTransId: 'e6e9bf9b-af70-4f20-ad3a-52ce69de1c15',
      dsTransId: '2d5e6910-4d86-4d84-b6a3-9125b80b9ba1',
      threeDSRequestorID: '*********.visa',
      threeDSRequestorName: '3dsclient.local.visa',
      threeDSServerRefNumber: '3DS_LOA_SER_GPPL_020100_00075',
      threeDSServerOperatorID: '1jpeeLAWgGFgS1Ri9tX9',
      threeDSRequestorURL: 'http://gpayments.com',
      threeDSServerURL: 'https://amir-test3.testlab.3dsecure.cloud:9605/api/v2/ds/result/request',
      deviceChannel: '02',
      dsReferenceNumber: '3DS_LOA_ACS_GPPL_020200_00442',
      payTokenInd: '0',
      mcc: '2020',
      messageCategory: '01',
      transType: '01',
      acctType: '03',
      threeDSRequestorAuthenticationInd: '01'
    },
    headerParams: {
      param: [
        {
          attributes: { key: 'browserJavaEnabled', cookie: 'false' },
          '$value': 'false'
        },
        {
          attributes: { key: 'browserTZ', cookie: 'false' },
          '$value': '-180'
        },
        {
          attributes: { key: 'browserLanguage', cookie: 'false' },
          '$value': 'en-US'
        },
        {
          attributes: { key: 'Accept-Language', cookie: 'false' },
          '$value': 'en-US'
        },
        {
          attributes: { key: 'User-Agent', cookie: 'false' },
          '$value': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        },
        {
          attributes: { key: 'Accept', cookie: 'false' },
          '$value': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7'
        },
        {
          attributes: { key: 'proxy-ip', cookie: 'false' },
          '$value': '************'
        },
        {
          attributes: { key: 'browserColorDepth', cookie: 'false' },
          '$value': '24'
        },
        {
          attributes: { key: 'browserScreenHeight', cookie: 'false' },
          '$value': '1050'
        },
        {
          attributes: { key: 'browserScreenWidth', cookie: 'false' },
          '$value': '1680'
        }
      ]
    },
    extensionParams: undefined,
    additionalParams: {
      param: [
        { attributes: { key: 'shipAddrState' }, '$value': 'NSW' },
        { attributes: { key: 'shipAddrCity' }, '$value': 'Sydney' },
        { attributes: { key: 'shipAddrCountry' }, '$value': '036' },
        { attributes: { key: 'shipAddrLine1' }, '$value': 'Unit 1' },
        {
          attributes: { key: 'shipAddrLine2' },
          '$value': '123 Street'
        },
        { attributes: { key: 'shipAddrPostCode' }, '$value': '2000' },
        { attributes: { key: 'billAddrCity' }, '$value': 'Sydney' },
        { attributes: { key: 'billAddrCountry' }, '$value': '036' },
        { attributes: { key: 'billAddrLine1' }, '$value': 'Unit 1' },
        {
          attributes: { key: 'billAddrLine2' },
          '$value': '123 Street'
        },
        { attributes: { key: 'billAddrState' }, '$value': 'NSW' },
        { attributes: { key: 'billAddrPostCode' }, '$value': '2000' },
        { attributes: { key: 'threeDSCompInd' }, '$value': 'U' },
        {
          attributes: { key: 'threeDSRequestorAuthenticationInd' },
          '$value': '01'
        },
        {
          attributes: { key: 'threeDSRequestorChallengeInd' },
          '$value': '01'
        },
        { attributes: { key: 'addrMatch' }, '$value': 'Y' },
        { attributes: { key: 'cardExpiryDate' }, '$value': '2508' },
        { attributes: { key: 'acctID' }, '$value': 'personal account' },
        { attributes: { key: 'email' }, '$value': '<EMAIL>' },
        { attributes: { key: 'mobilePhone.cc' }, '$value': '61' },
        {
          attributes: { key: 'mobilePhone.subscriber' },
          '$value': '**********'
        }
      ]
    }
  },
  headers: undefined
}
[2025-05-09T07:59:46.716Z] Parsed input data: {
  card: {
    id: undefined,
    number: '****************',
    type: 'VbV',
    context_Blob: undefined
  },
  transaction: {
    purchaseAmount: '10000',
    purchaseExponent: '2',
    purchaseCurrency: '036',
    purchaseDate: '2023-06-27T11:26:38.000Z',
    merchantId: '***************',
    merchantName: 'Test Merchant',
    merchantCountry: '840',
    acqBin: '***********',
    theeDSProtocolVersion: '2.1.0',
    cardExpiry: '2508',
    issuerName: 'remote',
    acsTransId: '36e6c66a-3dc6-4a59-a722-96c718824981',
    threeDSTransId: 'e6e9bf9b-af70-4f20-ad3a-52ce69de1c15',
    dsTransId: '2d5e6910-4d86-4d84-b6a3-9125b80b9ba1',
    threeDSRequestorID: '*********.visa',
    threeDSRequestorName: '3dsclient.local.visa',
    threeDSServerRefNumber: '3DS_LOA_SER_GPPL_020100_00075',
    threeDSServerOperatorID: '1jpeeLAWgGFgS1Ri9tX9',
    threeDSRequestorURL: 'http://gpayments.com',
    threeDSServerURL: 'https://amir-test3.testlab.3dsecure.cloud:9605/api/v2/ds/result/request',
    deviceChannel: '02',
    dsReferenceNumber: '3DS_LOA_ACS_GPPL_020200_00442',
    payTokenInd: '0',
    mcc: '2020',
    messageCategory: '01',
    transType: '01',
    acctType: '03',
    threeDSRequestorAuthenticationInd: '01'
  },
  headerParams: {
    param: [
      {
        attributes: { key: 'browserJavaEnabled', cookie: 'false' },
        '$value': 'false'
      },
      {
        attributes: { key: 'browserTZ', cookie: 'false' },
        '$value': '-180'
      },
      {
        attributes: { key: 'browserLanguage', cookie: 'false' },
        '$value': 'en-US'
      },
      {
        attributes: { key: 'Accept-Language', cookie: 'false' },
        '$value': 'en-US'
      },
      {
        attributes: { key: 'User-Agent', cookie: 'false' },
        '$value': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
      },
      {
        attributes: { key: 'Accept', cookie: 'false' },
        '$value': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7'
      },
      {
        attributes: { key: 'proxy-ip', cookie: 'false' },
        '$value': '************'
      },
      {
        attributes: { key: 'browserColorDepth', cookie: 'false' },
        '$value': '24'
      },
      {
        attributes: { key: 'browserScreenHeight', cookie: 'false' },
        '$value': '1050'
      },
      {
        attributes: { key: 'browserScreenWidth', cookie: 'false' },
        '$value': '1680'
      }
    ]
  },
  extensionParams: undefined,
  additionalParams: {
    param: [
      { attributes: { key: 'shipAddrState' }, '$value': 'NSW' },
      { attributes: { key: 'shipAddrCity' }, '$value': 'Sydney' },
      { attributes: { key: 'shipAddrCountry' }, '$value': '036' },
      { attributes: { key: 'shipAddrLine1' }, '$value': 'Unit 1' },
      { attributes: { key: 'shipAddrLine2' }, '$value': '123 Street' },
      { attributes: { key: 'shipAddrPostCode' }, '$value': '2000' },
      { attributes: { key: 'billAddrCity' }, '$value': 'Sydney' },
      { attributes: { key: 'billAddrCountry' }, '$value': '036' },
      { attributes: { key: 'billAddrLine1' }, '$value': 'Unit 1' },
      { attributes: { key: 'billAddrLine2' }, '$value': '123 Street' },
      { attributes: { key: 'billAddrState' }, '$value': 'NSW' },
      { attributes: { key: 'billAddrPostCode' }, '$value': '2000' },
      { attributes: { key: 'threeDSCompInd' }, '$value': 'U' },
      {
        attributes: { key: 'threeDSRequestorAuthenticationInd' },
        '$value': '01'
      },
      {
        attributes: { key: 'threeDSRequestorChallengeInd' },
        '$value': '01'
      },
      { attributes: { key: 'addrMatch' }, '$value': 'Y' },
      { attributes: { key: 'cardExpiryDate' }, '$value': '2508' },
      { attributes: { key: 'acctID' }, '$value': 'personal account' },
      { attributes: { key: 'email' }, '$value': '<EMAIL>' },
      { attributes: { key: 'mobilePhone.cc' }, '$value': '61' },
      {
        attributes: { key: 'mobilePhone.subscriber' },
        '$value': '**********'
      }
    ]
  }
}
[2025-05-09T07:59:46.719Z] getCardKey input: {
  card: {
    id: undefined,
    number: '****************',
    type: 'VbV',
    context_Blob: undefined
  },
  transaction: {
    purchaseAmount: '10000',
    purchaseExponent: '2',
    purchaseCurrency: '036',
    purchaseDate: '2023-06-27T11:26:38.000Z',
    merchantId: '***************',
    merchantName: 'Test Merchant',
    merchantCountry: '840',
    acqBin: '***********',
    theeDSProtocolVersion: '2.1.0',
    cardExpiry: '2508',
    issuerName: 'remote',
    acsTransId: '36e6c66a-3dc6-4a59-a722-96c718824981',
    threeDSTransId: 'e6e9bf9b-af70-4f20-ad3a-52ce69de1c15',
    dsTransId: '2d5e6910-4d86-4d84-b6a3-9125b80b9ba1',
    threeDSRequestorID: '*********.visa',
    threeDSRequestorName: '3dsclient.local.visa',
    threeDSServerRefNumber: '3DS_LOA_SER_GPPL_020100_00075',
    threeDSServerOperatorID: '1jpeeLAWgGFgS1Ri9tX9',
    threeDSRequestorURL: 'http://gpayments.com',
    threeDSServerURL: 'https://amir-test3.testlab.3dsecure.cloud:9605/api/v2/ds/result/request',
    deviceChannel: '02',
    dsReferenceNumber: '3DS_LOA_ACS_GPPL_020200_00442',
    payTokenInd: '0',
    mcc: '2020',
    messageCategory: '01',
    transType: '01',
    acctType: '03',
    threeDSRequestorAuthenticationInd: '01'
  },
  headerParams: {
    param: [
      {
        attributes: { key: 'browserJavaEnabled', cookie: 'false' },
        '$value': 'false'
      },
      {
        attributes: { key: 'browserTZ', cookie: 'false' },
        '$value': '-180'
      },
      {
        attributes: { key: 'browserLanguage', cookie: 'false' },
        '$value': 'en-US'
      },
      {
        attributes: { key: 'Accept-Language', cookie: 'false' },
        '$value': 'en-US'
      },
      {
        attributes: { key: 'User-Agent', cookie: 'false' },
        '$value': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
      },
      {
        attributes: { key: 'Accept', cookie: 'false' },
        '$value': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7'
      },
      {
        attributes: { key: 'proxy-ip', cookie: 'false' },
        '$value': '************'
      },
      {
        attributes: { key: 'browserColorDepth', cookie: 'false' },
        '$value': '24'
      },
      {
        attributes: { key: 'browserScreenHeight', cookie: 'false' },
        '$value': '1050'
      },
      {
        attributes: { key: 'browserScreenWidth', cookie: 'false' },
        '$value': '1680'
      }
    ]
  },
  extensionParams: undefined,
  additionalParams: {
    param: [
      { attributes: { key: 'shipAddrState' }, '$value': 'NSW' },
      { attributes: { key: 'shipAddrCity' }, '$value': 'Sydney' },
      { attributes: { key: 'shipAddrCountry' }, '$value': '036' },
      { attributes: { key: 'shipAddrLine1' }, '$value': 'Unit 1' },
      { attributes: { key: 'shipAddrLine2' }, '$value': '123 Street' },
      { attributes: { key: 'shipAddrPostCode' }, '$value': '2000' },
      { attributes: { key: 'billAddrCity' }, '$value': 'Sydney' },
      { attributes: { key: 'billAddrCountry' }, '$value': '036' },
      { attributes: { key: 'billAddrLine1' }, '$value': 'Unit 1' },
      { attributes: { key: 'billAddrLine2' }, '$value': '123 Street' },
      { attributes: { key: 'billAddrState' }, '$value': 'NSW' },
      { attributes: { key: 'billAddrPostCode' }, '$value': '2000' },
      { attributes: { key: 'threeDSCompInd' }, '$value': 'U' },
      {
        attributes: { key: 'threeDSRequestorAuthenticationInd' },
        '$value': '01'
      },
      {
        attributes: { key: 'threeDSRequestorChallengeInd' },
        '$value': '01'
      },
      { attributes: { key: 'addrMatch' }, '$value': 'Y' },
      { attributes: { key: 'cardExpiryDate' }, '$value': '2508' },
      { attributes: { key: 'acctID' }, '$value': 'personal account' },
      { attributes: { key: 'email' }, '$value': '<EMAIL>' },
      { attributes: { key: 'mobilePhone.cc' }, '$value': '61' },
      {
        attributes: { key: 'mobilePhone.subscriber' },
        '$value': '**********'
      }
    ]
  }
}
[2025-05-09T07:59:46.722Z] Card details: { original: '****************', masked: '446614******6104' }
[2025-05-09T07:59:46.915Z] Card found: { cardKey: 'KDR0020100057072' }
[2025-05-09T07:59:47.965Z] Card details retrieved: {
  cardKey: 'KDR0020100057072',
  cardDetails: {
    cardHash: 'FBBB71A8EC741FB001ADDCD140F6141FEEB4708BA9D515C8014828373E96B17C86F0DA62E45FDA39AF93E443D939864F39E3D69C88E4C9A23F610692B7250AC7',
    cardMask: '446614******6104',
    cardKey: 'KDR0020100057072',
    cardUuid: '492bea20-20f0-11f0-4c29-6f786f726131',
    expDate: '04/2028',
    issueDate: '2025-04-24',
    status: 'ACTIVE',
    statusCode: '0',
    terminating: false,
    productCode: 'R002',
    productDesc: 'RYVYL VISA CONSUMER DEBIT VTL',
    holder: 'RYVL-742897',
    accounts: [
      {
        accNo: '**********************',
        primary: true,
        currencyCode: '978',
        currencyName: 'EUR'
      }
    ],
    embossName1: 'TRENT ARNOLD',
    source: 'NEW',
    kind: 'DEBIT',
    main: false,
    limits: [],
    visual: 'R002',
    tokenized: false,
    delivery: {
      deliveryType: 'NONE',
      deliveryBranch: 'DHL',
      oneTimeDeliveryBranch: 'DHL'
    },
    contactless: true,
    cardTechnologyMode: 'VIRTUAL',
    creationDate: '2025-04-24',
    autoRenewal: true
  }
}
[2025-05-09T07:59:47.968Z] verifyReg response: {
  verifyRegResp: '{"cardInfo":{"card_ID":"****************","context_Blob":"xyz123","regStatus":0,"authRequired":1,"authTypeSup":[1,2],"lanCode":"","twoFA":true},"code":1,"errorMessage":"Card found","errorDetail":"Card info fetched successfully"}'
}
[2025-05-09T07:59:59.781Z] verifyReg called: {
  args: {
    card: {
      id: undefined,
      number: '****************',
      type: 'VbV',
      context_Blob: undefined
    },
    transaction: {
      purchaseAmount: '10000',
      purchaseExponent: '2',
      purchaseCurrency: '036',
      purchaseDate: '2023-06-27T11:26:38.000Z',
      merchantId: '***************',
      merchantName: 'Test Merchant',
      merchantCountry: '840',
      acqBin: '***********',
      theeDSProtocolVersion: '2.1.0',
      cardExpiry: '2508',
      issuerName: 'remote',
      acsTransId: '36e6c66a-3dc6-4a59-a722-96c718824981',
      threeDSTransId: 'e6e9bf9b-af70-4f20-ad3a-52ce69de1c15',
      dsTransId: '2d5e6910-4d86-4d84-b6a3-9125b80b9ba1',
      threeDSRequestorID: '*********.visa',
      threeDSRequestorName: '3dsclient.local.visa',
      threeDSServerRefNumber: '3DS_LOA_SER_GPPL_020100_00075',
      threeDSServerOperatorID: '1jpeeLAWgGFgS1Ri9tX9',
      threeDSRequestorURL: 'http://gpayments.com',
      threeDSServerURL: 'https://amir-test3.testlab.3dsecure.cloud:9605/api/v2/ds/result/request',
      deviceChannel: '02',
      dsReferenceNumber: '3DS_LOA_ACS_GPPL_020200_00442',
      payTokenInd: '0',
      mcc: '2020',
      messageCategory: '01',
      transType: '01',
      acctType: '03',
      threeDSRequestorAuthenticationInd: '01'
    },
    headerParams: {
      param: [
        {
          attributes: { key: 'browserJavaEnabled', cookie: 'false' },
          '$value': 'false'
        },
        {
          attributes: { key: 'browserTZ', cookie: 'false' },
          '$value': '-180'
        },
        {
          attributes: { key: 'browserLanguage', cookie: 'false' },
          '$value': 'en-US'
        },
        {
          attributes: { key: 'Accept-Language', cookie: 'false' },
          '$value': 'en-US'
        },
        {
          attributes: { key: 'User-Agent', cookie: 'false' },
          '$value': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        },
        {
          attributes: { key: 'Accept', cookie: 'false' },
          '$value': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7'
        },
        {
          attributes: { key: 'proxy-ip', cookie: 'false' },
          '$value': '************'
        },
        {
          attributes: { key: 'browserColorDepth', cookie: 'false' },
          '$value': '24'
        },
        {
          attributes: { key: 'browserScreenHeight', cookie: 'false' },
          '$value': '1050'
        },
        {
          attributes: { key: 'browserScreenWidth', cookie: 'false' },
          '$value': '1680'
        }
      ]
    },
    extensionParams: undefined,
    additionalParams: {
      param: [
        { attributes: { key: 'shipAddrState' }, '$value': 'NSW' },
        { attributes: { key: 'shipAddrCity' }, '$value': 'Sydney' },
        { attributes: { key: 'shipAddrCountry' }, '$value': '036' },
        { attributes: { key: 'shipAddrLine1' }, '$value': 'Unit 1' },
        {
          attributes: { key: 'shipAddrLine2' },
          '$value': '123 Street'
        },
        { attributes: { key: 'shipAddrPostCode' }, '$value': '2000' },
        { attributes: { key: 'billAddrCity' }, '$value': 'Sydney' },
        { attributes: { key: 'billAddrCountry' }, '$value': '036' },
        { attributes: { key: 'billAddrLine1' }, '$value': 'Unit 1' },
        {
          attributes: { key: 'billAddrLine2' },
          '$value': '123 Street'
        },
        { attributes: { key: 'billAddrState' }, '$value': 'NSW' },
        { attributes: { key: 'billAddrPostCode' }, '$value': '2000' },
        { attributes: { key: 'threeDSCompInd' }, '$value': 'U' },
        {
          attributes: { key: 'threeDSRequestorAuthenticationInd' },
          '$value': '01'
        },
        {
          attributes: { key: 'threeDSRequestorChallengeInd' },
          '$value': '01'
        },
        { attributes: { key: 'addrMatch' }, '$value': 'Y' },
        { attributes: { key: 'cardExpiryDate' }, '$value': '2508' },
        { attributes: { key: 'acctID' }, '$value': 'personal account' },
        { attributes: { key: 'email' }, '$value': '<EMAIL>' },
        { attributes: { key: 'mobilePhone.cc' }, '$value': '61' },
        {
          attributes: { key: 'mobilePhone.subscriber' },
          '$value': '**********'
        }
      ]
    }
  },
  headers: undefined
}
[2025-05-09T07:59:59.786Z] Parsed input data: {
  card: {
    id: undefined,
    number: '****************',
    type: 'VbV',
    context_Blob: undefined
  },
  transaction: {
    purchaseAmount: '10000',
    purchaseExponent: '2',
    purchaseCurrency: '036',
    purchaseDate: '2023-06-27T11:26:38.000Z',
    merchantId: '***************',
    merchantName: 'Test Merchant',
    merchantCountry: '840',
    acqBin: '***********',
    theeDSProtocolVersion: '2.1.0',
    cardExpiry: '2508',
    issuerName: 'remote',
    acsTransId: '36e6c66a-3dc6-4a59-a722-96c718824981',
    threeDSTransId: 'e6e9bf9b-af70-4f20-ad3a-52ce69de1c15',
    dsTransId: '2d5e6910-4d86-4d84-b6a3-9125b80b9ba1',
    threeDSRequestorID: '*********.visa',
    threeDSRequestorName: '3dsclient.local.visa',
    threeDSServerRefNumber: '3DS_LOA_SER_GPPL_020100_00075',
    threeDSServerOperatorID: '1jpeeLAWgGFgS1Ri9tX9',
    threeDSRequestorURL: 'http://gpayments.com',
    threeDSServerURL: 'https://amir-test3.testlab.3dsecure.cloud:9605/api/v2/ds/result/request',
    deviceChannel: '02',
    dsReferenceNumber: '3DS_LOA_ACS_GPPL_020200_00442',
    payTokenInd: '0',
    mcc: '2020',
    messageCategory: '01',
    transType: '01',
    acctType: '03',
    threeDSRequestorAuthenticationInd: '01'
  },
  headerParams: {
    param: [
      {
        attributes: { key: 'browserJavaEnabled', cookie: 'false' },
        '$value': 'false'
      },
      {
        attributes: { key: 'browserTZ', cookie: 'false' },
        '$value': '-180'
      },
      {
        attributes: { key: 'browserLanguage', cookie: 'false' },
        '$value': 'en-US'
      },
      {
        attributes: { key: 'Accept-Language', cookie: 'false' },
        '$value': 'en-US'
      },
      {
        attributes: { key: 'User-Agent', cookie: 'false' },
        '$value': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
      },
      {
        attributes: { key: 'Accept', cookie: 'false' },
        '$value': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7'
      },
      {
        attributes: { key: 'proxy-ip', cookie: 'false' },
        '$value': '************'
      },
      {
        attributes: { key: 'browserColorDepth', cookie: 'false' },
        '$value': '24'
      },
      {
        attributes: { key: 'browserScreenHeight', cookie: 'false' },
        '$value': '1050'
      },
      {
        attributes: { key: 'browserScreenWidth', cookie: 'false' },
        '$value': '1680'
      }
    ]
  },
  extensionParams: undefined,
  additionalParams: {
    param: [
      { attributes: { key: 'shipAddrState' }, '$value': 'NSW' },
      { attributes: { key: 'shipAddrCity' }, '$value': 'Sydney' },
      { attributes: { key: 'shipAddrCountry' }, '$value': '036' },
      { attributes: { key: 'shipAddrLine1' }, '$value': 'Unit 1' },
      { attributes: { key: 'shipAddrLine2' }, '$value': '123 Street' },
      { attributes: { key: 'shipAddrPostCode' }, '$value': '2000' },
      { attributes: { key: 'billAddrCity' }, '$value': 'Sydney' },
      { attributes: { key: 'billAddrCountry' }, '$value': '036' },
      { attributes: { key: 'billAddrLine1' }, '$value': 'Unit 1' },
      { attributes: { key: 'billAddrLine2' }, '$value': '123 Street' },
      { attributes: { key: 'billAddrState' }, '$value': 'NSW' },
      { attributes: { key: 'billAddrPostCode' }, '$value': '2000' },
      { attributes: { key: 'threeDSCompInd' }, '$value': 'U' },
      {
        attributes: { key: 'threeDSRequestorAuthenticationInd' },
        '$value': '01'
      },
      {
        attributes: { key: 'threeDSRequestorChallengeInd' },
        '$value': '01'
      },
      { attributes: { key: 'addrMatch' }, '$value': 'Y' },
      { attributes: { key: 'cardExpiryDate' }, '$value': '2508' },
      { attributes: { key: 'acctID' }, '$value': 'personal account' },
      { attributes: { key: 'email' }, '$value': '<EMAIL>' },
      { attributes: { key: 'mobilePhone.cc' }, '$value': '61' },
      {
        attributes: { key: 'mobilePhone.subscriber' },
        '$value': '**********'
      }
    ]
  }
}
[2025-05-09T07:59:59.788Z] getCardKey input: {
  card: {
    id: undefined,
    number: '****************',
    type: 'VbV',
    context_Blob: undefined
  },
  transaction: {
    purchaseAmount: '10000',
    purchaseExponent: '2',
    purchaseCurrency: '036',
    purchaseDate: '2023-06-27T11:26:38.000Z',
    merchantId: '***************',
    merchantName: 'Test Merchant',
    merchantCountry: '840',
    acqBin: '***********',
    theeDSProtocolVersion: '2.1.0',
    cardExpiry: '2508',
    issuerName: 'remote',
    acsTransId: '36e6c66a-3dc6-4a59-a722-96c718824981',
    threeDSTransId: 'e6e9bf9b-af70-4f20-ad3a-52ce69de1c15',
    dsTransId: '2d5e6910-4d86-4d84-b6a3-9125b80b9ba1',
    threeDSRequestorID: '*********.visa',
    threeDSRequestorName: '3dsclient.local.visa',
    threeDSServerRefNumber: '3DS_LOA_SER_GPPL_020100_00075',
    threeDSServerOperatorID: '1jpeeLAWgGFgS1Ri9tX9',
    threeDSRequestorURL: 'http://gpayments.com',
    threeDSServerURL: 'https://amir-test3.testlab.3dsecure.cloud:9605/api/v2/ds/result/request',
    deviceChannel: '02',
    dsReferenceNumber: '3DS_LOA_ACS_GPPL_020200_00442',
    payTokenInd: '0',
    mcc: '2020',
    messageCategory: '01',
    transType: '01',
    acctType: '03',
    threeDSRequestorAuthenticationInd: '01'
  },
  headerParams: {
    param: [
      {
        attributes: { key: 'browserJavaEnabled', cookie: 'false' },
        '$value': 'false'
      },
      {
        attributes: { key: 'browserTZ', cookie: 'false' },
        '$value': '-180'
      },
      {
        attributes: { key: 'browserLanguage', cookie: 'false' },
        '$value': 'en-US'
      },
      {
        attributes: { key: 'Accept-Language', cookie: 'false' },
        '$value': 'en-US'
      },
      {
        attributes: { key: 'User-Agent', cookie: 'false' },
        '$value': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
      },
      {
        attributes: { key: 'Accept', cookie: 'false' },
        '$value': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7'
      },
      {
        attributes: { key: 'proxy-ip', cookie: 'false' },
        '$value': '************'
      },
      {
        attributes: { key: 'browserColorDepth', cookie: 'false' },
        '$value': '24'
      },
      {
        attributes: { key: 'browserScreenHeight', cookie: 'false' },
        '$value': '1050'
      },
      {
        attributes: { key: 'browserScreenWidth', cookie: 'false' },
        '$value': '1680'
      }
    ]
  },
  extensionParams: undefined,
  additionalParams: {
    param: [
      { attributes: { key: 'shipAddrState' }, '$value': 'NSW' },
      { attributes: { key: 'shipAddrCity' }, '$value': 'Sydney' },
      { attributes: { key: 'shipAddrCountry' }, '$value': '036' },
      { attributes: { key: 'shipAddrLine1' }, '$value': 'Unit 1' },
      { attributes: { key: 'shipAddrLine2' }, '$value': '123 Street' },
      { attributes: { key: 'shipAddrPostCode' }, '$value': '2000' },
      { attributes: { key: 'billAddrCity' }, '$value': 'Sydney' },
      { attributes: { key: 'billAddrCountry' }, '$value': '036' },
      { attributes: { key: 'billAddrLine1' }, '$value': 'Unit 1' },
      { attributes: { key: 'billAddrLine2' }, '$value': '123 Street' },
      { attributes: { key: 'billAddrState' }, '$value': 'NSW' },
      { attributes: { key: 'billAddrPostCode' }, '$value': '2000' },
      { attributes: { key: 'threeDSCompInd' }, '$value': 'U' },
      {
        attributes: { key: 'threeDSRequestorAuthenticationInd' },
        '$value': '01'
      },
      {
        attributes: { key: 'threeDSRequestorChallengeInd' },
        '$value': '01'
      },
      { attributes: { key: 'addrMatch' }, '$value': 'Y' },
      { attributes: { key: 'cardExpiryDate' }, '$value': '2508' },
      { attributes: { key: 'acctID' }, '$value': 'personal account' },
      { attributes: { key: 'email' }, '$value': '<EMAIL>' },
      { attributes: { key: 'mobilePhone.cc' }, '$value': '61' },
      {
        attributes: { key: 'mobilePhone.subscriber' },
        '$value': '**********'
      }
    ]
  }
}
[2025-05-09T07:59:59.791Z] Card details: { original: '****************', masked: '446614******6104' }
[2025-05-09T07:59:59.924Z] Card found: { cardKey: 'KDR0020100057072' }
[2025-05-09T08:00:01.078Z] Card details retrieved: {
  cardKey: 'KDR0020100057072',
  cardDetails: {
    cardHash: 'FBBB71A8EC741FB001ADDCD140F6141FEEB4708BA9D515C8014828373E96B17C86F0DA62E45FDA39AF93E443D939864F39E3D69C88E4C9A23F610692B7250AC7',
    cardMask: '446614******6104',
    cardKey: 'KDR0020100057072',
    cardUuid: '492bea20-20f0-11f0-4c29-6f786f726131',
    expDate: '04/2028',
    issueDate: '2025-04-24',
    status: 'ACTIVE',
    statusCode: '0',
    terminating: false,
    productCode: 'R002',
    productDesc: 'RYVYL VISA CONSUMER DEBIT VTL',
    holder: 'RYVL-742897',
    accounts: [
      {
        accNo: '**********************',
        primary: true,
        currencyCode: '978',
        currencyName: 'EUR'
      }
    ],
    embossName1: 'TRENT ARNOLD',
    source: 'NEW',
    kind: 'DEBIT',
    main: false,
    limits: [],
    visual: 'R002',
    tokenized: false,
    delivery: {
      deliveryType: 'NONE',
      deliveryBranch: 'DHL',
      oneTimeDeliveryBranch: 'DHL'
    },
    contactless: true,
    cardTechnologyMode: 'VIRTUAL',
    creationDate: '2025-04-24',
    autoRenewal: true
  }
}
[2025-05-09T08:00:01.079Z] verifyReg response: {
  verifyRegResp: '{"cardInfo":{"card_ID":"****************","context_Blob":"xyz123","regStatus":0,"authRequired":1,"authTypeSup":[1,2],"lanCode":"","twoFA":true},"code":1,"errorMessage":"Card found","errorDetail":"Card info fetched successfully"}'
}
[2025-05-09T08:00:20.080Z] verifyReg called: {
  args: {
    card: {
      id: undefined,
      number: '****************',
      type: 'VbV',
      context_Blob: undefined
    },
    transaction: {
      purchaseAmount: '10000',
      purchaseExponent: '2',
      purchaseCurrency: '036',
      purchaseDate: '2023-06-27T11:26:38.000Z',
      merchantId: '***************',
      merchantName: 'Test Merchant',
      merchantCountry: '840',
      acqBin: '***********',
      theeDSProtocolVersion: '2.1.0',
      cardExpiry: '2508',
      issuerName: 'remote',
      acsTransId: '36e6c66a-3dc6-4a59-a722-96c718824981',
      threeDSTransId: 'e6e9bf9b-af70-4f20-ad3a-52ce69de1c15',
      dsTransId: '2d5e6910-4d86-4d84-b6a3-9125b80b9ba1',
      threeDSRequestorID: '*********.visa',
      threeDSRequestorName: '3dsclient.local.visa',
      threeDSServerRefNumber: '3DS_LOA_SER_GPPL_020100_00075',
      threeDSServerOperatorID: '1jpeeLAWgGFgS1Ri9tX9',
      threeDSRequestorURL: 'http://gpayments.com',
      threeDSServerURL: 'https://amir-test3.testlab.3dsecure.cloud:9605/api/v2/ds/result/request',
      deviceChannel: '02',
      dsReferenceNumber: '3DS_LOA_ACS_GPPL_020200_00442',
      payTokenInd: '0',
      mcc: '2020',
      messageCategory: '01',
      transType: '01',
      acctType: '03',
      threeDSRequestorAuthenticationInd: '01'
    },
    headerParams: {
      param: [
        {
          attributes: { key: 'browserJavaEnabled', cookie: 'false' },
          '$value': 'false'
        },
        {
          attributes: { key: 'browserTZ', cookie: 'false' },
          '$value': '-180'
        },
        {
          attributes: { key: 'browserLanguage', cookie: 'false' },
          '$value': 'en-US'
        },
        {
          attributes: { key: 'Accept-Language', cookie: 'false' },
          '$value': 'en-US'
        },
        {
          attributes: { key: 'User-Agent', cookie: 'false' },
          '$value': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        },
        {
          attributes: { key: 'Accept', cookie: 'false' },
          '$value': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7'
        },
        {
          attributes: { key: 'proxy-ip', cookie: 'false' },
          '$value': '************'
        },
        {
          attributes: { key: 'browserColorDepth', cookie: 'false' },
          '$value': '24'
        },
        {
          attributes: { key: 'browserScreenHeight', cookie: 'false' },
          '$value': '1050'
        },
        {
          attributes: { key: 'browserScreenWidth', cookie: 'false' },
          '$value': '1680'
        }
      ]
    },
    extensionParams: undefined,
    additionalParams: {
      param: [
        { attributes: { key: 'shipAddrState' }, '$value': 'NSW' },
        { attributes: { key: 'shipAddrCity' }, '$value': 'Sydney' },
        { attributes: { key: 'shipAddrCountry' }, '$value': '036' },
        { attributes: { key: 'shipAddrLine1' }, '$value': 'Unit 1' },
        {
          attributes: { key: 'shipAddrLine2' },
          '$value': '123 Street'
        },
        { attributes: { key: 'shipAddrPostCode' }, '$value': '2000' },
        { attributes: { key: 'billAddrCity' }, '$value': 'Sydney' },
        { attributes: { key: 'billAddrCountry' }, '$value': '036' },
        { attributes: { key: 'billAddrLine1' }, '$value': 'Unit 1' },
        {
          attributes: { key: 'billAddrLine2' },
          '$value': '123 Street'
        },
        { attributes: { key: 'billAddrState' }, '$value': 'NSW' },
        { attributes: { key: 'billAddrPostCode' }, '$value': '2000' },
        { attributes: { key: 'threeDSCompInd' }, '$value': 'U' },
        {
          attributes: { key: 'threeDSRequestorAuthenticationInd' },
          '$value': '01'
        },
        {
          attributes: { key: 'threeDSRequestorChallengeInd' },
          '$value': '01'
        },
        { attributes: { key: 'addrMatch' }, '$value': 'Y' },
        { attributes: { key: 'cardExpiryDate' }, '$value': '2508' },
        { attributes: { key: 'acctID' }, '$value': 'personal account' },
        { attributes: { key: 'email' }, '$value': '<EMAIL>' },
        { attributes: { key: 'mobilePhone.cc' }, '$value': '61' },
        {
          attributes: { key: 'mobilePhone.subscriber' },
          '$value': '**********'
        }
      ]
    }
  },
  headers: undefined
}
[2025-05-09T08:00:20.086Z] Parsed input data: {
  card: {
    id: undefined,
    number: '****************',
    type: 'VbV',
    context_Blob: undefined
  },
  transaction: {
    purchaseAmount: '10000',
    purchaseExponent: '2',
    purchaseCurrency: '036',
    purchaseDate: '2023-06-27T11:26:38.000Z',
    merchantId: '***************',
    merchantName: 'Test Merchant',
    merchantCountry: '840',
    acqBin: '***********',
    theeDSProtocolVersion: '2.1.0',
    cardExpiry: '2508',
    issuerName: 'remote',
    acsTransId: '36e6c66a-3dc6-4a59-a722-96c718824981',
    threeDSTransId: 'e6e9bf9b-af70-4f20-ad3a-52ce69de1c15',
    dsTransId: '2d5e6910-4d86-4d84-b6a3-9125b80b9ba1',
    threeDSRequestorID: '*********.visa',
    threeDSRequestorName: '3dsclient.local.visa',
    threeDSServerRefNumber: '3DS_LOA_SER_GPPL_020100_00075',
    threeDSServerOperatorID: '1jpeeLAWgGFgS1Ri9tX9',
    threeDSRequestorURL: 'http://gpayments.com',
    threeDSServerURL: 'https://amir-test3.testlab.3dsecure.cloud:9605/api/v2/ds/result/request',
    deviceChannel: '02',
    dsReferenceNumber: '3DS_LOA_ACS_GPPL_020200_00442',
    payTokenInd: '0',
    mcc: '2020',
    messageCategory: '01',
    transType: '01',
    acctType: '03',
    threeDSRequestorAuthenticationInd: '01'
  },
  headerParams: {
    param: [
      {
        attributes: { key: 'browserJavaEnabled', cookie: 'false' },
        '$value': 'false'
      },
      {
        attributes: { key: 'browserTZ', cookie: 'false' },
        '$value': '-180'
      },
      {
        attributes: { key: 'browserLanguage', cookie: 'false' },
        '$value': 'en-US'
      },
      {
        attributes: { key: 'Accept-Language', cookie: 'false' },
        '$value': 'en-US'
      },
      {
        attributes: { key: 'User-Agent', cookie: 'false' },
        '$value': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
      },
      {
        attributes: { key: 'Accept', cookie: 'false' },
        '$value': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7'
      },
      {
        attributes: { key: 'proxy-ip', cookie: 'false' },
        '$value': '************'
      },
      {
        attributes: { key: 'browserColorDepth', cookie: 'false' },
        '$value': '24'
      },
      {
        attributes: { key: 'browserScreenHeight', cookie: 'false' },
        '$value': '1050'
      },
      {
        attributes: { key: 'browserScreenWidth', cookie: 'false' },
        '$value': '1680'
      }
    ]
  },
  extensionParams: undefined,
  additionalParams: {
    param: [
      { attributes: { key: 'shipAddrState' }, '$value': 'NSW' },
      { attributes: { key: 'shipAddrCity' }, '$value': 'Sydney' },
      { attributes: { key: 'shipAddrCountry' }, '$value': '036' },
      { attributes: { key: 'shipAddrLine1' }, '$value': 'Unit 1' },
      { attributes: { key: 'shipAddrLine2' }, '$value': '123 Street' },
      { attributes: { key: 'shipAddrPostCode' }, '$value': '2000' },
      { attributes: { key: 'billAddrCity' }, '$value': 'Sydney' },
      { attributes: { key: 'billAddrCountry' }, '$value': '036' },
      { attributes: { key: 'billAddrLine1' }, '$value': 'Unit 1' },
      { attributes: { key: 'billAddrLine2' }, '$value': '123 Street' },
      { attributes: { key: 'billAddrState' }, '$value': 'NSW' },
      { attributes: { key: 'billAddrPostCode' }, '$value': '2000' },
      { attributes: { key: 'threeDSCompInd' }, '$value': 'U' },
      {
        attributes: { key: 'threeDSRequestorAuthenticationInd' },
        '$value': '01'
      },
      {
        attributes: { key: 'threeDSRequestorChallengeInd' },
        '$value': '01'
      },
      { attributes: { key: 'addrMatch' }, '$value': 'Y' },
      { attributes: { key: 'cardExpiryDate' }, '$value': '2508' },
      { attributes: { key: 'acctID' }, '$value': 'personal account' },
      { attributes: { key: 'email' }, '$value': '<EMAIL>' },
      { attributes: { key: 'mobilePhone.cc' }, '$value': '61' },
      {
        attributes: { key: 'mobilePhone.subscriber' },
        '$value': '**********'
      }
    ]
  }
}
[2025-05-09T08:00:20.089Z] getCardKey input: {
  card: {
    id: undefined,
    number: '****************',
    type: 'VbV',
    context_Blob: undefined
  },
  transaction: {
    purchaseAmount: '10000',
    purchaseExponent: '2',
    purchaseCurrency: '036',
    purchaseDate: '2023-06-27T11:26:38.000Z',
    merchantId: '***************',
    merchantName: 'Test Merchant',
    merchantCountry: '840',
    acqBin: '***********',
    theeDSProtocolVersion: '2.1.0',
    cardExpiry: '2508',
    issuerName: 'remote',
    acsTransId: '36e6c66a-3dc6-4a59-a722-96c718824981',
    threeDSTransId: 'e6e9bf9b-af70-4f20-ad3a-52ce69de1c15',
    dsTransId: '2d5e6910-4d86-4d84-b6a3-9125b80b9ba1',
    threeDSRequestorID: '*********.visa',
    threeDSRequestorName: '3dsclient.local.visa',
    threeDSServerRefNumber: '3DS_LOA_SER_GPPL_020100_00075',
    threeDSServerOperatorID: '1jpeeLAWgGFgS1Ri9tX9',
    threeDSRequestorURL: 'http://gpayments.com',
    threeDSServerURL: 'https://amir-test3.testlab.3dsecure.cloud:9605/api/v2/ds/result/request',
    deviceChannel: '02',
    dsReferenceNumber: '3DS_LOA_ACS_GPPL_020200_00442',
    payTokenInd: '0',
    mcc: '2020',
    messageCategory: '01',
    transType: '01',
    acctType: '03',
    threeDSRequestorAuthenticationInd: '01'
  },
  headerParams: {
    param: [
      {
        attributes: { key: 'browserJavaEnabled', cookie: 'false' },
        '$value': 'false'
      },
      {
        attributes: { key: 'browserTZ', cookie: 'false' },
        '$value': '-180'
      },
      {
        attributes: { key: 'browserLanguage', cookie: 'false' },
        '$value': 'en-US'
      },
      {
        attributes: { key: 'Accept-Language', cookie: 'false' },
        '$value': 'en-US'
      },
      {
        attributes: { key: 'User-Agent', cookie: 'false' },
        '$value': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
      },
      {
        attributes: { key: 'Accept', cookie: 'false' },
        '$value': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7'
      },
      {
        attributes: { key: 'proxy-ip', cookie: 'false' },
        '$value': '************'
      },
      {
        attributes: { key: 'browserColorDepth', cookie: 'false' },
        '$value': '24'
      },
      {
        attributes: { key: 'browserScreenHeight', cookie: 'false' },
        '$value': '1050'
      },
      {
        attributes: { key: 'browserScreenWidth', cookie: 'false' },
        '$value': '1680'
      }
    ]
  },
  extensionParams: undefined,
  additionalParams: {
    param: [
      { attributes: { key: 'shipAddrState' }, '$value': 'NSW' },
      { attributes: { key: 'shipAddrCity' }, '$value': 'Sydney' },
      { attributes: { key: 'shipAddrCountry' }, '$value': '036' },
      { attributes: { key: 'shipAddrLine1' }, '$value': 'Unit 1' },
      { attributes: { key: 'shipAddrLine2' }, '$value': '123 Street' },
      { attributes: { key: 'shipAddrPostCode' }, '$value': '2000' },
      { attributes: { key: 'billAddrCity' }, '$value': 'Sydney' },
      { attributes: { key: 'billAddrCountry' }, '$value': '036' },
      { attributes: { key: 'billAddrLine1' }, '$value': 'Unit 1' },
      { attributes: { key: 'billAddrLine2' }, '$value': '123 Street' },
      { attributes: { key: 'billAddrState' }, '$value': 'NSW' },
      { attributes: { key: 'billAddrPostCode' }, '$value': '2000' },
      { attributes: { key: 'threeDSCompInd' }, '$value': 'U' },
      {
        attributes: { key: 'threeDSRequestorAuthenticationInd' },
        '$value': '01'
      },
      {
        attributes: { key: 'threeDSRequestorChallengeInd' },
        '$value': '01'
      },
      { attributes: { key: 'addrMatch' }, '$value': 'Y' },
      { attributes: { key: 'cardExpiryDate' }, '$value': '2508' },
      { attributes: { key: 'acctID' }, '$value': 'personal account' },
      { attributes: { key: 'email' }, '$value': '<EMAIL>' },
      { attributes: { key: 'mobilePhone.cc' }, '$value': '61' },
      {
        attributes: { key: 'mobilePhone.subscriber' },
        '$value': '**********'
      }
    ]
  }
}
[2025-05-09T08:00:20.092Z] Card details: { original: '****************', masked: '446614******6104' }
[2025-05-09T08:00:20.247Z] Card found: { cardKey: 'KDR0020100057072' }
[2025-05-09T08:00:21.095Z] Card details retrieved: {
  cardKey: 'KDR0020100057072',
  cardDetails: {
    cardHash: 'FBBB71A8EC741FB001ADDCD140F6141FEEB4708BA9D515C8014828373E96B17C86F0DA62E45FDA39AF93E443D939864F39E3D69C88E4C9A23F610692B7250AC7',
    cardMask: '446614******6104',
    cardKey: 'KDR0020100057072',
    cardUuid: '492bea20-20f0-11f0-4c29-6f786f726131',
    expDate: '04/2028',
    issueDate: '2025-04-24',
    status: 'ACTIVE',
    statusCode: '0',
    terminating: false,
    productCode: 'R002',
    productDesc: 'RYVYL VISA CONSUMER DEBIT VTL',
    holder: 'RYVL-742897',
    accounts: [
      {
        accNo: '**********************',
        primary: true,
        currencyCode: '978',
        currencyName: 'EUR'
      }
    ],
    embossName1: 'TRENT ARNOLD',
    source: 'NEW',
    kind: 'DEBIT',
    main: false,
    limits: [],
    visual: 'R002',
    tokenized: false,
    delivery: {
      deliveryType: 'NONE',
      deliveryBranch: 'DHL',
      oneTimeDeliveryBranch: 'DHL'
    },
    contactless: true,
    cardTechnologyMode: 'VIRTUAL',
    creationDate: '2025-04-24',
    autoRenewal: true
  }
}
[2025-05-09T08:00:21.096Z] verifyReg response: {
  verifyRegResp: '{"cardInfo":{"card_ID":"****************","context_Blob":"xyz123","regStatus":0,"authRequired":1,"authTypeSup":[1,2],"lanCode":"","twoFA":true},"code":1,"errorMessage":"Card found","errorDetail":"Card info fetched successfully"}'
}
[2025-05-09T08:01:52.982Z] verifyReg called: {
  args: {
    card: {
      id: undefined,
      number: '****************',
      type: 'VbV',
      context_Blob: undefined
    },
    transaction: {
      purchaseAmount: '10000',
      purchaseExponent: '2',
      purchaseCurrency: '036',
      purchaseDate: '2023-06-27T11:26:38.000Z',
      merchantId: '***************',
      merchantName: 'Test Merchant',
      merchantCountry: '840',
      acqBin: '***********',
      theeDSProtocolVersion: '2.1.0',
      cardExpiry: '2508',
      issuerName: 'remote',
      acsTransId: '36e6c66a-3dc6-4a59-a722-96c718824981',
      threeDSTransId: 'e6e9bf9b-af70-4f20-ad3a-52ce69de1c15',
      dsTransId: '2d5e6910-4d86-4d84-b6a3-9125b80b9ba1',
      threeDSRequestorID: '*********.visa',
      threeDSRequestorName: '3dsclient.local.visa',
      threeDSServerRefNumber: '3DS_LOA_SER_GPPL_020100_00075',
      threeDSServerOperatorID: '1jpeeLAWgGFgS1Ri9tX9',
      threeDSRequestorURL: 'http://gpayments.com',
      threeDSServerURL: 'https://amir-test3.testlab.3dsecure.cloud:9605/api/v2/ds/result/request',
      deviceChannel: '02',
      dsReferenceNumber: '3DS_LOA_ACS_GPPL_020200_00442',
      payTokenInd: '0',
      mcc: '2020',
      messageCategory: '01',
      transType: '01',
      acctType: '03',
      threeDSRequestorAuthenticationInd: '01'
    },
    headerParams: {
      param: [
        {
          attributes: { key: 'browserJavaEnabled', cookie: 'false' },
          '$value': 'false'
        },
        {
          attributes: { key: 'browserTZ', cookie: 'false' },
          '$value': '-180'
        },
        {
          attributes: { key: 'browserLanguage', cookie: 'false' },
          '$value': 'en-US'
        },
        {
          attributes: { key: 'Accept-Language', cookie: 'false' },
          '$value': 'en-US'
        },
        {
          attributes: { key: 'User-Agent', cookie: 'false' },
          '$value': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        },
        {
          attributes: { key: 'Accept', cookie: 'false' },
          '$value': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7'
        },
        {
          attributes: { key: 'proxy-ip', cookie: 'false' },
          '$value': '************'
        },
        {
          attributes: { key: 'browserColorDepth', cookie: 'false' },
          '$value': '24'
        },
        {
          attributes: { key: 'browserScreenHeight', cookie: 'false' },
          '$value': '1050'
        },
        {
          attributes: { key: 'browserScreenWidth', cookie: 'false' },
          '$value': '1680'
        }
      ]
    },
    extensionParams: undefined,
    additionalParams: {
      param: [
        { attributes: { key: 'shipAddrState' }, '$value': 'NSW' },
        { attributes: { key: 'shipAddrCity' }, '$value': 'Sydney' },
        { attributes: { key: 'shipAddrCountry' }, '$value': '036' },
        { attributes: { key: 'shipAddrLine1' }, '$value': 'Unit 1' },
        {
          attributes: { key: 'shipAddrLine2' },
          '$value': '123 Street'
        },
        { attributes: { key: 'shipAddrPostCode' }, '$value': '2000' },
        { attributes: { key: 'billAddrCity' }, '$value': 'Sydney' },
        { attributes: { key: 'billAddrCountry' }, '$value': '036' },
        { attributes: { key: 'billAddrLine1' }, '$value': 'Unit 1' },
        {
          attributes: { key: 'billAddrLine2' },
          '$value': '123 Street'
        },
        { attributes: { key: 'billAddrState' }, '$value': 'NSW' },
        { attributes: { key: 'billAddrPostCode' }, '$value': '2000' },
        { attributes: { key: 'threeDSCompInd' }, '$value': 'U' },
        {
          attributes: { key: 'threeDSRequestorAuthenticationInd' },
          '$value': '01'
        },
        {
          attributes: { key: 'threeDSRequestorChallengeInd' },
          '$value': '01'
        },
        { attributes: { key: 'addrMatch' }, '$value': 'Y' },
        { attributes: { key: 'cardExpiryDate' }, '$value': '2508' },
        { attributes: { key: 'acctID' }, '$value': 'personal account' },
        { attributes: { key: 'email' }, '$value': '<EMAIL>' },
        { attributes: { key: 'mobilePhone.cc' }, '$value': '61' },
        {
          attributes: { key: 'mobilePhone.subscriber' },
          '$value': '**********'
        }
      ]
    }
  },
  headers: undefined
}
[2025-05-09T08:01:52.984Z] Parsed input data: {
  card: {
    id: undefined,
    number: '****************',
    type: 'VbV',
    context_Blob: undefined
  },
  transaction: {
    purchaseAmount: '10000',
    purchaseExponent: '2',
    purchaseCurrency: '036',
    purchaseDate: '2023-06-27T11:26:38.000Z',
    merchantId: '***************',
    merchantName: 'Test Merchant',
    merchantCountry: '840',
    acqBin: '***********',
    theeDSProtocolVersion: '2.1.0',
    cardExpiry: '2508',
    issuerName: 'remote',
    acsTransId: '36e6c66a-3dc6-4a59-a722-96c718824981',
    threeDSTransId: 'e6e9bf9b-af70-4f20-ad3a-52ce69de1c15',
    dsTransId: '2d5e6910-4d86-4d84-b6a3-9125b80b9ba1',
    threeDSRequestorID: '*********.visa',
    threeDSRequestorName: '3dsclient.local.visa',
    threeDSServerRefNumber: '3DS_LOA_SER_GPPL_020100_00075',
    threeDSServerOperatorID: '1jpeeLAWgGFgS1Ri9tX9',
    threeDSRequestorURL: 'http://gpayments.com',
    threeDSServerURL: 'https://amir-test3.testlab.3dsecure.cloud:9605/api/v2/ds/result/request',
    deviceChannel: '02',
    dsReferenceNumber: '3DS_LOA_ACS_GPPL_020200_00442',
    payTokenInd: '0',
    mcc: '2020',
    messageCategory: '01',
    transType: '01',
    acctType: '03',
    threeDSRequestorAuthenticationInd: '01'
  },
  headerParams: {
    param: [
      {
        attributes: { key: 'browserJavaEnabled', cookie: 'false' },
        '$value': 'false'
      },
      {
        attributes: { key: 'browserTZ', cookie: 'false' },
        '$value': '-180'
      },
      {
        attributes: { key: 'browserLanguage', cookie: 'false' },
        '$value': 'en-US'
      },
      {
        attributes: { key: 'Accept-Language', cookie: 'false' },
        '$value': 'en-US'
      },
      {
        attributes: { key: 'User-Agent', cookie: 'false' },
        '$value': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
      },
      {
        attributes: { key: 'Accept', cookie: 'false' },
        '$value': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7'
      },
      {
        attributes: { key: 'proxy-ip', cookie: 'false' },
        '$value': '************'
      },
      {
        attributes: { key: 'browserColorDepth', cookie: 'false' },
        '$value': '24'
      },
      {
        attributes: { key: 'browserScreenHeight', cookie: 'false' },
        '$value': '1050'
      },
      {
        attributes: { key: 'browserScreenWidth', cookie: 'false' },
        '$value': '1680'
      }
    ]
  },
  extensionParams: undefined,
  additionalParams: {
    param: [
      { attributes: { key: 'shipAddrState' }, '$value': 'NSW' },
      { attributes: { key: 'shipAddrCity' }, '$value': 'Sydney' },
      { attributes: { key: 'shipAddrCountry' }, '$value': '036' },
      { attributes: { key: 'shipAddrLine1' }, '$value': 'Unit 1' },
      { attributes: { key: 'shipAddrLine2' }, '$value': '123 Street' },
      { attributes: { key: 'shipAddrPostCode' }, '$value': '2000' },
      { attributes: { key: 'billAddrCity' }, '$value': 'Sydney' },
      { attributes: { key: 'billAddrCountry' }, '$value': '036' },
      { attributes: { key: 'billAddrLine1' }, '$value': 'Unit 1' },
      { attributes: { key: 'billAddrLine2' }, '$value': '123 Street' },
      { attributes: { key: 'billAddrState' }, '$value': 'NSW' },
      { attributes: { key: 'billAddrPostCode' }, '$value': '2000' },
      { attributes: { key: 'threeDSCompInd' }, '$value': 'U' },
      {
        attributes: { key: 'threeDSRequestorAuthenticationInd' },
        '$value': '01'
      },
      {
        attributes: { key: 'threeDSRequestorChallengeInd' },
        '$value': '01'
      },
      { attributes: { key: 'addrMatch' }, '$value': 'Y' },
      { attributes: { key: 'cardExpiryDate' }, '$value': '2508' },
      { attributes: { key: 'acctID' }, '$value': 'personal account' },
      { attributes: { key: 'email' }, '$value': '<EMAIL>' },
      { attributes: { key: 'mobilePhone.cc' }, '$value': '61' },
      {
        attributes: { key: 'mobilePhone.subscriber' },
        '$value': '**********'
      }
    ]
  }
}
[2025-05-09T08:01:52.985Z] getCardKey input: {
  card: {
    id: undefined,
    number: '****************',
    type: 'VbV',
    context_Blob: undefined
  },
  transaction: {
    purchaseAmount: '10000',
    purchaseExponent: '2',
    purchaseCurrency: '036',
    purchaseDate: '2023-06-27T11:26:38.000Z',
    merchantId: '***************',
    merchantName: 'Test Merchant',
    merchantCountry: '840',
    acqBin: '***********',
    theeDSProtocolVersion: '2.1.0',
    cardExpiry: '2508',
    issuerName: 'remote',
    acsTransId: '36e6c66a-3dc6-4a59-a722-96c718824981',
    threeDSTransId: 'e6e9bf9b-af70-4f20-ad3a-52ce69de1c15',
    dsTransId: '2d5e6910-4d86-4d84-b6a3-9125b80b9ba1',
    threeDSRequestorID: '*********.visa',
    threeDSRequestorName: '3dsclient.local.visa',
    threeDSServerRefNumber: '3DS_LOA_SER_GPPL_020100_00075',
    threeDSServerOperatorID: '1jpeeLAWgGFgS1Ri9tX9',
    threeDSRequestorURL: 'http://gpayments.com',
    threeDSServerURL: 'https://amir-test3.testlab.3dsecure.cloud:9605/api/v2/ds/result/request',
    deviceChannel: '02',
    dsReferenceNumber: '3DS_LOA_ACS_GPPL_020200_00442',
    payTokenInd: '0',
    mcc: '2020',
    messageCategory: '01',
    transType: '01',
    acctType: '03',
    threeDSRequestorAuthenticationInd: '01'
  },
  headerParams: {
    param: [
      {
        attributes: { key: 'browserJavaEnabled', cookie: 'false' },
        '$value': 'false'
      },
      {
        attributes: { key: 'browserTZ', cookie: 'false' },
        '$value': '-180'
      },
      {
        attributes: { key: 'browserLanguage', cookie: 'false' },
        '$value': 'en-US'
      },
      {
        attributes: { key: 'Accept-Language', cookie: 'false' },
        '$value': 'en-US'
      },
      {
        attributes: { key: 'User-Agent', cookie: 'false' },
        '$value': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
      },
      {
        attributes: { key: 'Accept', cookie: 'false' },
        '$value': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7'
      },
      {
        attributes: { key: 'proxy-ip', cookie: 'false' },
        '$value': '************'
      },
      {
        attributes: { key: 'browserColorDepth', cookie: 'false' },
        '$value': '24'
      },
      {
        attributes: { key: 'browserScreenHeight', cookie: 'false' },
        '$value': '1050'
      },
      {
        attributes: { key: 'browserScreenWidth', cookie: 'false' },
        '$value': '1680'
      }
    ]
  },
  extensionParams: undefined,
  additionalParams: {
    param: [
      { attributes: { key: 'shipAddrState' }, '$value': 'NSW' },
      { attributes: { key: 'shipAddrCity' }, '$value': 'Sydney' },
      { attributes: { key: 'shipAddrCountry' }, '$value': '036' },
      { attributes: { key: 'shipAddrLine1' }, '$value': 'Unit 1' },
      { attributes: { key: 'shipAddrLine2' }, '$value': '123 Street' },
      { attributes: { key: 'shipAddrPostCode' }, '$value': '2000' },
      { attributes: { key: 'billAddrCity' }, '$value': 'Sydney' },
      { attributes: { key: 'billAddrCountry' }, '$value': '036' },
      { attributes: { key: 'billAddrLine1' }, '$value': 'Unit 1' },
      { attributes: { key: 'billAddrLine2' }, '$value': '123 Street' },
      { attributes: { key: 'billAddrState' }, '$value': 'NSW' },
      { attributes: { key: 'billAddrPostCode' }, '$value': '2000' },
      { attributes: { key: 'threeDSCompInd' }, '$value': 'U' },
      {
        attributes: { key: 'threeDSRequestorAuthenticationInd' },
        '$value': '01'
      },
      {
        attributes: { key: 'threeDSRequestorChallengeInd' },
        '$value': '01'
      },
      { attributes: { key: 'addrMatch' }, '$value': 'Y' },
      { attributes: { key: 'cardExpiryDate' }, '$value': '2508' },
      { attributes: { key: 'acctID' }, '$value': 'personal account' },
      { attributes: { key: 'email' }, '$value': '<EMAIL>' },
      { attributes: { key: 'mobilePhone.cc' }, '$value': '61' },
      {
        attributes: { key: 'mobilePhone.subscriber' },
        '$value': '**********'
      }
    ]
  }
}
[2025-05-09T08:01:52.987Z] Card details: { original: '****************', masked: '446614******6104' }
[2025-05-09T08:01:53.352Z] Card found: { cardKey: 'KDR0020100057072' }
[2025-05-09T08:01:55.605Z] Card details retrieved: {
  cardKey: 'KDR0020100057072',
  cardDetails: {
    cardHash: 'FBBB71A8EC741FB001ADDCD140F6141FEEB4708BA9D515C8014828373E96B17C86F0DA62E45FDA39AF93E443D939864F39E3D69C88E4C9A23F610692B7250AC7',
    cardMask: '446614******6104',
    cardKey: 'KDR0020100057072',
    cardUuid: '492bea20-20f0-11f0-4c29-6f786f726131',
    expDate: '04/2028',
    issueDate: '2025-04-24',
    status: 'ACTIVE',
    statusCode: '0',
    terminating: false,
    productCode: 'R002',
    productDesc: 'RYVYL VISA CONSUMER DEBIT VTL',
    holder: 'RYVL-742897',
    accounts: [
      {
        accNo: '**********************',
        primary: true,
        currencyCode: '978',
        currencyName: 'EUR'
      }
    ],
    embossName1: 'TRENT ARNOLD',
    source: 'NEW',
    kind: 'DEBIT',
    main: false,
    limits: [],
    visual: 'R002',
    tokenized: false,
    delivery: {
      deliveryType: 'NONE',
      deliveryBranch: 'DHL',
      oneTimeDeliveryBranch: 'DHL'
    },
    contactless: true,
    cardTechnologyMode: 'VIRTUAL',
    creationDate: '2025-04-24',
    autoRenewal: true
  }
}
[2025-05-09T08:01:55.607Z] verifyReg response: {
  verifyRegResp: '{"cardInfo":{"card_ID":"****************","context_Blob":"xyz123","regStatus":0,"authRequired":1,"authTypeSup":[1,2],"lanCode":"","twoFA":true},"code":1,"errorMessage":"Card found","errorDetail":"Card info fetched successfully"}'
}
[2025-05-09T08:06:30.222Z] verifyReg called: {
  args: {
    card: {
      id: null,
      number: '****************',
      type: 'VbV',
      context_Blob: null
    },
    transaction: {
      purchaseAmount: '10000',
      purchaseExponent: '2',
      purchaseCurrency: '036',
      purchaseDate: '2023-06-27T11:26:38.000Z',
      merchantId: '***************',
      merchantName: 'Test Merchant',
      merchantCountry: '840',
      acqBin: '***********',
      theeDSProtocolVersion: '2.1.0',
      cardExpiry: '2508',
      issuerName: 'remote',
      acsTransId: '36e6c66a-3dc6-4a59-a722-96c718824981',
      threeDSTransId: 'e6e9bf9b-af70-4f20-ad3a-52ce69de1c15',
      dsTransId: '2d5e6910-4d86-4d84-b6a3-9125b80b9ba1',
      threeDSRequestorID: '*********.visa',
      threeDSRequestorName: '3dsclient.local.visa',
      threeDSServerRefNumber: '3DS_LOA_SER_GPPL_020100_00075',
      threeDSServerOperatorID: '1jpeeLAWgGFgS1Ri9tX9',
      threeDSRequestorURL: 'http://gpayments.com',
      threeDSServerURL: 'https://amir-test3.testlab.3dsecure.cloud:9605/api/v2/ds/result/request',
      deviceChannel: '02',
      dsReferenceNumber: '3DS_LOA_ACS_GPPL_020200_00442',
      payTokenInd: '0',
      mcc: '2020',
      messageCategory: '01',
      transType: '01',
      acctType: '03',
      threeDSRequestorAuthenticationInd: '01'
    },
    headerParams: {
      param: [
        {
          attributes: { key: 'browserJavaEnabled', cookie: 'false' },
          '$value': 'false'
        },
        {
          attributes: { key: 'browserTZ', cookie: 'false' },
          '$value': '-180'
        },
        {
          attributes: { key: 'browserLanguage', cookie: 'false' },
          '$value': 'en-US'
        },
        {
          attributes: { key: 'Accept-Language', cookie: 'false' },
          '$value': 'en-US'
        },
        {
          attributes: { key: 'User-Agent', cookie: 'false' },
          '$value': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        },
        {
          attributes: { key: 'Accept', cookie: 'false' },
          '$value': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7'
        },
        {
          attributes: { key: 'proxy-ip', cookie: 'false' },
          '$value': '************'
        },
        {
          attributes: { key: 'browserColorDepth', cookie: 'false' },
          '$value': '24'
        },
        {
          attributes: { key: 'browserScreenHeight', cookie: 'false' },
          '$value': '1050'
        },
        {
          attributes: { key: 'browserScreenWidth', cookie: 'false' },
          '$value': '1680'
        }
      ]
    },
    extensionParams: null,
    additionalParams: {
      param: [
        { attributes: { key: 'shipAddrState' }, '$value': 'NSW' },
        { attributes: { key: 'shipAddrCity' }, '$value': 'Sydney' },
        { attributes: { key: 'shipAddrCountry' }, '$value': '036' },
        { attributes: { key: 'shipAddrLine1' }, '$value': 'Unit 1' },
        {
          attributes: { key: 'shipAddrLine2' },
          '$value': '123 Street'
        },
        { attributes: { key: 'shipAddrPostCode' }, '$value': '2000' },
        { attributes: { key: 'billAddrCity' }, '$value': 'Sydney' },
        { attributes: { key: 'billAddrCountry' }, '$value': '036' },
        { attributes: { key: 'billAddrLine1' }, '$value': 'Unit 1' },
        {
          attributes: { key: 'billAddrLine2' },
          '$value': '123 Street'
        },
        { attributes: { key: 'billAddrState' }, '$value': 'NSW' },
        { attributes: { key: 'billAddrPostCode' }, '$value': '2000' },
        { attributes: { key: 'threeDSCompInd' }, '$value': 'U' },
        {
          attributes: { key: 'threeDSRequestorAuthenticationInd' },
          '$value': '01'
        },
        {
          attributes: { key: 'threeDSRequestorChallengeInd' },
          '$value': '01'
        },
        { attributes: { key: 'addrMatch' }, '$value': 'Y' },
        { attributes: { key: 'cardExpiryDate' }, '$value': '2508' },
        { attributes: { key: 'acctID' }, '$value': 'personal account' },
        { attributes: { key: 'email' }, '$value': '<EMAIL>' },
        { attributes: { key: 'mobilePhone.cc' }, '$value': '61' },
        {
          attributes: { key: 'mobilePhone.subscriber' },
          '$value': '**********'
        }
      ]
    }
  },
  headers: undefined
}
[2025-05-09T08:06:30.224Z] Parsed input data: {
  card: {
    id: null,
    number: '****************',
    type: 'VbV',
    context_Blob: null
  },
  transaction: {
    purchaseAmount: '10000',
    purchaseExponent: '2',
    purchaseCurrency: '036',
    purchaseDate: '2023-06-27T11:26:38.000Z',
    merchantId: '***************',
    merchantName: 'Test Merchant',
    merchantCountry: '840',
    acqBin: '***********',
    theeDSProtocolVersion: '2.1.0',
    cardExpiry: '2508',
    issuerName: 'remote',
    acsTransId: '36e6c66a-3dc6-4a59-a722-96c718824981',
    threeDSTransId: 'e6e9bf9b-af70-4f20-ad3a-52ce69de1c15',
    dsTransId: '2d5e6910-4d86-4d84-b6a3-9125b80b9ba1',
    threeDSRequestorID: '*********.visa',
    threeDSRequestorName: '3dsclient.local.visa',
    threeDSServerRefNumber: '3DS_LOA_SER_GPPL_020100_00075',
    threeDSServerOperatorID: '1jpeeLAWgGFgS1Ri9tX9',
    threeDSRequestorURL: 'http://gpayments.com',
    threeDSServerURL: 'https://amir-test3.testlab.3dsecure.cloud:9605/api/v2/ds/result/request',
    deviceChannel: '02',
    dsReferenceNumber: '3DS_LOA_ACS_GPPL_020200_00442',
    payTokenInd: '0',
    mcc: '2020',
    messageCategory: '01',
    transType: '01',
    acctType: '03',
    threeDSRequestorAuthenticationInd: '01'
  },
  headerParams: {
    param: [
      {
        attributes: { key: 'browserJavaEnabled', cookie: 'false' },
        '$value': 'false'
      },
      {
        attributes: { key: 'browserTZ', cookie: 'false' },
        '$value': '-180'
      },
      {
        attributes: { key: 'browserLanguage', cookie: 'false' },
        '$value': 'en-US'
      },
      {
        attributes: { key: 'Accept-Language', cookie: 'false' },
        '$value': 'en-US'
      },
      {
        attributes: { key: 'User-Agent', cookie: 'false' },
        '$value': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
      },
      {
        attributes: { key: 'Accept', cookie: 'false' },
        '$value': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7'
      },
      {
        attributes: { key: 'proxy-ip', cookie: 'false' },
        '$value': '************'
      },
      {
        attributes: { key: 'browserColorDepth', cookie: 'false' },
        '$value': '24'
      },
      {
        attributes: { key: 'browserScreenHeight', cookie: 'false' },
        '$value': '1050'
      },
      {
        attributes: { key: 'browserScreenWidth', cookie: 'false' },
        '$value': '1680'
      }
    ]
  },
  extensionParams: null,
  additionalParams: {
    param: [
      { attributes: { key: 'shipAddrState' }, '$value': 'NSW' },
      { attributes: { key: 'shipAddrCity' }, '$value': 'Sydney' },
      { attributes: { key: 'shipAddrCountry' }, '$value': '036' },
      { attributes: { key: 'shipAddrLine1' }, '$value': 'Unit 1' },
      { attributes: { key: 'shipAddrLine2' }, '$value': '123 Street' },
      { attributes: { key: 'shipAddrPostCode' }, '$value': '2000' },
      { attributes: { key: 'billAddrCity' }, '$value': 'Sydney' },
      { attributes: { key: 'billAddrCountry' }, '$value': '036' },
      { attributes: { key: 'billAddrLine1' }, '$value': 'Unit 1' },
      { attributes: { key: 'billAddrLine2' }, '$value': '123 Street' },
      { attributes: { key: 'billAddrState' }, '$value': 'NSW' },
      { attributes: { key: 'billAddrPostCode' }, '$value': '2000' },
      { attributes: { key: 'threeDSCompInd' }, '$value': 'U' },
      {
        attributes: { key: 'threeDSRequestorAuthenticationInd' },
        '$value': '01'
      },
      {
        attributes: { key: 'threeDSRequestorChallengeInd' },
        '$value': '01'
      },
      { attributes: { key: 'addrMatch' }, '$value': 'Y' },
      { attributes: { key: 'cardExpiryDate' }, '$value': '2508' },
      { attributes: { key: 'acctID' }, '$value': 'personal account' },
      { attributes: { key: 'email' }, '$value': '<EMAIL>' },
      { attributes: { key: 'mobilePhone.cc' }, '$value': '61' },
      {
        attributes: { key: 'mobilePhone.subscriber' },
        '$value': '**********'
      }
    ]
  }
}
[2025-05-09T08:06:30.225Z] getCardKey input: {
  card: {
    id: null,
    number: '****************',
    type: 'VbV',
    context_Blob: null
  },
  transaction: {
    purchaseAmount: '10000',
    purchaseExponent: '2',
    purchaseCurrency: '036',
    purchaseDate: '2023-06-27T11:26:38.000Z',
    merchantId: '***************',
    merchantName: 'Test Merchant',
    merchantCountry: '840',
    acqBin: '***********',
    theeDSProtocolVersion: '2.1.0',
    cardExpiry: '2508',
    issuerName: 'remote',
    acsTransId: '36e6c66a-3dc6-4a59-a722-96c718824981',
    threeDSTransId: 'e6e9bf9b-af70-4f20-ad3a-52ce69de1c15',
    dsTransId: '2d5e6910-4d86-4d84-b6a3-9125b80b9ba1',
    threeDSRequestorID: '*********.visa',
    threeDSRequestorName: '3dsclient.local.visa',
    threeDSServerRefNumber: '3DS_LOA_SER_GPPL_020100_00075',
    threeDSServerOperatorID: '1jpeeLAWgGFgS1Ri9tX9',
    threeDSRequestorURL: 'http://gpayments.com',
    threeDSServerURL: 'https://amir-test3.testlab.3dsecure.cloud:9605/api/v2/ds/result/request',
    deviceChannel: '02',
    dsReferenceNumber: '3DS_LOA_ACS_GPPL_020200_00442',
    payTokenInd: '0',
    mcc: '2020',
    messageCategory: '01',
    transType: '01',
    acctType: '03',
    threeDSRequestorAuthenticationInd: '01'
  },
  headerParams: {
    param: [
      {
        attributes: { key: 'browserJavaEnabled', cookie: 'false' },
        '$value': 'false'
      },
      {
        attributes: { key: 'browserTZ', cookie: 'false' },
        '$value': '-180'
      },
      {
        attributes: { key: 'browserLanguage', cookie: 'false' },
        '$value': 'en-US'
      },
      {
        attributes: { key: 'Accept-Language', cookie: 'false' },
        '$value': 'en-US'
      },
      {
        attributes: { key: 'User-Agent', cookie: 'false' },
        '$value': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
      },
      {
        attributes: { key: 'Accept', cookie: 'false' },
        '$value': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7'
      },
      {
        attributes: { key: 'proxy-ip', cookie: 'false' },
        '$value': '************'
      },
      {
        attributes: { key: 'browserColorDepth', cookie: 'false' },
        '$value': '24'
      },
      {
        attributes: { key: 'browserScreenHeight', cookie: 'false' },
        '$value': '1050'
      },
      {
        attributes: { key: 'browserScreenWidth', cookie: 'false' },
        '$value': '1680'
      }
    ]
  },
  extensionParams: null,
  additionalParams: {
    param: [
      { attributes: { key: 'shipAddrState' }, '$value': 'NSW' },
      { attributes: { key: 'shipAddrCity' }, '$value': 'Sydney' },
      { attributes: { key: 'shipAddrCountry' }, '$value': '036' },
      { attributes: { key: 'shipAddrLine1' }, '$value': 'Unit 1' },
      { attributes: { key: 'shipAddrLine2' }, '$value': '123 Street' },
      { attributes: { key: 'shipAddrPostCode' }, '$value': '2000' },
      { attributes: { key: 'billAddrCity' }, '$value': 'Sydney' },
      { attributes: { key: 'billAddrCountry' }, '$value': '036' },
      { attributes: { key: 'billAddrLine1' }, '$value': 'Unit 1' },
      { attributes: { key: 'billAddrLine2' }, '$value': '123 Street' },
      { attributes: { key: 'billAddrState' }, '$value': 'NSW' },
      { attributes: { key: 'billAddrPostCode' }, '$value': '2000' },
      { attributes: { key: 'threeDSCompInd' }, '$value': 'U' },
      {
        attributes: { key: 'threeDSRequestorAuthenticationInd' },
        '$value': '01'
      },
      {
        attributes: { key: 'threeDSRequestorChallengeInd' },
        '$value': '01'
      },
      { attributes: { key: 'addrMatch' }, '$value': 'Y' },
      { attributes: { key: 'cardExpiryDate' }, '$value': '2508' },
      { attributes: { key: 'acctID' }, '$value': 'personal account' },
      { attributes: { key: 'email' }, '$value': '<EMAIL>' },
      { attributes: { key: 'mobilePhone.cc' }, '$value': '61' },
      {
        attributes: { key: 'mobilePhone.subscriber' },
        '$value': '**********'
      }
    ]
  }
}
[2025-05-09T08:06:30.228Z] Card details: { original: '****************', masked: '446614******6104' }
[2025-05-09T08:06:31.275Z] Card found: { cardKey: 'KDR0020100057072' }
[2025-05-09T08:06:33.731Z] Card details retrieved: {
  cardKey: 'KDR0020100057072',
  cardDetails: {
    cardHash: 'FBBB71A8EC741FB001ADDCD140F6141FEEB4708BA9D515C8014828373E96B17C86F0DA62E45FDA39AF93E443D939864F39E3D69C88E4C9A23F610692B7250AC7',
    cardMask: '446614******6104',
    cardKey: 'KDR0020100057072',
    cardUuid: '492bea20-20f0-11f0-4c29-6f786f726131',
    expDate: '04/2028',
    issueDate: '2025-04-24',
    status: 'ACTIVE',
    statusCode: '0',
    terminating: false,
    productCode: 'R002',
    productDesc: 'RYVYL VISA CONSUMER DEBIT VTL',
    holder: 'RYVL-742897',
    accounts: [
      {
        accNo: '**********************',
        primary: true,
        currencyCode: '978',
        currencyName: 'EUR'
      }
    ],
    embossName1: 'TRENT ARNOLD',
    source: 'NEW',
    kind: 'DEBIT',
    main: false,
    limits: [],
    visual: 'R002',
    tokenized: false,
    delivery: {
      deliveryType: 'NONE',
      deliveryBranch: 'DHL',
      oneTimeDeliveryBranch: 'DHL'
    },
    contactless: true,
    cardTechnologyMode: 'VIRTUAL',
    creationDate: '2025-04-24',
    autoRenewal: true
  }
}
[2025-05-09T08:06:33.733Z] verifyReg response: {
  verifyRegResp: '{"cardInfo":{"card_ID":"****************","context_Blob":"xyz123","regStatus":0,"authRequired":1,"authTypeSup":[1,2],"lanCode":"","twoFA":true},"code":1,"errorMessage":"Card found","errorDetail":"Card info fetched successfully"}'
}
[2025-05-09T08:08:09.040Z] verifyReg called: {
  args: {
    card: {
      id: null,
      number: '****************',
      type: 'VbV',
      context_Blob: null
    },
    transaction: {
      purchaseAmount: '10000',
      purchaseExponent: '2',
      purchaseCurrency: '036',
      purchaseDate: '2023-06-27T11:26:38.000Z',
      merchantId: '***************',
      merchantName: 'Test Merchant',
      merchantCountry: '840',
      acqBin: '***********',
      theeDSProtocolVersion: '2.1.0',
      cardExpiry: '2508',
      issuerName: 'remote',
      acsTransId: '36e6c66a-3dc6-4a59-a722-96c718824981',
      threeDSTransId: 'e6e9bf9b-af70-4f20-ad3a-52ce69de1c15',
      dsTransId: '2d5e6910-4d86-4d84-b6a3-9125b80b9ba1',
      threeDSRequestorID: '*********.visa',
      threeDSRequestorName: '3dsclient.local.visa',
      threeDSServerRefNumber: '3DS_LOA_SER_GPPL_020100_00075',
      threeDSServerOperatorID: '1jpeeLAWgGFgS1Ri9tX9',
      threeDSRequestorURL: 'http://gpayments.com',
      threeDSServerURL: 'https://amir-test3.testlab.3dsecure.cloud:9605/api/v2/ds/result/request',
      deviceChannel: '02',
      dsReferenceNumber: '3DS_LOA_ACS_GPPL_020200_00442',
      payTokenInd: '0',
      mcc: '2020',
      messageCategory: '01',
      transType: '01',
      acctType: '03',
      threeDSRequestorAuthenticationInd: '01'
    },
    headerParams: {
      param: [
        {
          attributes: { key: 'browserJavaEnabled', cookie: 'false' },
          '$value': 'false'
        },
        {
          attributes: { key: 'browserTZ', cookie: 'false' },
          '$value': '-180'
        },
        {
          attributes: { key: 'browserLanguage', cookie: 'false' },
          '$value': 'en-US'
        },
        {
          attributes: { key: 'Accept-Language', cookie: 'false' },
          '$value': 'en-US'
        },
        {
          attributes: { key: 'User-Agent', cookie: 'false' },
          '$value': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        },
        {
          attributes: { key: 'Accept', cookie: 'false' },
          '$value': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7'
        },
        {
          attributes: { key: 'proxy-ip', cookie: 'false' },
          '$value': '************'
        },
        {
          attributes: { key: 'browserColorDepth', cookie: 'false' },
          '$value': '24'
        },
        {
          attributes: { key: 'browserScreenHeight', cookie: 'false' },
          '$value': '1050'
        },
        {
          attributes: { key: 'browserScreenWidth', cookie: 'false' },
          '$value': '1680'
        }
      ]
    },
    extensionParams: null,
    additionalParams: {
      param: [
        { attributes: { key: 'shipAddrState' }, '$value': 'NSW' },
        { attributes: { key: 'shipAddrCity' }, '$value': 'Sydney' },
        { attributes: { key: 'shipAddrCountry' }, '$value': '036' },
        { attributes: { key: 'shipAddrLine1' }, '$value': 'Unit 1' },
        {
          attributes: { key: 'shipAddrLine2' },
          '$value': '123 Street'
        },
        { attributes: { key: 'shipAddrPostCode' }, '$value': '2000' },
        { attributes: { key: 'billAddrCity' }, '$value': 'Sydney' },
        { attributes: { key: 'billAddrCountry' }, '$value': '036' },
        { attributes: { key: 'billAddrLine1' }, '$value': 'Unit 1' },
        {
          attributes: { key: 'billAddrLine2' },
          '$value': '123 Street'
        },
        { attributes: { key: 'billAddrState' }, '$value': 'NSW' },
        { attributes: { key: 'billAddrPostCode' }, '$value': '2000' },
        { attributes: { key: 'threeDSCompInd' }, '$value': 'U' },
        {
          attributes: { key: 'threeDSRequestorAuthenticationInd' },
          '$value': '01'
        },
        {
          attributes: { key: 'threeDSRequestorChallengeInd' },
          '$value': '01'
        },
        { attributes: { key: 'addrMatch' }, '$value': 'Y' },
        { attributes: { key: 'cardExpiryDate' }, '$value': '2508' },
        { attributes: { key: 'acctID' }, '$value': 'personal account' },
        { attributes: { key: 'email' }, '$value': '<EMAIL>' },
        { attributes: { key: 'mobilePhone.cc' }, '$value': '61' },
        {
          attributes: { key: 'mobilePhone.subscriber' },
          '$value': '**********'
        }
      ]
    }
  },
  headers: undefined
}
[2025-05-09T08:08:09.055Z] Parsed input data: {
  card: {
    id: null,
    number: '****************',
    type: 'VbV',
    context_Blob: null
  },
  transaction: {
    purchaseAmount: '10000',
    purchaseExponent: '2',
    purchaseCurrency: '036',
    purchaseDate: '2023-06-27T11:26:38.000Z',
    merchantId: '***************',
    merchantName: 'Test Merchant',
    merchantCountry: '840',
    acqBin: '***********',
    theeDSProtocolVersion: '2.1.0',
    cardExpiry: '2508',
    issuerName: 'remote',
    acsTransId: '36e6c66a-3dc6-4a59-a722-96c718824981',
    threeDSTransId: 'e6e9bf9b-af70-4f20-ad3a-52ce69de1c15',
    dsTransId: '2d5e6910-4d86-4d84-b6a3-9125b80b9ba1',
    threeDSRequestorID: '*********.visa',
    threeDSRequestorName: '3dsclient.local.visa',
    threeDSServerRefNumber: '3DS_LOA_SER_GPPL_020100_00075',
    threeDSServerOperatorID: '1jpeeLAWgGFgS1Ri9tX9',
    threeDSRequestorURL: 'http://gpayments.com',
    threeDSServerURL: 'https://amir-test3.testlab.3dsecure.cloud:9605/api/v2/ds/result/request',
    deviceChannel: '02',
    dsReferenceNumber: '3DS_LOA_ACS_GPPL_020200_00442',
    payTokenInd: '0',
    mcc: '2020',
    messageCategory: '01',
    transType: '01',
    acctType: '03',
    threeDSRequestorAuthenticationInd: '01'
  },
  headerParams: {
    param: [
      {
        attributes: { key: 'browserJavaEnabled', cookie: 'false' },
        '$value': 'false'
      },
      {
        attributes: { key: 'browserTZ', cookie: 'false' },
        '$value': '-180'
      },
      {
        attributes: { key: 'browserLanguage', cookie: 'false' },
        '$value': 'en-US'
      },
      {
        attributes: { key: 'Accept-Language', cookie: 'false' },
        '$value': 'en-US'
      },
      {
        attributes: { key: 'User-Agent', cookie: 'false' },
        '$value': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
      },
      {
        attributes: { key: 'Accept', cookie: 'false' },
        '$value': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7'
      },
      {
        attributes: { key: 'proxy-ip', cookie: 'false' },
        '$value': '************'
      },
      {
        attributes: { key: 'browserColorDepth', cookie: 'false' },
        '$value': '24'
      },
      {
        attributes: { key: 'browserScreenHeight', cookie: 'false' },
        '$value': '1050'
      },
      {
        attributes: { key: 'browserScreenWidth', cookie: 'false' },
        '$value': '1680'
      }
    ]
  },
  extensionParams: null,
  additionalParams: {
    param: [
      { attributes: { key: 'shipAddrState' }, '$value': 'NSW' },
      { attributes: { key: 'shipAddrCity' }, '$value': 'Sydney' },
      { attributes: { key: 'shipAddrCountry' }, '$value': '036' },
      { attributes: { key: 'shipAddrLine1' }, '$value': 'Unit 1' },
      { attributes: { key: 'shipAddrLine2' }, '$value': '123 Street' },
      { attributes: { key: 'shipAddrPostCode' }, '$value': '2000' },
      { attributes: { key: 'billAddrCity' }, '$value': 'Sydney' },
      { attributes: { key: 'billAddrCountry' }, '$value': '036' },
      { attributes: { key: 'billAddrLine1' }, '$value': 'Unit 1' },
      { attributes: { key: 'billAddrLine2' }, '$value': '123 Street' },
      { attributes: { key: 'billAddrState' }, '$value': 'NSW' },
      { attributes: { key: 'billAddrPostCode' }, '$value': '2000' },
      { attributes: { key: 'threeDSCompInd' }, '$value': 'U' },
      {
        attributes: { key: 'threeDSRequestorAuthenticationInd' },
        '$value': '01'
      },
      {
        attributes: { key: 'threeDSRequestorChallengeInd' },
        '$value': '01'
      },
      { attributes: { key: 'addrMatch' }, '$value': 'Y' },
      { attributes: { key: 'cardExpiryDate' }, '$value': '2508' },
      { attributes: { key: 'acctID' }, '$value': 'personal account' },
      { attributes: { key: 'email' }, '$value': '<EMAIL>' },
      { attributes: { key: 'mobilePhone.cc' }, '$value': '61' },
      {
        attributes: { key: 'mobilePhone.subscriber' },
        '$value': '**********'
      }
    ]
  }
}
[2025-05-09T08:08:09.058Z] getCardKey input: {
  card: {
    id: null,
    number: '****************',
    type: 'VbV',
    context_Blob: null
  },
  transaction: {
    purchaseAmount: '10000',
    purchaseExponent: '2',
    purchaseCurrency: '036',
    purchaseDate: '2023-06-27T11:26:38.000Z',
    merchantId: '***************',
    merchantName: 'Test Merchant',
    merchantCountry: '840',
    acqBin: '***********',
    theeDSProtocolVersion: '2.1.0',
    cardExpiry: '2508',
    issuerName: 'remote',
    acsTransId: '36e6c66a-3dc6-4a59-a722-96c718824981',
    threeDSTransId: 'e6e9bf9b-af70-4f20-ad3a-52ce69de1c15',
    dsTransId: '2d5e6910-4d86-4d84-b6a3-9125b80b9ba1',
    threeDSRequestorID: '*********.visa',
    threeDSRequestorName: '3dsclient.local.visa',
    threeDSServerRefNumber: '3DS_LOA_SER_GPPL_020100_00075',
    threeDSServerOperatorID: '1jpeeLAWgGFgS1Ri9tX9',
    threeDSRequestorURL: 'http://gpayments.com',
    threeDSServerURL: 'https://amir-test3.testlab.3dsecure.cloud:9605/api/v2/ds/result/request',
    deviceChannel: '02',
    dsReferenceNumber: '3DS_LOA_ACS_GPPL_020200_00442',
    payTokenInd: '0',
    mcc: '2020',
    messageCategory: '01',
    transType: '01',
    acctType: '03',
    threeDSRequestorAuthenticationInd: '01'
  },
  headerParams: {
    param: [
      {
        attributes: { key: 'browserJavaEnabled', cookie: 'false' },
        '$value': 'false'
      },
      {
        attributes: { key: 'browserTZ', cookie: 'false' },
        '$value': '-180'
      },
      {
        attributes: { key: 'browserLanguage', cookie: 'false' },
        '$value': 'en-US'
      },
      {
        attributes: { key: 'Accept-Language', cookie: 'false' },
        '$value': 'en-US'
      },
      {
        attributes: { key: 'User-Agent', cookie: 'false' },
        '$value': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
      },
      {
        attributes: { key: 'Accept', cookie: 'false' },
        '$value': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7'
      },
      {
        attributes: { key: 'proxy-ip', cookie: 'false' },
        '$value': '************'
      },
      {
        attributes: { key: 'browserColorDepth', cookie: 'false' },
        '$value': '24'
      },
      {
        attributes: { key: 'browserScreenHeight', cookie: 'false' },
        '$value': '1050'
      },
      {
        attributes: { key: 'browserScreenWidth', cookie: 'false' },
        '$value': '1680'
      }
    ]
  },
  extensionParams: null,
  additionalParams: {
    param: [
      { attributes: { key: 'shipAddrState' }, '$value': 'NSW' },
      { attributes: { key: 'shipAddrCity' }, '$value': 'Sydney' },
      { attributes: { key: 'shipAddrCountry' }, '$value': '036' },
      { attributes: { key: 'shipAddrLine1' }, '$value': 'Unit 1' },
      { attributes: { key: 'shipAddrLine2' }, '$value': '123 Street' },
      { attributes: { key: 'shipAddrPostCode' }, '$value': '2000' },
      { attributes: { key: 'billAddrCity' }, '$value': 'Sydney' },
      { attributes: { key: 'billAddrCountry' }, '$value': '036' },
      { attributes: { key: 'billAddrLine1' }, '$value': 'Unit 1' },
      { attributes: { key: 'billAddrLine2' }, '$value': '123 Street' },
      { attributes: { key: 'billAddrState' }, '$value': 'NSW' },
      { attributes: { key: 'billAddrPostCode' }, '$value': '2000' },
      { attributes: { key: 'threeDSCompInd' }, '$value': 'U' },
      {
        attributes: { key: 'threeDSRequestorAuthenticationInd' },
        '$value': '01'
      },
      {
        attributes: { key: 'threeDSRequestorChallengeInd' },
        '$value': '01'
      },
      { attributes: { key: 'addrMatch' }, '$value': 'Y' },
      { attributes: { key: 'cardExpiryDate' }, '$value': '2508' },
      { attributes: { key: 'acctID' }, '$value': 'personal account' },
      { attributes: { key: 'email' }, '$value': '<EMAIL>' },
      { attributes: { key: 'mobilePhone.cc' }, '$value': '61' },
      {
        attributes: { key: 'mobilePhone.subscriber' },
        '$value': '**********'
      }
    ]
  }
}
[2025-05-09T08:08:09.061Z] Card details: { original: '****************', masked: '446614******6104' }
[2025-05-09T08:08:09.734Z] Card found: { cardKey: 'KDR0020100057072' }
[2025-05-09T08:08:12.467Z] Card details retrieved: {
  cardKey: 'KDR0020100057072',
  cardDetails: {
    cardHash: 'FBBB71A8EC741FB001ADDCD140F6141FEEB4708BA9D515C8014828373E96B17C86F0DA62E45FDA39AF93E443D939864F39E3D69C88E4C9A23F610692B7250AC7',
    cardMask: '446614******6104',
    cardKey: 'KDR0020100057072',
    cardUuid: '492bea20-20f0-11f0-4c29-6f786f726131',
    expDate: '04/2028',
    issueDate: '2025-04-24',
    status: 'ACTIVE',
    statusCode: '0',
    terminating: false,
    productCode: 'R002',
    productDesc: 'RYVYL VISA CONSUMER DEBIT VTL',
    holder: 'RYVL-742897',
    accounts: [
      {
        accNo: '**********************',
        primary: true,
        currencyCode: '978',
        currencyName: 'EUR'
      }
    ],
    embossName1: 'TRENT ARNOLD',
    source: 'NEW',
    kind: 'DEBIT',
    main: false,
    limits: [],
    visual: 'R002',
    tokenized: false,
    delivery: {
      deliveryType: 'NONE',
      deliveryBranch: 'DHL',
      oneTimeDeliveryBranch: 'DHL'
    },
    contactless: true,
    cardTechnologyMode: 'VIRTUAL',
    creationDate: '2025-04-24',
    autoRenewal: true
  }
}
[2025-05-09T08:08:12.471Z] verifyReg response: {
  verifyRegResp: '{"cardInfo":{"card_ID":"****************","context_Blob":"xyz123","regStatus":0,"authRequired":1,"authTypeSup":[1,2],"lanCode":"","twoFA":true},"code":1,"errorMessage":"Card found","errorDetail":"Card info fetched successfully"}'
}
[2025-05-09T08:08:26.623Z] verifyReg called: {
  args: {
    card: {
      id: null,
      number: '****************',
      type: 'VbV',
      context_Blob: null
    },
    transaction: {
      purchaseAmount: '10000',
      purchaseExponent: '2',
      purchaseCurrency: '036',
      purchaseDate: '2023-06-27T11:26:38.000Z',
      merchantId: '***************',
      merchantName: 'Test Merchant',
      merchantCountry: '840',
      acqBin: '***********',
      theeDSProtocolVersion: '2.1.0',
      cardExpiry: '2508',
      issuerName: 'remote',
      acsTransId: '36e6c66a-3dc6-4a59-a722-96c718824981',
      threeDSTransId: 'e6e9bf9b-af70-4f20-ad3a-52ce69de1c15',
      dsTransId: '2d5e6910-4d86-4d84-b6a3-9125b80b9ba1',
      threeDSRequestorID: '*********.visa',
      threeDSRequestorName: '3dsclient.local.visa',
      threeDSServerRefNumber: '3DS_LOA_SER_GPPL_020100_00075',
      threeDSServerOperatorID: '1jpeeLAWgGFgS1Ri9tX9',
      threeDSRequestorURL: 'http://gpayments.com',
      threeDSServerURL: 'https://amir-test3.testlab.3dsecure.cloud:9605/api/v2/ds/result/request',
      deviceChannel: '02',
      dsReferenceNumber: '3DS_LOA_ACS_GPPL_020200_00442',
      payTokenInd: '0',
      mcc: '2020',
      messageCategory: '01',
      transType: '01',
      acctType: '03',
      threeDSRequestorAuthenticationInd: '01'
    },
    headerParams: {
      param: [
        {
          attributes: { key: 'browserJavaEnabled', cookie: 'false' },
          '$value': 'false'
        },
        {
          attributes: { key: 'browserTZ', cookie: 'false' },
          '$value': '-180'
        },
        {
          attributes: { key: 'browserLanguage', cookie: 'false' },
          '$value': 'en-US'
        },
        {
          attributes: { key: 'Accept-Language', cookie: 'false' },
          '$value': 'en-US'
        },
        {
          attributes: { key: 'User-Agent', cookie: 'false' },
          '$value': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        },
        {
          attributes: { key: 'Accept', cookie: 'false' },
          '$value': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7'
        },
        {
          attributes: { key: 'proxy-ip', cookie: 'false' },
          '$value': '************'
        },
        {
          attributes: { key: 'browserColorDepth', cookie: 'false' },
          '$value': '24'
        },
        {
          attributes: { key: 'browserScreenHeight', cookie: 'false' },
          '$value': '1050'
        },
        {
          attributes: { key: 'browserScreenWidth', cookie: 'false' },
          '$value': '1680'
        }
      ]
    },
    extensionParams: null,
    additionalParams: {
      param: [
        { attributes: { key: 'shipAddrState' }, '$value': 'NSW' },
        { attributes: { key: 'shipAddrCity' }, '$value': 'Sydney' },
        { attributes: { key: 'shipAddrCountry' }, '$value': '036' },
        { attributes: { key: 'shipAddrLine1' }, '$value': 'Unit 1' },
        {
          attributes: { key: 'shipAddrLine2' },
          '$value': '123 Street'
        },
        { attributes: { key: 'shipAddrPostCode' }, '$value': '2000' },
        { attributes: { key: 'billAddrCity' }, '$value': 'Sydney' },
        { attributes: { key: 'billAddrCountry' }, '$value': '036' },
        { attributes: { key: 'billAddrLine1' }, '$value': 'Unit 1' },
        {
          attributes: { key: 'billAddrLine2' },
          '$value': '123 Street'
        },
        { attributes: { key: 'billAddrState' }, '$value': 'NSW' },
        { attributes: { key: 'billAddrPostCode' }, '$value': '2000' },
        { attributes: { key: 'threeDSCompInd' }, '$value': 'U' },
        {
          attributes: { key: 'threeDSRequestorAuthenticationInd' },
          '$value': '01'
        },
        {
          attributes: { key: 'threeDSRequestorChallengeInd' },
          '$value': '01'
        },
        { attributes: { key: 'addrMatch' }, '$value': 'Y' },
        { attributes: { key: 'cardExpiryDate' }, '$value': '2508' },
        { attributes: { key: 'acctID' }, '$value': 'personal account' },
        { attributes: { key: 'email' }, '$value': '<EMAIL>' },
        { attributes: { key: 'mobilePhone.cc' }, '$value': '61' },
        {
          attributes: { key: 'mobilePhone.subscriber' },
          '$value': '**********'
        }
      ]
    }
  },
  headers: undefined
}
[2025-05-09T08:08:26.630Z] Parsed input data: {
  card: {
    id: null,
    number: '****************',
    type: 'VbV',
    context_Blob: null
  },
  transaction: {
    purchaseAmount: '10000',
    purchaseExponent: '2',
    purchaseCurrency: '036',
    purchaseDate: '2023-06-27T11:26:38.000Z',
    merchantId: '***************',
    merchantName: 'Test Merchant',
    merchantCountry: '840',
    acqBin: '***********',
    theeDSProtocolVersion: '2.1.0',
    cardExpiry: '2508',
    issuerName: 'remote',
    acsTransId: '36e6c66a-3dc6-4a59-a722-96c718824981',
    threeDSTransId: 'e6e9bf9b-af70-4f20-ad3a-52ce69de1c15',
    dsTransId: '2d5e6910-4d86-4d84-b6a3-9125b80b9ba1',
    threeDSRequestorID: '*********.visa',
    threeDSRequestorName: '3dsclient.local.visa',
    threeDSServerRefNumber: '3DS_LOA_SER_GPPL_020100_00075',
    threeDSServerOperatorID: '1jpeeLAWgGFgS1Ri9tX9',
    threeDSRequestorURL: 'http://gpayments.com',
    threeDSServerURL: 'https://amir-test3.testlab.3dsecure.cloud:9605/api/v2/ds/result/request',
    deviceChannel: '02',
    dsReferenceNumber: '3DS_LOA_ACS_GPPL_020200_00442',
    payTokenInd: '0',
    mcc: '2020',
    messageCategory: '01',
    transType: '01',
    acctType: '03',
    threeDSRequestorAuthenticationInd: '01'
  },
  headerParams: {
    param: [
      {
        attributes: { key: 'browserJavaEnabled', cookie: 'false' },
        '$value': 'false'
      },
      {
        attributes: { key: 'browserTZ', cookie: 'false' },
        '$value': '-180'
      },
      {
        attributes: { key: 'browserLanguage', cookie: 'false' },
        '$value': 'en-US'
      },
      {
        attributes: { key: 'Accept-Language', cookie: 'false' },
        '$value': 'en-US'
      },
      {
        attributes: { key: 'User-Agent', cookie: 'false' },
        '$value': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
      },
      {
        attributes: { key: 'Accept', cookie: 'false' },
        '$value': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7'
      },
      {
        attributes: { key: 'proxy-ip', cookie: 'false' },
        '$value': '************'
      },
      {
        attributes: { key: 'browserColorDepth', cookie: 'false' },
        '$value': '24'
      },
      {
        attributes: { key: 'browserScreenHeight', cookie: 'false' },
        '$value': '1050'
      },
      {
        attributes: { key: 'browserScreenWidth', cookie: 'false' },
        '$value': '1680'
      }
    ]
  },
  extensionParams: null,
  additionalParams: {
    param: [
      { attributes: { key: 'shipAddrState' }, '$value': 'NSW' },
      { attributes: { key: 'shipAddrCity' }, '$value': 'Sydney' },
      { attributes: { key: 'shipAddrCountry' }, '$value': '036' },
      { attributes: { key: 'shipAddrLine1' }, '$value': 'Unit 1' },
      { attributes: { key: 'shipAddrLine2' }, '$value': '123 Street' },
      { attributes: { key: 'shipAddrPostCode' }, '$value': '2000' },
      { attributes: { key: 'billAddrCity' }, '$value': 'Sydney' },
      { attributes: { key: 'billAddrCountry' }, '$value': '036' },
      { attributes: { key: 'billAddrLine1' }, '$value': 'Unit 1' },
      { attributes: { key: 'billAddrLine2' }, '$value': '123 Street' },
      { attributes: { key: 'billAddrState' }, '$value': 'NSW' },
      { attributes: { key: 'billAddrPostCode' }, '$value': '2000' },
      { attributes: { key: 'threeDSCompInd' }, '$value': 'U' },
      {
        attributes: { key: 'threeDSRequestorAuthenticationInd' },
        '$value': '01'
      },
      {
        attributes: { key: 'threeDSRequestorChallengeInd' },
        '$value': '01'
      },
      { attributes: { key: 'addrMatch' }, '$value': 'Y' },
      { attributes: { key: 'cardExpiryDate' }, '$value': '2508' },
      { attributes: { key: 'acctID' }, '$value': 'personal account' },
      { attributes: { key: 'email' }, '$value': '<EMAIL>' },
      { attributes: { key: 'mobilePhone.cc' }, '$value': '61' },
      {
        attributes: { key: 'mobilePhone.subscriber' },
        '$value': '**********'
      }
    ]
  }
}
[2025-05-09T08:08:26.632Z] getCardKey input: {
  card: {
    id: null,
    number: '****************',
    type: 'VbV',
    context_Blob: null
  },
  transaction: {
    purchaseAmount: '10000',
    purchaseExponent: '2',
    purchaseCurrency: '036',
    purchaseDate: '2023-06-27T11:26:38.000Z',
    merchantId: '***************',
    merchantName: 'Test Merchant',
    merchantCountry: '840',
    acqBin: '***********',
    theeDSProtocolVersion: '2.1.0',
    cardExpiry: '2508',
    issuerName: 'remote',
    acsTransId: '36e6c66a-3dc6-4a59-a722-96c718824981',
    threeDSTransId: 'e6e9bf9b-af70-4f20-ad3a-52ce69de1c15',
    dsTransId: '2d5e6910-4d86-4d84-b6a3-9125b80b9ba1',
    threeDSRequestorID: '*********.visa',
    threeDSRequestorName: '3dsclient.local.visa',
    threeDSServerRefNumber: '3DS_LOA_SER_GPPL_020100_00075',
    threeDSServerOperatorID: '1jpeeLAWgGFgS1Ri9tX9',
    threeDSRequestorURL: 'http://gpayments.com',
    threeDSServerURL: 'https://amir-test3.testlab.3dsecure.cloud:9605/api/v2/ds/result/request',
    deviceChannel: '02',
    dsReferenceNumber: '3DS_LOA_ACS_GPPL_020200_00442',
    payTokenInd: '0',
    mcc: '2020',
    messageCategory: '01',
    transType: '01',
    acctType: '03',
    threeDSRequestorAuthenticationInd: '01'
  },
  headerParams: {
    param: [
      {
        attributes: { key: 'browserJavaEnabled', cookie: 'false' },
        '$value': 'false'
      },
      {
        attributes: { key: 'browserTZ', cookie: 'false' },
        '$value': '-180'
      },
      {
        attributes: { key: 'browserLanguage', cookie: 'false' },
        '$value': 'en-US'
      },
      {
        attributes: { key: 'Accept-Language', cookie: 'false' },
        '$value': 'en-US'
      },
      {
        attributes: { key: 'User-Agent', cookie: 'false' },
        '$value': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
      },
      {
        attributes: { key: 'Accept', cookie: 'false' },
        '$value': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7'
      },
      {
        attributes: { key: 'proxy-ip', cookie: 'false' },
        '$value': '************'
      },
      {
        attributes: { key: 'browserColorDepth', cookie: 'false' },
        '$value': '24'
      },
      {
        attributes: { key: 'browserScreenHeight', cookie: 'false' },
        '$value': '1050'
      },
      {
        attributes: { key: 'browserScreenWidth', cookie: 'false' },
        '$value': '1680'
      }
    ]
  },
  extensionParams: null,
  additionalParams: {
    param: [
      { attributes: { key: 'shipAddrState' }, '$value': 'NSW' },
      { attributes: { key: 'shipAddrCity' }, '$value': 'Sydney' },
      { attributes: { key: 'shipAddrCountry' }, '$value': '036' },
      { attributes: { key: 'shipAddrLine1' }, '$value': 'Unit 1' },
      { attributes: { key: 'shipAddrLine2' }, '$value': '123 Street' },
      { attributes: { key: 'shipAddrPostCode' }, '$value': '2000' },
      { attributes: { key: 'billAddrCity' }, '$value': 'Sydney' },
      { attributes: { key: 'billAddrCountry' }, '$value': '036' },
      { attributes: { key: 'billAddrLine1' }, '$value': 'Unit 1' },
      { attributes: { key: 'billAddrLine2' }, '$value': '123 Street' },
      { attributes: { key: 'billAddrState' }, '$value': 'NSW' },
      { attributes: { key: 'billAddrPostCode' }, '$value': '2000' },
      { attributes: { key: 'threeDSCompInd' }, '$value': 'U' },
      {
        attributes: { key: 'threeDSRequestorAuthenticationInd' },
        '$value': '01'
      },
      {
        attributes: { key: 'threeDSRequestorChallengeInd' },
        '$value': '01'
      },
      { attributes: { key: 'addrMatch' }, '$value': 'Y' },
      { attributes: { key: 'cardExpiryDate' }, '$value': '2508' },
      { attributes: { key: 'acctID' }, '$value': 'personal account' },
      { attributes: { key: 'email' }, '$value': '<EMAIL>' },
      { attributes: { key: 'mobilePhone.cc' }, '$value': '61' },
      {
        attributes: { key: 'mobilePhone.subscriber' },
        '$value': '**********'
      }
    ]
  }
}
[2025-05-09T08:08:26.634Z] Card details: { original: '****************', masked: '446614******6104' }
[2025-05-09T08:08:27.070Z] Card found: { cardKey: 'KDR0020100057072' }
[2025-05-09T08:08:29.376Z] Card details retrieved: {
  cardKey: 'KDR0020100057072',
  cardDetails: {
    cardHash: 'FBBB71A8EC741FB001ADDCD140F6141FEEB4708BA9D515C8014828373E96B17C86F0DA62E45FDA39AF93E443D939864F39E3D69C88E4C9A23F610692B7250AC7',
    cardMask: '446614******6104',
    cardKey: 'KDR0020100057072',
    cardUuid: '492bea20-20f0-11f0-4c29-6f786f726131',
    expDate: '04/2028',
    issueDate: '2025-04-24',
    status: 'ACTIVE',
    statusCode: '0',
    terminating: false,
    productCode: 'R002',
    productDesc: 'RYVYL VISA CONSUMER DEBIT VTL',
    holder: 'RYVL-742897',
    accounts: [
      {
        accNo: '**********************',
        primary: true,
        currencyCode: '978',
        currencyName: 'EUR'
      }
    ],
    embossName1: 'TRENT ARNOLD',
    source: 'NEW',
    kind: 'DEBIT',
    main: false,
    limits: [],
    visual: 'R002',
    tokenized: false,
    delivery: {
      deliveryType: 'NONE',
      deliveryBranch: 'DHL',
      oneTimeDeliveryBranch: 'DHL'
    },
    contactless: true,
    cardTechnologyMode: 'VIRTUAL',
    creationDate: '2025-04-24',
    autoRenewal: true
  }
}
[2025-05-09T08:08:29.377Z] verifyReg response: {
  verifyRegResp: '{"cardInfo":{"card_ID":"****************","context_Blob":"xyz123","regStatus":0,"authRequired":1,"authTypeSup":[1,2],"lanCode":"","twoFA":true},"code":1,"errorMessage":"Card found","errorDetail":"Card info fetched successfully"}'
}
[2025-05-09T08:08:40.015Z] verifyReg called: {
  args: {
    card: {
      id: null,
      number: '****************',
      type: 'VbV',
      context_Blob: null
    },
    transaction: {
      purchaseAmount: '10000',
      purchaseExponent: '2',
      purchaseCurrency: '036',
      purchaseDate: '2023-06-27T11:26:38.000Z',
      merchantId: '***************',
      merchantName: 'Test Merchant',
      merchantCountry: '840',
      acqBin: '***********',
      theeDSProtocolVersion: '2.1.0',
      cardExpiry: '2508',
      issuerName: 'remote',
      acsTransId: '36e6c66a-3dc6-4a59-a722-96c718824981',
      threeDSTransId: 'e6e9bf9b-af70-4f20-ad3a-52ce69de1c15',
      dsTransId: '2d5e6910-4d86-4d84-b6a3-9125b80b9ba1',
      threeDSRequestorID: '*********.visa',
      threeDSRequestorName: '3dsclient.local.visa',
      threeDSServerRefNumber: '3DS_LOA_SER_GPPL_020100_00075',
      threeDSServerOperatorID: '1jpeeLAWgGFgS1Ri9tX9',
      threeDSRequestorURL: 'http://gpayments.com',
      threeDSServerURL: 'https://amir-test3.testlab.3dsecure.cloud:9605/api/v2/ds/result/request',
      deviceChannel: '02',
      dsReferenceNumber: '3DS_LOA_ACS_GPPL_020200_00442',
      payTokenInd: '0',
      mcc: '2020',
      messageCategory: '01',
      transType: '01',
      acctType: '03',
      threeDSRequestorAuthenticationInd: '01'
    },
    headerParams: {
      param: [
        {
          attributes: { key: 'browserJavaEnabled', cookie: 'false' },
          '$value': 'false'
        },
        {
          attributes: { key: 'browserTZ', cookie: 'false' },
          '$value': '-180'
        },
        {
          attributes: { key: 'browserLanguage', cookie: 'false' },
          '$value': 'en-US'
        },
        {
          attributes: { key: 'Accept-Language', cookie: 'false' },
          '$value': 'en-US'
        },
        {
          attributes: { key: 'User-Agent', cookie: 'false' },
          '$value': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        },
        {
          attributes: { key: 'Accept', cookie: 'false' },
          '$value': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7'
        },
        {
          attributes: { key: 'proxy-ip', cookie: 'false' },
          '$value': '************'
        },
        {
          attributes: { key: 'browserColorDepth', cookie: 'false' },
          '$value': '24'
        },
        {
          attributes: { key: 'browserScreenHeight', cookie: 'false' },
          '$value': '1050'
        },
        {
          attributes: { key: 'browserScreenWidth', cookie: 'false' },
          '$value': '1680'
        }
      ]
    },
    extensionParams: null,
    additionalParams: {
      param: [
        { attributes: { key: 'shipAddrState' }, '$value': 'NSW' },
        { attributes: { key: 'shipAddrCity' }, '$value': 'Sydney' },
        { attributes: { key: 'shipAddrCountry' }, '$value': '036' },
        { attributes: { key: 'shipAddrLine1' }, '$value': 'Unit 1' },
        {
          attributes: { key: 'shipAddrLine2' },
          '$value': '123 Street'
        },
        { attributes: { key: 'shipAddrPostCode' }, '$value': '2000' },
        { attributes: { key: 'billAddrCity' }, '$value': 'Sydney' },
        { attributes: { key: 'billAddrCountry' }, '$value': '036' },
        { attributes: { key: 'billAddrLine1' }, '$value': 'Unit 1' },
        {
          attributes: { key: 'billAddrLine2' },
          '$value': '123 Street'
        },
        { attributes: { key: 'billAddrState' }, '$value': 'NSW' },
        { attributes: { key: 'billAddrPostCode' }, '$value': '2000' },
        { attributes: { key: 'threeDSCompInd' }, '$value': 'U' },
        {
          attributes: { key: 'threeDSRequestorAuthenticationInd' },
          '$value': '01'
        },
        {
          attributes: { key: 'threeDSRequestorChallengeInd' },
          '$value': '01'
        },
        { attributes: { key: 'addrMatch' }, '$value': 'Y' },
        { attributes: { key: 'cardExpiryDate' }, '$value': '2508' },
        { attributes: { key: 'acctID' }, '$value': 'personal account' },
        { attributes: { key: 'email' }, '$value': '<EMAIL>' },
        { attributes: { key: 'mobilePhone.cc' }, '$value': '61' },
        {
          attributes: { key: 'mobilePhone.subscriber' },
          '$value': '**********'
        }
      ]
    }
  },
  headers: undefined
}
[2025-05-09T08:08:40.037Z] Parsed input data: {
  card: {
    id: null,
    number: '****************',
    type: 'VbV',
    context_Blob: null
  },
  transaction: {
    purchaseAmount: '10000',
    purchaseExponent: '2',
    purchaseCurrency: '036',
    purchaseDate: '2023-06-27T11:26:38.000Z',
    merchantId: '***************',
    merchantName: 'Test Merchant',
    merchantCountry: '840',
    acqBin: '***********',
    theeDSProtocolVersion: '2.1.0',
    cardExpiry: '2508',
    issuerName: 'remote',
    acsTransId: '36e6c66a-3dc6-4a59-a722-96c718824981',
    threeDSTransId: 'e6e9bf9b-af70-4f20-ad3a-52ce69de1c15',
    dsTransId: '2d5e6910-4d86-4d84-b6a3-9125b80b9ba1',
    threeDSRequestorID: '*********.visa',
    threeDSRequestorName: '3dsclient.local.visa',
    threeDSServerRefNumber: '3DS_LOA_SER_GPPL_020100_00075',
    threeDSServerOperatorID: '1jpeeLAWgGFgS1Ri9tX9',
    threeDSRequestorURL: 'http://gpayments.com',
    threeDSServerURL: 'https://amir-test3.testlab.3dsecure.cloud:9605/api/v2/ds/result/request',
    deviceChannel: '02',
    dsReferenceNumber: '3DS_LOA_ACS_GPPL_020200_00442',
    payTokenInd: '0',
    mcc: '2020',
    messageCategory: '01',
    transType: '01',
    acctType: '03',
    threeDSRequestorAuthenticationInd: '01'
  },
  headerParams: {
    param: [
      {
        attributes: { key: 'browserJavaEnabled', cookie: 'false' },
        '$value': 'false'
      },
      {
        attributes: { key: 'browserTZ', cookie: 'false' },
        '$value': '-180'
      },
      {
        attributes: { key: 'browserLanguage', cookie: 'false' },
        '$value': 'en-US'
      },
      {
        attributes: { key: 'Accept-Language', cookie: 'false' },
        '$value': 'en-US'
      },
      {
        attributes: { key: 'User-Agent', cookie: 'false' },
        '$value': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
      },
      {
        attributes: { key: 'Accept', cookie: 'false' },
        '$value': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7'
      },
      {
        attributes: { key: 'proxy-ip', cookie: 'false' },
        '$value': '************'
      },
      {
        attributes: { key: 'browserColorDepth', cookie: 'false' },
        '$value': '24'
      },
      {
        attributes: { key: 'browserScreenHeight', cookie: 'false' },
        '$value': '1050'
      },
      {
        attributes: { key: 'browserScreenWidth', cookie: 'false' },
        '$value': '1680'
      }
    ]
  },
  extensionParams: null,
  additionalParams: {
    param: [
      { attributes: { key: 'shipAddrState' }, '$value': 'NSW' },
      { attributes: { key: 'shipAddrCity' }, '$value': 'Sydney' },
      { attributes: { key: 'shipAddrCountry' }, '$value': '036' },
      { attributes: { key: 'shipAddrLine1' }, '$value': 'Unit 1' },
      { attributes: { key: 'shipAddrLine2' }, '$value': '123 Street' },
      { attributes: { key: 'shipAddrPostCode' }, '$value': '2000' },
      { attributes: { key: 'billAddrCity' }, '$value': 'Sydney' },
      { attributes: { key: 'billAddrCountry' }, '$value': '036' },
      { attributes: { key: 'billAddrLine1' }, '$value': 'Unit 1' },
      { attributes: { key: 'billAddrLine2' }, '$value': '123 Street' },
      { attributes: { key: 'billAddrState' }, '$value': 'NSW' },
      { attributes: { key: 'billAddrPostCode' }, '$value': '2000' },
      { attributes: { key: 'threeDSCompInd' }, '$value': 'U' },
      {
        attributes: { key: 'threeDSRequestorAuthenticationInd' },
        '$value': '01'
      },
      {
        attributes: { key: 'threeDSRequestorChallengeInd' },
        '$value': '01'
      },
      { attributes: { key: 'addrMatch' }, '$value': 'Y' },
      { attributes: { key: 'cardExpiryDate' }, '$value': '2508' },
      { attributes: { key: 'acctID' }, '$value': 'personal account' },
      { attributes: { key: 'email' }, '$value': '<EMAIL>' },
      { attributes: { key: 'mobilePhone.cc' }, '$value': '61' },
      {
        attributes: { key: 'mobilePhone.subscriber' },
        '$value': '**********'
      }
    ]
  }
}
[2025-05-09T08:08:40.047Z] getCardKey input: {
  card: {
    id: null,
    number: '****************',
    type: 'VbV',
    context_Blob: null
  },
  transaction: {
    purchaseAmount: '10000',
    purchaseExponent: '2',
    purchaseCurrency: '036',
    purchaseDate: '2023-06-27T11:26:38.000Z',
    merchantId: '***************',
    merchantName: 'Test Merchant',
    merchantCountry: '840',
    acqBin: '***********',
    theeDSProtocolVersion: '2.1.0',
    cardExpiry: '2508',
    issuerName: 'remote',
    acsTransId: '36e6c66a-3dc6-4a59-a722-96c718824981',
    threeDSTransId: 'e6e9bf9b-af70-4f20-ad3a-52ce69de1c15',
    dsTransId: '2d5e6910-4d86-4d84-b6a3-9125b80b9ba1',
    threeDSRequestorID: '*********.visa',
    threeDSRequestorName: '3dsclient.local.visa',
    threeDSServerRefNumber: '3DS_LOA_SER_GPPL_020100_00075',
    threeDSServerOperatorID: '1jpeeLAWgGFgS1Ri9tX9',
    threeDSRequestorURL: 'http://gpayments.com',
    threeDSServerURL: 'https://amir-test3.testlab.3dsecure.cloud:9605/api/v2/ds/result/request',
    deviceChannel: '02',
    dsReferenceNumber: '3DS_LOA_ACS_GPPL_020200_00442',
    payTokenInd: '0',
    mcc: '2020',
    messageCategory: '01',
    transType: '01',
    acctType: '03',
    threeDSRequestorAuthenticationInd: '01'
  },
  headerParams: {
    param: [
      {
        attributes: { key: 'browserJavaEnabled', cookie: 'false' },
        '$value': 'false'
      },
      {
        attributes: { key: 'browserTZ', cookie: 'false' },
        '$value': '-180'
      },
      {
        attributes: { key: 'browserLanguage', cookie: 'false' },
        '$value': 'en-US'
      },
      {
        attributes: { key: 'Accept-Language', cookie: 'false' },
        '$value': 'en-US'
      },
      {
        attributes: { key: 'User-Agent', cookie: 'false' },
        '$value': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
      },
      {
        attributes: { key: 'Accept', cookie: 'false' },
        '$value': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7'
      },
      {
        attributes: { key: 'proxy-ip', cookie: 'false' },
        '$value': '************'
      },
      {
        attributes: { key: 'browserColorDepth', cookie: 'false' },
        '$value': '24'
      },
      {
        attributes: { key: 'browserScreenHeight', cookie: 'false' },
        '$value': '1050'
      },
      {
        attributes: { key: 'browserScreenWidth', cookie: 'false' },
        '$value': '1680'
      }
    ]
  },
  extensionParams: null,
  additionalParams: {
    param: [
      { attributes: { key: 'shipAddrState' }, '$value': 'NSW' },
      { attributes: { key: 'shipAddrCity' }, '$value': 'Sydney' },
      { attributes: { key: 'shipAddrCountry' }, '$value': '036' },
      { attributes: { key: 'shipAddrLine1' }, '$value': 'Unit 1' },
      { attributes: { key: 'shipAddrLine2' }, '$value': '123 Street' },
      { attributes: { key: 'shipAddrPostCode' }, '$value': '2000' },
      { attributes: { key: 'billAddrCity' }, '$value': 'Sydney' },
      { attributes: { key: 'billAddrCountry' }, '$value': '036' },
      { attributes: { key: 'billAddrLine1' }, '$value': 'Unit 1' },
      { attributes: { key: 'billAddrLine2' }, '$value': '123 Street' },
      { attributes: { key: 'billAddrState' }, '$value': 'NSW' },
      { attributes: { key: 'billAddrPostCode' }, '$value': '2000' },
      { attributes: { key: 'threeDSCompInd' }, '$value': 'U' },
      {
        attributes: { key: 'threeDSRequestorAuthenticationInd' },
        '$value': '01'
      },
      {
        attributes: { key: 'threeDSRequestorChallengeInd' },
        '$value': '01'
      },
      { attributes: { key: 'addrMatch' }, '$value': 'Y' },
      { attributes: { key: 'cardExpiryDate' }, '$value': '2508' },
      { attributes: { key: 'acctID' }, '$value': 'personal account' },
      { attributes: { key: 'email' }, '$value': '<EMAIL>' },
      { attributes: { key: 'mobilePhone.cc' }, '$value': '61' },
      {
        attributes: { key: 'mobilePhone.subscriber' },
        '$value': '**********'
      }
    ]
  }
}
[2025-05-09T08:08:40.054Z] Card details: { original: '****************', masked: '446614******6104' }
[2025-05-09T08:08:44.212Z] Card found: { cardKey: 'KDR0020100057072' }
[2025-05-09T08:08:52.724Z] Card details retrieved: {
  cardKey: 'KDR0020100057072',
  cardDetails: {
    cardHash: 'FBBB71A8EC741FB001ADDCD140F6141FEEB4708BA9D515C8014828373E96B17C86F0DA62E45FDA39AF93E443D939864F39E3D69C88E4C9A23F610692B7250AC7',
    cardMask: '446614******6104',
    cardKey: 'KDR0020100057072',
    cardUuid: '492bea20-20f0-11f0-4c29-6f786f726131',
    expDate: '04/2028',
    issueDate: '2025-04-24',
    status: 'ACTIVE',
    statusCode: '0',
    terminating: false,
    productCode: 'R002',
    productDesc: 'RYVYL VISA CONSUMER DEBIT VTL',
    holder: 'RYVL-742897',
    accounts: [
      {
        accNo: '**********************',
        primary: true,
        currencyCode: '978',
        currencyName: 'EUR'
      }
    ],
    embossName1: 'TRENT ARNOLD',
    source: 'NEW',
    kind: 'DEBIT',
    main: false,
    limits: [],
    visual: 'R002',
    tokenized: false,
    delivery: {
      deliveryType: 'NONE',
      deliveryBranch: 'DHL',
      oneTimeDeliveryBranch: 'DHL'
    },
    contactless: true,
    cardTechnologyMode: 'VIRTUAL',
    creationDate: '2025-04-24',
    autoRenewal: true
  }
}
[2025-05-09T08:08:52.743Z] verifyReg response: {
  verifyRegResp: '{"cardInfo":{"card_ID":"****************","context_Blob":"xyz123","regStatus":0,"authRequired":1,"authTypeSup":[1,2],"lanCode":"","twoFA":true},"code":1,"errorMessage":"Card found","errorDetail":"Card info fetched successfully"}'
}
