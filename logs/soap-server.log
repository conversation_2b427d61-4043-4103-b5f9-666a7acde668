[2025-05-09T08:01:45.736Z] WSDL loaded: { path: '/Users/<USER>/WebstormProjects/ryvyl-backend/verify.wsdl' }
[2025-05-09T08:01:45.745Z] SOAP 1.2 service attached: { path: '/wsdl' }
[2025-05-09T08:01:45.763Z] Server started: { port: '3001' }
[2025-05-09T08:01:47.928Z] Connected to MongoDB: {}
[2025-05-09T08:01:52.968Z] SOAP info: 'Handling POST on /wsdl?wsdl'
[2025-05-09T08:01:52.970Z] SOAP received: '\n' +
  "<?xml version='1.0' encoding='UTF-8'?>\n" +
  '<S:Envelope xmlns:S="http://www.w3.org/2003/05/soap-envelope">\n' +
  '    <S:Body>\n' +
  '        <ns2:verifyRegReq xmlns:ns2="http://www.gpayments.com/caas/">\n' +
  '            <ns2:card>\n' +
  '                <id></id>\n' +
  '                <number>****************</number>\n' +
  '                <type>VbV</type>\n' +
  '                <context_Blob></context_Blob>\n' +
  '            </ns2:card>\n' +
  '            <ns2:transaction>\n' +
  '                <purchaseAmount>10000</purchaseAmount>\n' +
  '                <purchaseExponent>2</purchaseExponent>\n' +
  '                <purchaseCurrency>036</purchaseCurrency>\n' +
  '                <purchaseDate>2023-06-27T11:26:38.000Z</purchaseDate>\n' +
  '                <merchantId>***************</merchantId>\n' +
  '                <merchantName>Test Merchant</merchantName>\n' +
  '                <merchantCountry>840</merchantCountry>\n' +
  '                <acqBin>41234567890</acqBin>\n' +
  '                <theeDSProtocolVersion>2.1.0</theeDSProtocolVersion>\n' +
  '                <cardExpiry>2508</cardExpiry>\n' +
  '                <issuerName>remote</issuerName>\n' +
  '                <acsTransId>36e6c66a-3dc6-4a59-a722-96c718824981</acsTransId>\n' +
  '                <threeDSTransId>e6e9bf9b-af70-4f20-ad3a-52ce69de1c15</threeDSTransId>\n' +
  '                <dsTransId>2d5e6910-4d86-4d84-b6a3-9125b80b9ba1</dsTransId>\n' +
  '                <threeDSRequestorID>123456789.visa</threeDSRequestorID>\n' +
  '                <threeDSRequestorName>3dsclient.local.visa</threeDSRequestorName>\n' +
  '                <threeDSServerRefNumber>3DS_LOA_SER_GPPL_020100_00075</threeDSServerRefNumber>\n' +
  '                <threeDSServerOperatorID>1jpeeLAWgGFgS1Ri9tX9</threeDSServerOperatorID>\n' +
  '                <threeDSRequestorURL>http://gpayments.com</threeDSRequestorURL>\n' +
  '                <threeDSServerURL>https://amir-test3.testlab.3dsecure.cloud:9605/api/v2/ds/result/request</threeDSServerURL>\n' +
  '                <deviceChannel>02</deviceChannel>\n' +
  '                <dsReferenceNumber>3DS_LOA_ACS_GPPL_020200_00442</dsReferenceNumber>\n' +
  '                <payTokenInd>0</payTokenInd>\n' +
  '                <mcc>2020</mcc>\n' +
  '                <messageCategory>01</messageCategory>\n' +
  '                <transType>01</transType>\n' +
  '                <acctType>03</acctType>\n' +
  '                <threeDSRequestorAuthenticationInd>01</threeDSRequestorAuthenticationInd>\n' +
  '            </ns2:transaction>\n' +
  '            <headerParams>\n' +
  '                <param key="browserJavaEnabled" cookie="false">false</param>\n' +
  '                <param key="browserTZ" cookie="false">-180</param>\n' +
  '                <param key="browserLanguage" cookie="false">en-US</param>\n' +
  '                <param key="Accept-Language" cookie="false">en-US</param>\n' +
  '                <param key="User-Agent" cookie="false">Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</param>\n' +
  '                <param key="Accept" cookie="false">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</param>\n' +
  '                <param key="proxy-ip" cookie="false">************</param>\n' +
  '                <param key="browserColorDepth" cookie="false">24</param>\n' +
  '                <param key="browserScreenHeight" cookie="false">1050</param>\n' +
  '                <param key="browserScreenWidth" cookie="false">1680</param>\n' +
  '            </headerParams>\n' +
  '            <extensionParams/>\n' +
  '            <additionalParams>\n' +
  '                <param key="shipAddrState">NSW</param>\n' +
  '                <param key="shipAddrCity">Sydney</param>\n' +
  '                <param key="shipAddrCountry">036</param>\n' +
  '                <param key="shipAddrLine1">Unit 1</param>\n' +
  '                <param key="shipAddrLine2">123 Street</param>\n' +
  '                <param key="shipAddrPostCode">2000</param>\n' +
  '                <param key="billAddrCity">Sydney</param>\n' +
  '                <param key="billAddrCountry">036</param>\n' +
  '                <param key="billAddrLine1">Unit 1</param>\n' +
  '                <param key="billAddrLine2">123 Street</param>\n' +
  '                <param key="billAddrState">NSW</param>\n' +
  '                <param key="billAddrPostCode">2000</param>\n' +
  '                <param key="threeDSCompInd">U</param>\n' +
  '                <param key="threeDSRequestorAuthenticationInd">01</param>\n' +
  '                <param key="threeDSRequestorChallengeInd">01</param>\n' +
  '                <param key="addrMatch">Y</param>\n' +
  '                <param key="cardExpiryDate">2508</param>\n' +
  '                <param key="acctID">personal account</param>\n' +
  '                <param key="email"><EMAIL></param>\n' +
  '                <param key="mobilePhone.cc">61</param>\n' +
  '                <param key="mobilePhone.subscriber">**********</param>\n' +
  '            </additionalParams>\n' +
  '        </ns2:verifyRegReq>\n' +
  '    </S:Body>\n' +
  '</S:Envelope>'
[2025-05-09T08:01:52.976Z] SOAP info: 'Attempting to bind to /wsdl'
[2025-05-09T08:01:52.977Z] SOAP info: 'Trying VerifyPort from path /wsdl'
[2025-05-09T08:01:52.977Z] SOAP request: {
  methodName: 'verifyReg',
  request: {
    Body: {
      verifyRegReq: {
        card: {
          id: undefined,
          number: '****************',
          type: 'VbV',
          context_Blob: undefined
        },
        transaction: {
          purchaseAmount: '10000',
          purchaseExponent: '2',
          purchaseCurrency: '036',
          purchaseDate: '2023-06-27T11:26:38.000Z',
          merchantId: '***************',
          merchantName: 'Test Merchant',
          merchantCountry: '840',
          acqBin: '41234567890',
          theeDSProtocolVersion: '2.1.0',
          cardExpiry: '2508',
          issuerName: 'remote',
          acsTransId: '36e6c66a-3dc6-4a59-a722-96c718824981',
          threeDSTransId: 'e6e9bf9b-af70-4f20-ad3a-52ce69de1c15',
          dsTransId: '2d5e6910-4d86-4d84-b6a3-9125b80b9ba1',
          threeDSRequestorID: '123456789.visa',
          threeDSRequestorName: '3dsclient.local.visa',
          threeDSServerRefNumber: '3DS_LOA_SER_GPPL_020100_00075',
          threeDSServerOperatorID: '1jpeeLAWgGFgS1Ri9tX9',
          threeDSRequestorURL: 'http://gpayments.com',
          threeDSServerURL: 'https://amir-test3.testlab.3dsecure.cloud:9605/api/v2/ds/result/request',
          deviceChannel: '02',
          dsReferenceNumber: '3DS_LOA_ACS_GPPL_020200_00442',
          payTokenInd: '0',
          mcc: '2020',
          messageCategory: '01',
          transType: '01',
          acctType: '03',
          threeDSRequestorAuthenticationInd: '01'
        },
        headerParams: {
          param: [
            {
              attributes: { key: 'browserJavaEnabled', cookie: 'false' },
              '$value': 'false'
            },
            {
              attributes: { key: 'browserTZ', cookie: 'false' },
              '$value': '-180'
            },
            {
              attributes: { key: 'browserLanguage', cookie: 'false' },
              '$value': 'en-US'
            },
            {
              attributes: { key: 'Accept-Language', cookie: 'false' },
              '$value': 'en-US'
            },
            {
              attributes: { key: 'User-Agent', cookie: 'false' },
              '$value': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
            },
            {
              attributes: { key: 'Accept', cookie: 'false' },
              '$value': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7'
            },
            {
              attributes: { key: 'proxy-ip', cookie: 'false' },
              '$value': '************'
            },
            {
              attributes: { key: 'browserColorDepth', cookie: 'false' },
              '$value': '24'
            },
            {
              attributes: { key: 'browserScreenHeight', cookie: 'false' },
              '$value': '1050'
            },
            {
              attributes: { key: 'browserScreenWidth', cookie: 'false' },
              '$value': '1680'
            }
          ]
        },
        extensionParams: undefined,
        additionalParams: {
          param: [
            { attributes: { key: 'shipAddrState' }, '$value': 'NSW' },
            { attributes: { key: 'shipAddrCity' }, '$value': 'Sydney' },
            { attributes: { key: 'shipAddrCountry' }, '$value': '036' },
            {
              attributes: { key: 'shipAddrLine1' },
              '$value': 'Unit 1'
            },
            {
              attributes: { key: 'shipAddrLine2' },
              '$value': '123 Street'
            },
            {
              attributes: { key: 'shipAddrPostCode' },
              '$value': '2000'
            },
            { attributes: { key: 'billAddrCity' }, '$value': 'Sydney' },
            { attributes: { key: 'billAddrCountry' }, '$value': '036' },
            {
              attributes: { key: 'billAddrLine1' },
              '$value': 'Unit 1'
            },
            {
              attributes: { key: 'billAddrLine2' },
              '$value': '123 Street'
            },
            { attributes: { key: 'billAddrState' }, '$value': 'NSW' },
            {
              attributes: { key: 'billAddrPostCode' },
              '$value': '2000'
            },
            { attributes: { key: 'threeDSCompInd' }, '$value': 'U' },
            {
              attributes: { key: 'threeDSRequestorAuthenticationInd' },
              '$value': '01'
            },
            {
              attributes: { key: 'threeDSRequestorChallengeInd' },
              '$value': '01'
            },
            { attributes: { key: 'addrMatch' }, '$value': 'Y' },
            { attributes: { key: 'cardExpiryDate' }, '$value': '2508' },
            {
              attributes: { key: 'acctID' },
              '$value': 'personal account'
            },
            { attributes: { key: 'email' }, '$value': '<EMAIL>' },
            { attributes: { key: 'mobilePhone.cc' }, '$value': '61' },
            {
              attributes: { key: 'mobilePhone.subscriber' },
              '$value': '**********'
            }
          ]
        }
      }
    }
  }
}
[2025-05-09T08:01:52.991Z] SOAP replied: '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\n' +
  '<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope">\n' +
  '  <soap:Header/>\n' +
  '  <soap:Body/>\n' +
  '</soap:Envelope>'
[2025-05-09T08:06:25.674Z] WSDL loaded: { path: '/Users/<USER>/WebstormProjects/ryvyl-backend/verify.wsdl' }
[2025-05-09T08:06:25.684Z] SOAP 1.2 service attached: { path: '/wsdl' }
[2025-05-09T08:06:25.689Z] SOAP server initialized: {}
[2025-05-09T08:06:25.690Z] Server started: { port: '3001' }
[2025-05-09T08:06:30.205Z] SOAP info: 'Handling POST on /wsdl?wsdl'
[2025-05-09T08:06:30.207Z] SOAP received: '\n' +
  "<?xml version='1.0' encoding='UTF-8'?>\n" +
  '<S:Envelope xmlns:S="http://www.w3.org/2003/05/soap-envelope">\n' +
  '    <S:Body>\n' +
  '        <ns2:verifyRegReq xmlns:ns2="http://www.gpayments.com/caas/">\n' +
  '            <ns2:card>\n' +
  '                <id></id>\n' +
  '                <number>****************</number>\n' +
  '                <type>VbV</type>\n' +
  '                <context_Blob></context_Blob>\n' +
  '            </ns2:card>\n' +
  '            <ns2:transaction>\n' +
  '                <purchaseAmount>10000</purchaseAmount>\n' +
  '                <purchaseExponent>2</purchaseExponent>\n' +
  '                <purchaseCurrency>036</purchaseCurrency>\n' +
  '                <purchaseDate>2023-06-27T11:26:38.000Z</purchaseDate>\n' +
  '                <merchantId>***************</merchantId>\n' +
  '                <merchantName>Test Merchant</merchantName>\n' +
  '                <merchantCountry>840</merchantCountry>\n' +
  '                <acqBin>41234567890</acqBin>\n' +
  '                <theeDSProtocolVersion>2.1.0</theeDSProtocolVersion>\n' +
  '                <cardExpiry>2508</cardExpiry>\n' +
  '                <issuerName>remote</issuerName>\n' +
  '                <acsTransId>36e6c66a-3dc6-4a59-a722-96c718824981</acsTransId>\n' +
  '                <threeDSTransId>e6e9bf9b-af70-4f20-ad3a-52ce69de1c15</threeDSTransId>\n' +
  '                <dsTransId>2d5e6910-4d86-4d84-b6a3-9125b80b9ba1</dsTransId>\n' +
  '                <threeDSRequestorID>123456789.visa</threeDSRequestorID>\n' +
  '                <threeDSRequestorName>3dsclient.local.visa</threeDSRequestorName>\n' +
  '                <threeDSServerRefNumber>3DS_LOA_SER_GPPL_020100_00075</threeDSServerRefNumber>\n' +
  '                <threeDSServerOperatorID>1jpeeLAWgGFgS1Ri9tX9</threeDSServerOperatorID>\n' +
  '                <threeDSRequestorURL>http://gpayments.com</threeDSRequestorURL>\n' +
  '                <threeDSServerURL>https://amir-test3.testlab.3dsecure.cloud:9605/api/v2/ds/result/request</threeDSServerURL>\n' +
  '                <deviceChannel>02</deviceChannel>\n' +
  '                <dsReferenceNumber>3DS_LOA_ACS_GPPL_020200_00442</dsReferenceNumber>\n' +
  '                <payTokenInd>0</payTokenInd>\n' +
  '                <mcc>2020</mcc>\n' +
  '                <messageCategory>01</messageCategory>\n' +
  '                <transType>01</transType>\n' +
  '                <acctType>03</acctType>\n' +
  '                <threeDSRequestorAuthenticationInd>01</threeDSRequestorAuthenticationInd>\n' +
  '            </ns2:transaction>\n' +
  '            <headerParams>\n' +
  '                <param key="browserJavaEnabled" cookie="false">false</param>\n' +
  '                <param key="browserTZ" cookie="false">-180</param>\n' +
  '                <param key="browserLanguage" cookie="false">en-US</param>\n' +
  '                <param key="Accept-Language" cookie="false">en-US</param>\n' +
  '                <param key="User-Agent" cookie="false">Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</param>\n' +
  '                <param key="Accept" cookie="false">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</param>\n' +
  '                <param key="proxy-ip" cookie="false">************</param>\n' +
  '                <param key="browserColorDepth" cookie="false">24</param>\n' +
  '                <param key="browserScreenHeight" cookie="false">1050</param>\n' +
  '                <param key="browserScreenWidth" cookie="false">1680</param>\n' +
  '            </headerParams>\n' +
  '            <extensionParams/>\n' +
  '            <additionalParams>\n' +
  '                <param key="shipAddrState">NSW</param>\n' +
  '                <param key="shipAddrCity">Sydney</param>\n' +
  '                <param key="shipAddrCountry">036</param>\n' +
  '                <param key="shipAddrLine1">Unit 1</param>\n' +
  '                <param key="shipAddrLine2">123 Street</param>\n' +
  '                <param key="shipAddrPostCode">2000</param>\n' +
  '                <param key="billAddrCity">Sydney</param>\n' +
  '                <param key="billAddrCountry">036</param>\n' +
  '                <param key="billAddrLine1">Unit 1</param>\n' +
  '                <param key="billAddrLine2">123 Street</param>\n' +
  '                <param key="billAddrState">NSW</param>\n' +
  '                <param key="billAddrPostCode">2000</param>\n' +
  '                <param key="threeDSCompInd">U</param>\n' +
  '                <param key="threeDSRequestorAuthenticationInd">01</param>\n' +
  '                <param key="threeDSRequestorChallengeInd">01</param>\n' +
  '                <param key="addrMatch">Y</param>\n' +
  '                <param key="cardExpiryDate">2508</param>\n' +
  '                <param key="acctID">personal account</param>\n' +
  '                <param key="email"><EMAIL></param>\n' +
  '                <param key="mobilePhone.cc">61</param>\n' +
  '                <param key="mobilePhone.subscriber">**********</param>\n' +
  '            </additionalParams>\n' +
  '        </ns2:verifyRegReq>\n' +
  '    </S:Body>\n' +
  '</S:Envelope>'
[2025-05-09T08:06:30.217Z] SOAP info: 'Attempting to bind to /wsdl'
[2025-05-09T08:06:30.218Z] SOAP info: 'Trying VerifyPort from path /wsdl'
[2025-05-09T08:06:30.218Z] SOAP request: {
  methodName: 'verifyReg',
  request: {
    Body: {
      verifyRegReq: {
        card: {
          id: null,
          number: '****************',
          type: 'VbV',
          context_Blob: null
        },
        transaction: {
          purchaseAmount: '10000',
          purchaseExponent: '2',
          purchaseCurrency: '036',
          purchaseDate: '2023-06-27T11:26:38.000Z',
          merchantId: '***************',
          merchantName: 'Test Merchant',
          merchantCountry: '840',
          acqBin: '41234567890',
          theeDSProtocolVersion: '2.1.0',
          cardExpiry: '2508',
          issuerName: 'remote',
          acsTransId: '36e6c66a-3dc6-4a59-a722-96c718824981',
          threeDSTransId: 'e6e9bf9b-af70-4f20-ad3a-52ce69de1c15',
          dsTransId: '2d5e6910-4d86-4d84-b6a3-9125b80b9ba1',
          threeDSRequestorID: '123456789.visa',
          threeDSRequestorName: '3dsclient.local.visa',
          threeDSServerRefNumber: '3DS_LOA_SER_GPPL_020100_00075',
          threeDSServerOperatorID: '1jpeeLAWgGFgS1Ri9tX9',
          threeDSRequestorURL: 'http://gpayments.com',
          threeDSServerURL: 'https://amir-test3.testlab.3dsecure.cloud:9605/api/v2/ds/result/request',
          deviceChannel: '02',
          dsReferenceNumber: '3DS_LOA_ACS_GPPL_020200_00442',
          payTokenInd: '0',
          mcc: '2020',
          messageCategory: '01',
          transType: '01',
          acctType: '03',
          threeDSRequestorAuthenticationInd: '01'
        },
        headerParams: {
          param: [
            {
              attributes: { key: 'browserJavaEnabled', cookie: 'false' },
              '$value': 'false'
            },
            {
              attributes: { key: 'browserTZ', cookie: 'false' },
              '$value': '-180'
            },
            {
              attributes: { key: 'browserLanguage', cookie: 'false' },
              '$value': 'en-US'
            },
            {
              attributes: { key: 'Accept-Language', cookie: 'false' },
              '$value': 'en-US'
            },
            {
              attributes: { key: 'User-Agent', cookie: 'false' },
              '$value': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
            },
            {
              attributes: { key: 'Accept', cookie: 'false' },
              '$value': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7'
            },
            {
              attributes: { key: 'proxy-ip', cookie: 'false' },
              '$value': '************'
            },
            {
              attributes: { key: 'browserColorDepth', cookie: 'false' },
              '$value': '24'
            },
            {
              attributes: { key: 'browserScreenHeight', cookie: 'false' },
              '$value': '1050'
            },
            {
              attributes: { key: 'browserScreenWidth', cookie: 'false' },
              '$value': '1680'
            }
          ]
        },
        extensionParams: null,
        additionalParams: {
          param: [
            { attributes: { key: 'shipAddrState' }, '$value': 'NSW' },
            { attributes: { key: 'shipAddrCity' }, '$value': 'Sydney' },
            { attributes: { key: 'shipAddrCountry' }, '$value': '036' },
            {
              attributes: { key: 'shipAddrLine1' },
              '$value': 'Unit 1'
            },
            {
              attributes: { key: 'shipAddrLine2' },
              '$value': '123 Street'
            },
            {
              attributes: { key: 'shipAddrPostCode' },
              '$value': '2000'
            },
            { attributes: { key: 'billAddrCity' }, '$value': 'Sydney' },
            { attributes: { key: 'billAddrCountry' }, '$value': '036' },
            {
              attributes: { key: 'billAddrLine1' },
              '$value': 'Unit 1'
            },
            {
              attributes: { key: 'billAddrLine2' },
              '$value': '123 Street'
            },
            { attributes: { key: 'billAddrState' }, '$value': 'NSW' },
            {
              attributes: { key: 'billAddrPostCode' },
              '$value': '2000'
            },
            { attributes: { key: 'threeDSCompInd' }, '$value': 'U' },
            {
              attributes: { key: 'threeDSRequestorAuthenticationInd' },
              '$value': '01'
            },
            {
              attributes: { key: 'threeDSRequestorChallengeInd' },
              '$value': '01'
            },
            { attributes: { key: 'addrMatch' }, '$value': 'Y' },
            { attributes: { key: 'cardExpiryDate' }, '$value': '2508' },
            {
              attributes: { key: 'acctID' },
              '$value': 'personal account'
            },
            { attributes: { key: 'email' }, '$value': '<EMAIL>' },
            { attributes: { key: 'mobilePhone.cc' }, '$value': '61' },
            {
              attributes: { key: 'mobilePhone.subscriber' },
              '$value': '**********'
            }
          ]
        }
      }
    }
  }
}
[2025-05-09T08:06:30.534Z] Connected to MongoDB: {}
[2025-05-09T08:06:33.735Z] SOAP response: {
  methodName: 'verifyReg',
  response: {
    result: '<?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/"  xmlns:tns="http://www.gpayments.com/caas/"><soap:Body><tns:verifyRegResp><verifyRegResp>{&quot;cardInfo&quot;:{&quot;card_ID&quot;:&quot;****************&quot;,&quot;context_Blob&quot;:&quot;xyz123&quot;,&quot;regStatus&quot;:0,&quot;authRequired&quot;:1,&quot;authTypeSup&quot;:[1,2],&quot;lanCode&quot;:&quot;&quot;,&quot;twoFA&quot;:true},&quot;code&quot;:1,&quot;errorMessage&quot;:&quot;Card found&quot;,&quot;errorDetail&quot;:&quot;Card info fetched successfully&quot;}</verifyRegResp></tns:verifyRegResp></soap:Body></soap:Envelope>'
  }
}
[2025-05-09T08:06:33.738Z] SOAP replied: '<?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/"  xmlns:tns="http://www.gpayments.com/caas/"><soap:Body><tns:verifyRegResp><verifyRegResp>{&quot;cardInfo&quot;:{&quot;card_ID&quot;:&quot;****************&quot;,&quot;context_Blob&quot;:&quot;xyz123&quot;,&quot;regStatus&quot;:0,&quot;authRequired&quot;:1,&quot;authTypeSup&quot;:[1,2],&quot;lanCode&quot;:&quot;&quot;,&quot;twoFA&quot;:true},&quot;code&quot;:1,&quot;errorMessage&quot;:&quot;Card found&quot;,&quot;errorDetail&quot;:&quot;Card info fetched successfully&quot;}</verifyRegResp></tns:verifyRegResp></soap:Body></soap:Envelope>'
