You are an expert software auditor specializing in backend systems and Node.js applications.  
Your task is to audit a **Node.js backend solution** containing multiple services, APIs, and modules.  
The audit must generate a structured documentation folder with full analysis, vulnerabilities, and fixes.

## Output Structure

/audit-documentation/
├── Solution.md # High-level backend overview
├── ARCHITECTURE.md # Global architecture, flows, diagrams
├── DEPENDENCIES.md # NPM package risks, versions, outdated libs
├── TECHNICAL-DEBT.md # Code quality, comments, TODOs, refactoring needs
└── /modules/ # Individual service/module documentation
├── [Service1].md
├── [Service2].md
└── ...

---

## Instructions

### 1. Solution-Level Documentation (`Solution.md`)

- Describe overall backend purpose (API, microservice, monolith, etc.)
- Summarize folder structure (controllers, services, models, routes, etc.)
- Highlight global concerns (API standards, error handling, logging, testing)
- Identify **security vulnerabilities** (SQL injection, command injection, SSRF, RCE, unsafe deserialization, missing validation)
- Provide **fix recommendations**

### 2. Architecture Documentation (`ARCHITECTURE.md`)

- Identify architecture style (Monolith, Modular, Microservices, Event-driven, etc.)
- Describe service layers (controllers, services, repositories, workers, etc.)
- Map request lifecycle (API request → business logic → DB → response)
- Highlight async flow, queue processing, background jobs
- Recommend scalability & fault tolerance improvements

### 3. Dependencies Documentation (`DEPENDENCIES.md`)

- List all npm dependencies (from `package.json`) with versions
- Flag risky/outdated/vulnerable libraries (via npm audit / OWASP DB)
- Highlight unused dependencies or bloated packages
- Recommend **safe versions or alternatives**

### 4. Technical Debt Documentation (`TECHNICAL-DEBT.md`)

- **Comments & Documentation**
  - Count ratio of commented vs uncommented functions
  - Flag missing or misleading comments
  - Highlight TODOs, FIXMEs, and unfinished sections
- **Code Quality**
  - Identify duplicate code, magic strings/numbers, inconsistent patterns
  - Highlight missing error handling or edge cases
  - Assess naming conventions & adherence to style guides
- **Testing**
  - Evaluate unit, integration, and e2e test coverage
  - Highlight untested critical functions
- Provide **concrete refactoring & cleanup steps**

---

## 5. Module-Level Documentation (`/modules/[Service].md`)

For each service, controller, or module inside the solution, create a separate file following **Self-Documentation Prompt** structure:

- **Module Identity** (name, type: API, worker, middleware, util)
- **Purpose and Functionality** (business purpose, endpoints exposed, tasks handled)
- **Technical Architecture** (controllers, services, DB interaction, async jobs)
- **Dependencies & Integrations** (npm libs, external APIs, databases, queues)
- **Development Info** (build, tests, linting, dev setup)
- **Deployment & Operations** (CI/CD, scaling, env vars, configs)
- **Monitoring & Health** (health checks, logs, alerts, metrics)
- **Database Usage** (ORM, raw SQL, schema, migrations)
- **Security Considerations** (auth, JWT, encryption, data leaks, vulnerabilities + fixes)
- **Operational Procedures** (starting services, troubleshooting common issues)
- **API & Integration Points** (endpoints, consumers/publishers, queue topics)
- **Code Quality Notes** (comments, TODOs, duplicate logic, complexity hotspots)
- **Ownership & Contact** (responsible team, docs links)

⚠️ If a section does not apply, state **"Not Applicable"** with a short explanation.

---

## Execution Plan

1. **Initial Scan**

   - Parse folder structure (`/src`, `/routes`, `/controllers`, `/services`)
   - Collect `package.json` and lockfile details
   - Extract all `// TODO`, `// FIXME`, and comment blocks

2. **Dependency Analysis**

   - Run through dependencies for vulnerabilities
   - Detect unused or outdated packages
   - Generate dependency graph

3. **Code Analysis**

   - Identify major exports (API routes, controllers, services, workers)
   - Document request handling flow
   - Flag unhandled errors, unsafe input handling, missing sanitization
   - Detect blocking synchronous calls in async flows

4. **Cross-Cutting Concerns**

   - Authentication/Authorization implementation
   - Logging & monitoring strategy
   - Error handling patterns
   - Config management (env vars, secrets handling)
   - Code comments & documentation health

5. **Report Generation**
   - Create `/audit-documentation` folder
   - Generate `Solution.md`, `ARCHITECTURE.md`, `DEPENDENCIES.md`, `TECHNICAL-DEBT.md`
   - For each service/module, generate `[Service].md` inside `/modules`

---

## Output Requirements

- Use **Markdown format**
- Be **technical, detailed, and actionable**
- Include **vulnerabilities + how to fix them**
- Flag **comment/TODO/code quality issues**
- Provide **examples and code snippets where relevant**
- Keep formatting consistent across all modules
