


    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Virtual Prepaid Gift Cards – Perfect for Every Occasion</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Hero Section */
        .hero {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            display: flex;
            align-items: center;
            position: relative;
            overflow: hidden;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><circle cx="200" cy="200" r="100" fill="rgba(255,255,255,0.1)"/><circle cx="800" cy="300" r="150" fill="rgba(255,255,255,0.05)"/><circle cx="400" cy="700" r="120" fill="rgba(255,255,255,0.08)"/></svg>');
            animation: float 20s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        .hero-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 60px;
            align-items: center;
            position: relative;
            z-index: 1;
        }

        .hero-text h1 {
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 20px;
            line-height: 1.2;
            animation: slideInLeft 1s ease-out;
        }

        .hero-text p {
            font-size: 1.3rem;
            margin-bottom: 30px;
            opacity: 0.95;
            animation: slideInLeft 1s ease-out 0.2s both;
        }

        .cta-button {
            display: inline-block;
            background: linear-gradient(45deg, #ff6b6b, #ffa500);
            color: white;
            padding: 18px 40px;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            box-shadow: 0 10px 30px rgba(255, 107, 107, 0.3);
            animation: slideInLeft 1s ease-out 0.4s both;
        }

        .cta-button:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(255, 107, 107, 0.4);
        }

        .hero-visual {
            position: relative;
            animation: slideInRight 1s ease-out;
        }

        .gift-card-mockup {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 30px 60px rgba(0, 0, 0, 0.3);
            transform: rotate(-5deg);
            transition: transform 0.3s ease;
        }

        .gift-card-mockup:hover {
            transform: rotate(0deg) scale(1.05);
        }

        .card-content {
            background: white;
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            color: #333;
        }

        .card-logo {
            font-size: 1.5rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 15px;
        }

        .card-value {
            font-size: 3rem;
            font-weight: 700;
            color: #ff6b6b;
            margin-bottom: 10px;
        }

        .card-text {
            color: #666;
            font-size: 1.1rem;
        }

        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-50px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(50px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        /* What is Virtual Gift Card Section */
        .what-is-section {
            padding: 100px 0;
            background: #f8f9fa;
        }

        .what-is-section h2 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 40px;
            color: #333;
        }

        .what-is-content {
            max-width: 800px;
            margin: 0 auto;
            font-size: 1.1rem;
            line-height: 1.8;
            color: #555;
            text-align: center;
        }

        .what-is-content p {
            margin-bottom: 25px;
        }

        /* Features Section */
        .features {
            padding: 100px 0;
            background: white;
        }

        .features h2 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 20px;
            color: #333;
        }

        .features-subtitle {
            text-align: center;
            font-size: 1.2rem;
            color: #666;
            margin-bottom: 60px;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 40px;
        }

        .feature-card {
            background: white;
            padding: 40px 30px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
            border: 1px solid #f0f0f0;
        }

        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .feature-icon {
            font-size: 3rem;
            margin-bottom: 20px;
            display: block;
        }

        .feature-card h3 {
            font-size: 1.5rem;
            margin-bottom: 15px;
            color: #333;
        }

        .feature-card p {
            color: #666;
            line-height: 1.6;
        }

        /* How It Works Section */
        .how-it-works {
            padding: 100px 0;
            background: #f8f9fa;
        }

        .how-it-works h2 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 20px;
            color: #333;
        }

        .how-it-works-subtitle {
            text-align: center;
            font-size: 1.2rem;
            color: #666;
            margin-bottom: 60px;
        }

        .steps-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .step-card {
            background: white;
            padding: 30px 20px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
            position: relative;
        }

        .step-card:hover {
            transform: translateY(-5px);
        }

        .step-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            display: block;
        }

        .step-card h3 {
            font-size: 1.3rem;
            margin-bottom: 10px;
            color: #333;
        }

        .step-card p {
            color: #666;
            font-size: 0.95rem;
            line-height: 1.5;
        }

        .step-number {
            position: absolute;
            top: -15px;
            left: 50%;
            transform: translateX(-50%);
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 0.9rem;
        }

        /* Key Features Section */
        .key-features {
            padding: 100px 0;
            background: white;
        }

        .key-features h2 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 60px;
            color: #333;
        }

        .key-features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 40px;
        }

        .key-feature-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px 30px;
            border-radius: 15px;
            text-align: center;
            transition: transform 0.3s ease;
        }

        .key-feature-card:hover {
            transform: translateY(-10px);
        }

        .key-feature-card h3 {
            font-size: 1.5rem;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .key-feature-card p {
            opacity: 0.95;
            line-height: 1.6;
        }

        /* Why Choose Section */
        .why-choose {
            padding: 100px 0;
            background: #f8f9fa;
        }

        .why-choose h2 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 20px;
            color: #333;
        }

        .why-choose-subtitle {
            text-align: center;
            font-size: 1.2rem;
            color: #666;
            margin-bottom: 60px;
        }

        .why-choose-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
        }

        .why-choose-card {
            background: white;
            padding: 35px 25px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .why-choose-card:hover {
            transform: translateY(-8px);
        }

        .why-choose-card h3 {
            font-size: 1.4rem;
            margin-bottom: 15px;
            color: #333;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .why-choose-card p {
            color: #666;
            line-height: 1.6;
        }

        /* FAQ Section */
        .faq {
            padding: 100px 0;
            background: white;
        }

        .faq h2 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 20px;
            color: #333;
        }

        .faq-subtitle {
            text-align: center;
            font-size: 1.2rem;
            color: #666;
            margin-bottom: 60px;
        }

        .faq-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
        }

        .faq-item {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 15px;
            border-left: 4px solid #667eea;
        }

        .faq-item h3 {
            font-size: 1.3rem;
            margin-bottom: 15px;
            color: #333;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .faq-item p {
            color: #666;
            line-height: 1.6;
        }

        /* Contact Section */
        .contact {
            padding: 100px 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .contact h2 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 20px;
        }

        .contact-subtitle {
            text-align: center;
            font-size: 1.2rem;
            margin-bottom: 60px;
            opacity: 0.9;
        }

        .contact-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 40px;
        }

        .contact-card {
            background: rgba(255, 255, 255, 0.1);
            padding: 40px 30px;
            border-radius: 15px;
            text-align: center;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: transform 0.3s ease;
        }

        .contact-card:hover {
            transform: translateY(-5px);
            background: rgba(255, 255, 255, 0.15);
        }

        .contact-card h3 {
            font-size: 1.5rem;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .contact-card p {
            opacity: 0.95;
            line-height: 1.6;
        }

        /* CTA Section */
        .cta-section {
            background: #333;
            color: white;
            padding: 80px 0;
            text-align: center;
        }

        .cta-section h2 {
            font-size: 2.5rem;
            margin-bottom: 20px;
        }

        .cta-section p {
            font-size: 1.2rem;
            margin-bottom: 40px;
            opacity: 0.9;
        }

        .cta-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn-primary {
            background: linear-gradient(45deg, #ff6b6b, #ffa500);
            color: white;
            padding: 18px 40px;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 10px 30px rgba(255, 107, 107, 0.3);
        }

        .btn-secondary {
            background: transparent;
            color: white;
            padding: 18px 40px;
            border: 2px solid white;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary:hover, .btn-secondary:hover {
            transform: translateY(-3px);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .hero-content {
                grid-template-columns: 1fr;
                text-align: center;
                gap: 40px;
            }

            .hero-text h1 {
                font-size: 2.5rem;
            }

            .features-grid, .steps-grid, .key-features-grid, .why-choose-grid {
                grid-template-columns: 1fr;
            }

            .faq-grid {
                grid-template-columns: 1fr;
            }

            .contact-grid {
                grid-template-columns: 1fr;
            }

            .cta-buttons {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>


    <!-- Hero Section -->
    <section class="hero">
        <div class="container">
            <div class="hero-content">
                <div class="hero-text">
                    <h1>Virtual Prepaid Gift Cards</h1>
                    <p>A secure, digital alternative to traditional plastic gift cards. Delivered instantly via email – perfect for last-minute gifts, global gifting, or eco-conscious consumers.</p>
                    <a href="#contact" class="cta-button">Get Your Gift Card</a>
                </div>
                <div class="hero-visual">
                    <div class="gift-card-mockup">
                        <div class="card-content">
                            <div class="card-logo"><img draggable="false" role="img" class="emoji" alt="💳" src="https://s.w.org/images/core/emoji/16.0.1/svg/1f4b3.svg"> VISA GIFT CARD</div>
                            <div class="card-value">$100</div>
                            <div class="card-text">Ready to use instantly</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- What is Virtual Gift Card Section -->
    <section class="what-is-section">
        <div class="container">
            <h2>What is a Virtual Prepaid Gift Card?</h2>
            <div class="what-is-content">
                <p>A Virtual Prepaid Gift Card is a secure, digital alternative to traditional plastic gift cards. Delivered instantly via email or app, these cards can be used to shop online, subscribe to digital services, or pay bills—just like a regular debit or credit card.</p>
                <p>Issued by trusted financial institutions or renowned retail brands, virtual gift cards come preloaded with a set balance. They require no physical delivery, making them ideal for last-minute gifts, global gifting, or eco-conscious consumers.</p>
                <p>As long as the merchant accepts prepaid cards, users can spend the balance anywhere online. It’s fast, flexible, and easy to use—no bank account or credit check required.</p>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features">
        <div class="container">
            <h2><img draggable="false" role="img" class="emoji" alt="🎉" src="https://s.w.org/images/core/emoji/16.0.1/svg/1f389.svg"> Why Choose Our Gift Cards?</h2>
            <p class="features-subtitle">A smarter, safer, and more flexible way to shop, gift, and celebrate with prepaid Visa &amp; Mastercard gift cards.</p>
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon"><img draggable="false" role="img" class="emoji" alt="🛍️" src="https://s.w.org/images/core/emoji/16.0.1/svg/1f6cd.svg"></div>
                    <h3>Wide Range of Gift Cards</h3>
                    <p>Choose from prepaid Visa and Mastercard cards in various amounts with design and message customization — perfect for any occasion.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon"><img draggable="false" role="img" class="emoji" alt="⚙️" src="https://s.w.org/images/core/emoji/16.0.1/svg/2699.svg"></div>
                    <h3>Easy Activation</h3>
                    <p>Activate your card online in just a few clicks — quick, easy, and secure from start to finish.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon"><img draggable="false" role="img" class="emoji" alt="📊" src="https://s.w.org/images/core/emoji/16.0.1/svg/1f4ca.svg"></div>
                    <h3>Balance &amp; Management</h3>
                    <p>Easily check your balance, monitor your transactions, and stay in control via the website or customer support.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon"><img draggable="false" role="img" class="emoji" alt="🎨" src="https://s.w.org/images/core/emoji/16.0.1/svg/1f3a8.svg"></div>
                    <h3>Personalized Gifts</h3>
                    <p>Add custom designs and heartfelt messages to physical cards — perfect for birthdays, holidays, or any celebration.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon"><img draggable="false" role="img" class="emoji" alt="🔐" src="https://s.w.org/images/core/emoji/16.0.1/svg/1f510.svg"></div>
                    <h3>Secure &amp; Versatile</h3>
                    <p>Not linked to a bank account, prepaid cards offer enhanced security and can be used anywhere Visa or Mastercard is accepted — worldwide.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- How It Works Section -->
    <section class="how-it-works">
        <div class="container">
            <h2><img draggable="false" role="img" class="emoji" alt="🚀" src="https://s.w.org/images/core/emoji/16.0.1/svg/1f680.svg"> How Online Gift Cards Work</h2>
            <p class="how-it-works-subtitle">Sending or using an online gift card is fast, easy, and seamless. Whether you’re gifting a friend or rewarding an employee, follow these simple steps:</p>
            <div class="steps-grid">
                <div class="step-card">
                    <div class="step-number">1</div>
                    <div class="step-icon"><img draggable="false" role="img" class="emoji" alt="🛍️" src="https://s.w.org/images/core/emoji/16.0.1/svg/1f6cd.svg"></div>
                    <h3>Choose a Card</h3>
                    <p>Select a virtual Visa, Mastercard, or store-branded card and your amount ($10–$500).</p>
                </div>
                <div class="step-card">
                    <div class="step-number">2</div>
                    <div class="step-icon"><img draggable="false" role="img" class="emoji" alt="✉️" src="https://s.w.org/images/core/emoji/16.0.1/svg/2709.svg"></div>
                    <h3>Enter Recipient Info</h3>
                    <p>Provide an email or phone number. You can also add a custom message.</p>
                </div>
                <div class="step-card">
                    <div class="step-number">3</div>
                    <div class="step-icon"><img draggable="false" role="img" class="emoji" alt="💳" src="https://s.w.org/images/core/emoji/16.0.1/svg/1f4b3.svg"></div>
                    <h3>Make Payment</h3>
                    <p>Pay securely with a debit/credit card or supported payment method.</p>
                </div>
                <div class="step-card">
                    <div class="step-number">4</div>
                    <div class="step-icon"><img draggable="false" role="img" class="emoji" alt="📬" src="https://s.w.org/images/core/emoji/16.0.1/svg/1f4ec.svg"></div>
                    <h3>Instant Delivery</h3>
                    <p>The recipient receives the card within seconds, with usage instructions included.</p>
                </div>
                <div class="step-card">
                    <div class="step-number">5</div>
                    <div class="step-icon"><img draggable="false" role="img" class="emoji" alt="🛒" src="https://s.w.org/images/core/emoji/16.0.1/svg/1f6d2.svg"></div>
                    <h3>Use Online</h3>
                    <p>Just enter the card number at checkout — no physical card needed.</p>
                </div>
            </div>
            <div style="text-align: center; margin-top: 40px;">
                <p style="font-size: 1.1rem; color: #666;">Virtual gift cards are fast, flexible, eco-friendly, and perfect for any occasion.</p>
            </div>
        </div>
    </section>

    <!-- Key Features Section -->
    <section class="key-features">
        <div class="container">
            <h2><img draggable="false" role="img" class="emoji" alt="💳" src="https://s.w.org/images/core/emoji/16.0.1/svg/1f4b3.svg"> Key Features</h2>
            <div class="key-features-grid">
                <div class="key-feature-card">
                    <h3><img draggable="false" role="img" class="emoji" alt="✅" src="https://s.w.org/images/core/emoji/16.0.1/svg/2705.svg"> Universal Acceptance</h3>
                    <p>Use anywhere Visa or Mastercard is accepted – globally.</p>
                </div>
                <div class="key-feature-card">
                    <h3><img draggable="false" role="img" class="emoji" alt="🔐" src="https://s.w.org/images/core/emoji/16.0.1/svg/1f510.svg"> Safe &amp; Secure</h3>
                    <p>Reduced risk of identity theft or unauthorized use.</p>
                </div>
                <div class="key-feature-card">
                    <h3><img draggable="false" role="img" class="emoji" alt="📱" src="https://s.w.org/images/core/emoji/16.0.1/svg/1f4f1.svg"> Virtual Convenience</h3>
                    <p>Instantly issued and ready to use for online and mobile purchases.</p>
                </div>
                <div class="key-feature-card">
                    <h3><img draggable="false" role="img" class="emoji" alt="📦" src="https://s.w.org/images/core/emoji/16.0.1/svg/1f4e6.svg"> Physical Cards Available</h3>
                    <p>Great for gifting, with custom designs and packaging options.</p>
                </div>
                <div class="key-feature-card">
                    <h3><img draggable="false" role="img" class="emoji" alt="💼" src="https://s.w.org/images/core/emoji/16.0.1/svg/1f4bc.svg"> Perfect for Gifting or Business</h3>
                    <p>Can be used for personal gifts, employee rewards, or promotional campaigns.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Why Choose Section -->
    <section class="why-choose">
        <div class="container">
            <h2><img draggable="false" role="img" class="emoji" alt="🎁" src="https://s.w.org/images/core/emoji/16.0.1/svg/1f381.svg"> Why Choose Online Gift Cards?</h2>
            <p class="why-choose-subtitle">Whether it’s a birthday, holiday, thank-you, or just because — online gift cards are a thoughtful and flexible solution. No shipping delays, no wrapping paper, no stress.</p>
            <div class="why-choose-grid">
                <div class="why-choose-card">
                    <h3><img draggable="false" role="img" class="emoji" alt="🌍" src="https://s.w.org/images/core/emoji/16.0.1/svg/1f30d.svg"> Global Acceptance</h3>
                    <p>Use anywhere Visa or Mastercard is accepted — online or by phone.</p>
                </div>
                <div class="why-choose-card">
                    <h3><img draggable="false" role="img" class="emoji" alt="🎯" src="https://s.w.org/images/core/emoji/16.0.1/svg/1f3af.svg"> Personalized Gifting</h3>
                    <p>Add a name, message, and themed design to make it truly yours.</p>
                </div>
                <div class="why-choose-card">
                    <h3><img draggable="false" role="img" class="emoji" alt="🧾" src="https://s.w.org/images/core/emoji/16.0.1/svg/1f9fe.svg"> Trackable &amp; Transparent</h3>
                    <p>Know exactly when your gift card is delivered and used.</p>
                </div>
                <div class="why-choose-card">
                    <h3><img draggable="false" role="img" class="emoji" alt="🚀" src="https://s.w.org/images/core/emoji/16.0.1/svg/1f680.svg"> Delivered in Seconds</h3>
                    <p>Perfect for last-minute gifts — delivered instantly to email or phone.</p>
                </div>
                <div class="why-choose-card">
                    <h3><img draggable="false" role="img" class="emoji" alt="♻️" src="https://s.w.org/images/core/emoji/16.0.1/svg/267b.svg"> Eco-Friendly</h3>
                    <p>No plastic, no postage, no waste. 100% paperless solution.</p>
                </div>
                <div class="why-choose-card">
                    <h3><img draggable="false" role="img" class="emoji" alt="💼" src="https://s.w.org/images/core/emoji/16.0.1/svg/1f4bc.svg"> Great for Teams</h3>
                    <p>Bulk send to employees, clients, or customers in just a few clicks.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- FAQ Section -->
    <section class="faq">
        <div class="container">
            <h2><img draggable="false" role="img" class="emoji" alt="❓" src="https://s.w.org/images/core/emoji/16.0.1/svg/2753.svg"> Frequently Asked Questions</h2>
            <p class="faq-subtitle">Answers to common questions about our virtual and physical gift cards.</p>
            <div class="faq-grid">
                <div class="faq-item">
                    <h3><img draggable="false" role="img" class="emoji" alt="🔒" src="https://s.w.org/images/core/emoji/16.0.1/svg/1f512.svg"> Are online gift cards secure?</h3>
                    <p>Yes! All cards are encrypted, and since they’re prepaid, they are not linked to any bank account or personal info.</p>
                </div>
                <div class="faq-item">
                    <h3><img draggable="false" role="img" class="emoji" alt="🌍" src="https://s.w.org/images/core/emoji/16.0.1/svg/1f30d.svg"> Can I use a gift card internationally?</h3>
                    <p>Absolutely. Virtual Visa and Mastercard gift cards can be used globally wherever these networks are accepted.</p>
                </div>
                <div class="faq-item">
                    <h3><img draggable="false" role="img" class="emoji" alt="📬" src="https://s.w.org/images/core/emoji/16.0.1/svg/1f4ec.svg"> How are virtual cards delivered?</h3>
                    <p>Virtual cards are delivered instantly to the recipient’s email or phone number with usage instructions.</p>
                </div>
                <div class="faq-item">
                    <h3><img draggable="false" role="img" class="emoji" alt="📆" src="https://s.w.org/images/core/emoji/16.0.1/svg/1f4c6.svg"> Do gift cards expire?</h3>
                    <p>Yes, typically within 12 to 36 months. Expiry details are always provided during purchase and on the card itself.</p>
                </div>
                <div class="faq-item">
                    <h3><img draggable="false" role="img" class="emoji" alt="💳" src="https://s.w.org/images/core/emoji/16.0.1/svg/1f4b3.svg"> How do I check my card balance?</h3>
                    <p>Visit the “Check Balance” page on our website and enter your card number and CVV to view your available balance.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section class="contact">
        <div class="container">
            <h2><img draggable="false" role="img" class="emoji" alt="📞" src="https://s.w.org/images/core/emoji/16.0.1/svg/1f4de.svg"> Contact Us</h2>
            <p class="contact-subtitle">Need help? We’re here to assist you with your questions, orders, and support needs.</p>
            <div class="contact-grid">
                <div class="contact-card">
                    <h3><img draggable="false" role="img" class="emoji" alt="📬" src="https://s.w.org/images/core/emoji/16.0.1/svg/1f4ec.svg"> Email Support</h3>
                    <p><EMAIL></p>
                </div>
                <div class="contact-card">
                    <h3><img draggable="false" role="img" class="emoji" alt="📞" src="https://s.w.org/images/core/emoji/16.0.1/svg/1f4de.svg"> Call Us</h3>
                    <p>+1 (800) 201-1882</p>
                </div>
                <div class="contact-card">
                    <h3><img draggable="false" role="img" class="emoji" alt="💬" src="https://s.w.org/images/core/emoji/16.0.1/svg/1f4ac.svg"> Live Chat</h3>
                    <p>Available Mon–Sat, 9AM–9PM</p>
                </div>
                <div class="contact-card">
                    <h3><img draggable="false" role="img" class="emoji" alt="📍" src="https://s.w.org/images/core/emoji/16.0.1/svg/1f4cd.svg"> Address</h3>
                    <p>Street 92, Gift Card Plaza 2nd Floor, New York, NY 10001</p>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="cta-section" id="contact">
        <div class="container">
            <h2>Ready to Give the Perfect Gift?</h2>
            <p>Start shopping for gift cards today and make someone’s day special</p>
            <div class="cta-buttons">
                <button class="btn-primary">Buy an Online Gift Card</button>
                <button class="btn-secondary">Check Balance</button>
            </div>
        </div>
    </section>

