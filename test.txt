# Send an email with curl

# Copy and paste this into terminal
curl "https://api.postmarkapp.com/email/withTemplate" \
  -X POST \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  -H "X-Postmark-Server-Token: ************************************" \
  -d '{
  "From": "<EMAIL>",
  "To": "<EMAIL>",
  "TemplateAlias": "welcome",
  "TemplateModel": {
    "name": "",
    "email": "email_Value",
    "password": "password_Value",
    "product_name": "product_name_Value",
    "action_url": "action_url_Value",
    "login_url": "login_url_Value",
    "username": "username_Value",
    "trial_length": "trial_length_Value",
    "trial_start_date": "trial_start_date_Value",
    "trial_end_date": "trial_end_date_Value",
    "support_email": "support_email_Value",
    "live_chat_url": "live_chat_url_Value",
    "sender_name": "sender_name_Value",
    "help_url": "help_url_Value"
  }
}'