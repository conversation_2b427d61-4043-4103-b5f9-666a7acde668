# File Upload Module Documentation

## Module Identity
- **Name**: File Upload System
- **Type**: Utility Module
- **Primary Files**:
  - `middleware/file-upload.js`
  - `routes/CardImagesRoutes.js`
  - `controllers/onboardingPersonalController.js`
  - `config/id-document-service.js`

## Purpose and Functionality

### Business Purpose
Handles secure file uploads for the Ryvyl platform, primarily supporting:
- Customer identity document uploads (KYC process)
- Card design image uploads
- General document management for compliance

### Core Functions
- File validation and filtering
- Secure file storage
- Image processing and optimization
- Document metadata management
- File access control

### Endpoints Exposed
- `POST /api/clients/:clientCode/id-documents` - ID document upload (⚠️ No auth)
- `POST /api/images/upload` - Card image upload
- `GET /uploads/*` - Static file serving

## Technical Architecture

### Upload Flow
```mermaid
graph TD
    A[File Upload Request] --> B[Multer Middleware]
    B --> C[File Type Validation]
    C --> D[Size Validation]
    D --> E[Storage Processing]
    E --> F[Database Record Creation]
    F --> G[Response with File Info]
    
    C -->|Invalid Type| H[Reject Upload]
    D -->|Too Large| H
```

### Storage Strategy
- **Primary**: Local filesystem storage
- **Location**: `./uploads/` directory
- **Structure**: Flat file structure with timestamp naming
- **Backup**: ❌ No backup strategy implemented

### File Processing
- **Memory Storage**: Temporary in-memory processing
- **Disk Storage**: Permanent local storage
- **Processing**: Basic file type validation only

## Dependencies & Integrations

### NPM Libraries
- **multer**: File upload handling
- **path**: File path manipulation
- **fs**: File system operations

### File Type Support
- **Images**: JPEG, PNG, JPG
- **Documents**: PDF
- **Size Limit**: 5MB per file
- **Validation**: MIME type checking only

### Internal Dependencies
- Express.js framework
- MongoDB for metadata storage
- Authentication middleware (inconsistently applied)

## Development Info

### Build Process
- No specific build requirements
- Static file serving via Express
- Directory creation on startup

### Testing
- **Status**: ❌ No tests implemented
- **Critical Gap**: File upload security untested
- **Risk**: High security risk for file handling

### Configuration
```javascript
// File upload configuration
const UPLOAD_CONFIG = {
    MAX_FILE_SIZE: 2 * 1024 * 1024, // 5MB (inconsistent with comment)
    ALLOWED_MIME_TYPES: ["image/jpeg", "image/png", "image/jpg", "application/pdf"],
    UPLOAD_DIR: path.join(__dirname, "../uploads/id-documents")
};
```

## Deployment & Operations

### Environment Requirements
- **Disk Space**: Adequate storage for uploaded files
- **Permissions**: Write access to uploads directory
- **Backup**: Manual backup process required

### Scaling Considerations
- **Local Storage**: Not suitable for horizontal scaling
- **Performance**: File I/O operations block request processing
- **Capacity**: No automatic cleanup or archival

### Directory Structure
```
uploads/
├── id-documents/
├── front_<timestamp>.png
├── back_<timestamp>.png
└── <filename>_<timestamp>.<ext>
```

## Monitoring & Health

### Health Checks
- **Status**: ❌ No health checks for file system
- **Missing**: Disk space monitoring
- **Missing**: Upload success/failure rates

### Logging
- **Upload Events**: Basic logging in controllers
- **Error Logging**: Console error logging
- **Audit Trail**: Limited file operation tracking

### Metrics
- **Status**: ❌ No file upload metrics
- **Missing Metrics**:
  - Upload volume and size
  - File type distribution
  - Storage utilization
  - Processing times

## Database Usage

### File Metadata Storage
```javascript
// IndividualOnboarding model includes file references
{
  idDocument: {
    frontImagePath: String,
    backImagePath: String,
    documentType: String,
    uploadDate: Date
  }
}
```

### Storage Pattern
- **File Path Storage**: Relative paths in database
- **Metadata**: Basic file information
- **Relationships**: Linked to user/onboarding records

## Security Considerations

### 🚨 Critical Vulnerabilities

#### 1. Missing Authentication on Critical Endpoint
```javascript
// index.js:252-259 - No authentication middleware
app.post("/api/clients/:clientCode/id-documents", 
    upload.fields([...]), 
    uploadIdDocumentImages
);
```
**Risk**: Unauthorized file uploads, potential system compromise
**Fix**: Add authentication middleware immediately

#### 2. Insufficient File Validation
```javascript
// Only MIME type checking
if (file.mimetype === "image/jpeg" || 
    file.mimetype === "image/png" || 
    file.mimetype === "image/jpg" || 
    file.mimetype === "application/pdf") {
    cb(null, true);
}
```
**Risk**: MIME type spoofing, malicious file uploads
**Fix**: Implement file content validation

#### 3. Path Traversal Vulnerability
```javascript
// Unsafe filename handling
const newFilename = `${safeName}_${timestamp}.${extension}`;
```
**Risk**: Directory traversal attacks
**Fix**: Sanitize filenames properly

#### 4. No Virus Scanning
- **Issue**: No malware detection
- **Risk**: Malicious file storage and distribution
- **Fix**: Implement virus scanning service

#### 5. Unrestricted File Access
```javascript
// Static file serving without access control
app.use("/uploads", express.static(path.join(__dirname, "uploads")));
```
**Risk**: Unauthorized access to sensitive documents
**Fix**: Implement access control for file downloads

### 🔒 Security Strengths
- **File Size Limits**: 5MB limit prevents large file attacks
- **MIME Type Filtering**: Basic file type restrictions
- **Temporary Storage**: Memory storage for processing

### Recommended Security Fixes

#### Immediate (1 week)
1. **Add authentication to all upload endpoints**
2. **Implement proper filename sanitization**
3. **Add file content validation**
4. **Restrict static file access**

#### Short-term (1 month)
1. **Implement virus scanning**
2. **Add file encryption at rest**
3. **Implement access logging**
4. **Add file integrity checks**

#### Long-term (3 months)
1. **Migrate to cloud storage (S3, etc.)**
2. **Implement file versioning**
3. **Add automated file cleanup**
4. **Implement CDN for file delivery**

## Operational Procedures

### File Upload Process
1. Client submits file via multipart form
2. Multer processes upload
3. File validation occurs
4. File stored to local filesystem
5. Metadata saved to database
6. Response returned with file info

### Common Issues & Troubleshooting

#### Issue: Upload Fails with 413 Error
- **Cause**: File size exceeds limit
- **Resolution**: Check file size and increase limit if appropriate

#### Issue: Unsupported File Type
- **Cause**: MIME type not in allowed list
- **Resolution**: Verify file type and update allowed types if needed

#### Issue: Disk Space Full
- **Cause**: No automatic cleanup
- **Resolution**: Manual file cleanup required

#### Issue: File Not Found
- **Cause**: File path issues or file deletion
- **Resolution**: Check file system and database consistency

### Maintenance Tasks
- **Disk Space Monitoring**: Manual monitoring required
- **File Cleanup**: No automated process
- **Backup Verification**: Manual backup validation
- **Access Log Review**: No automated log analysis

## API & Integration Points

### Upload API Format
```http
POST /api/clients/:clientCode/id-documents
Content-Type: multipart/form-data

{
  "idFront": <file>,
  "idBack": <file>
}
```

### Response Format
```json
{
  "success": true,
  "message": "Files uploaded successfully",
  "files": {
    "frontImagePath": "/uploads/front_1234567890.jpg",
    "backImagePath": "/uploads/back_1234567890.jpg"
  }
}
```

### Internal Consumers
- Onboarding system
- KYC verification process
- Card design system
- Compliance reporting

## Code Quality Notes

### Comments & Documentation
- **Coverage**: ~20% of functions documented
- **Quality**: Basic comments, missing security considerations
- **Configuration**: Inconsistent configuration values

### Duplicate Logic
- File validation logic repeated
- Error handling patterns inconsistent
- Storage path generation duplicated

### Complexity Hotspots
- **File processing logic**: Needs refactoring
- **Error handling**: Inconsistent patterns
- **Configuration management**: Scattered settings

### Magic Numbers/Strings
```javascript
// Hardcoded values throughout
fileSize: 5 * 1024 * 1024  // Should be configurable
ALLOWED_MIME_TYPES: ["image/jpeg", ...]  // Should be centralized
```

## Performance Considerations

### Current Limitations
- **Synchronous Processing**: Blocks request thread
- **Memory Usage**: Large files consume significant memory
- **No Caching**: No file caching mechanism
- **Single Threaded**: No parallel processing

### Optimization Opportunities
- **Streaming**: Implement streaming uploads
- **Background Processing**: Move processing to background jobs
- **Compression**: Implement file compression
- **CDN**: Use CDN for file delivery

## Ownership & Contact

### Responsible Team
- **Primary**: Backend Development Team
- **Security**: Security Team (file security)
- **Infrastructure**: DevOps Team (storage management)

### Documentation Links
- **File Handling Policies**: ❌ Not documented
- **Security Guidelines**: ❌ Not available
- **Backup Procedures**: ❌ Not documented

## Recommendations Summary

1. **Critical Security**: Add authentication and proper file validation
2. **Storage Migration**: Move to cloud storage for scalability
3. **Security Enhancement**: Implement virus scanning and encryption
4. **Monitoring**: Add comprehensive file operation monitoring
5. **Performance**: Implement streaming and background processing
6. **Compliance**: Add audit trails and access controls

### Migration Priority
**HIGH PRIORITY** - File upload security vulnerabilities pose immediate risk to system security and compliance requirements.
