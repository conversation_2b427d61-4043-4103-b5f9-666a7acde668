# Database Module Documentation

## Module Identity
- **Name**: Database Layer
- **Type**: Data Access Layer
- **Primary Files**:
  - `models/` directory (40+ model files)
  - `index.js` (connection setup)
  - `server.js` (connection setup)

## Purpose and Functionality

### Business Purpose
Provides data persistence and management for the Ryvyl financial platform, handling all business entities including users, cards, transactions, and compliance data.

### Core Functions
- User and authentication data management
- Card program and issuance data
- Transaction and activity logging
- Compliance and KYC data storage
- Configuration and reference data

### Database Technology
- **Primary**: MongoDB with Mongoose ODM
- **Connection**: Single instance connection
- **Schema**: Document-based with validation
- **Indexing**: Basic indexing strategy

## Technical Architecture

### Connection Management
```javascript
// Connection setup in index.js and server.js
mongoose.connect(process.env.DATABASE_URL, {})
    .then(() => console.log('MongoDB connected'))
    .catch(err => console.error(err));
```

### Model Organization
```
models/
├── User Management
│   ├── user.js
│   ├── Role.js
│   └── permissions.js
├── Card Operations
│   ├── CardBin.js
│   ├── CardProgram.js
│   ├── CardScheme.js
│   └── CardType.js
├── Onboarding
│   ├── IndividualOnboarding.js
│   ├── B2BAccount.js
│   └── company.js
├── Transactions
│   ├── Transaction.js
│   ├── Activity.js
│   └── Log.js
└── Configuration
    ├── Countries.js
    ├── Region.js
    └── Zone.js
```

### Schema Patterns
- **Timestamps**: Automatic createdAt/updatedAt
- **Soft Deletes**: deleted_at field pattern
- **Status Fields**: Enumerated status values
- **References**: ObjectId relationships

## Dependencies & Integrations

### NPM Libraries
- **mongoose**: MongoDB ODM (v8.14.0)
- **mongodb**: Native MongoDB driver (transitive)

### Connection Configuration
- **Database URL**: Environment variable
- **Connection Options**: Default Mongoose settings
- **Pool Size**: Default connection pooling
- **Timeout**: Default timeout settings

### External Dependencies
- **MongoDB Server**: Required database instance
- **Network**: Database connectivity required

## Development Info

### Schema Validation
```javascript
// Example validation pattern
const userSchema = new mongoose.Schema({
    email: {
        required: true,
        unique: true,
        type: String
    },
    password: {
        required: true,
        type: String
    }
});
```

### Testing
- **Status**: ❌ No database tests
- **Missing**: Model validation tests
- **Missing**: Integration tests
- **Risk**: Schema changes untested

### Migration Strategy
- **Status**: ❌ No migration system
- **Risk**: Schema changes require manual intervention
- **Recommendation**: Implement migration framework

## Deployment & Operations

### Environment Variables
```bash
DATABASE_URL=mongodb://localhost:27017/ryvyl
# or
DATABASE_URL=mongodb+srv://user:<EMAIL>/ryvyl
```

### Database Setup
1. MongoDB instance running
2. Database created (auto-created on first connection)
3. Collections created automatically by Mongoose
4. Indexes created on application startup

### Backup Strategy
- **Status**: ❌ No automated backup
- **Risk**: Data loss potential
- **Recommendation**: Implement automated backup

## Monitoring & Health

### Connection Monitoring
- **Status**: Basic connection error logging
- **Missing**: Connection pool monitoring
- **Missing**: Query performance monitoring
- **Missing**: Database health checks

### Performance Metrics
- **Status**: ❌ No database metrics collected
- **Missing Metrics**:
  - Query execution times
  - Connection pool utilization
  - Document growth rates
  - Index usage statistics

### Logging
- **Connection Events**: Basic connection logging
- **Query Logging**: Not enabled
- **Error Logging**: Console error output

## Database Usage

### Key Collections

#### Users Collection
```javascript
{
  _id: ObjectId,
  name: String,
  email: String (unique),
  password: String (hashed),
  roles: [ObjectId],
  dashboard: String,
  twoFactorSecret: String,
  twoFactorEnabled: Boolean,
  status: String,
  createdAt: Date,
  lastLoginAt: Date,
  lastLoginIP: String
}
```

#### IndividualOnboarding Collection
```javascript
{
  _id: ObjectId,
  clientCode: String,
  personalInfo: {
    firstName: String,
    lastName: String,
    email: String,
    phoneNumber: String
  },
  address: Object,
  idDocument: {
    type: String,
    number: String,
    frontImagePath: String,
    backImagePath: String
  },
  status: String,
  createdAt: Date,
  updatedAt: Date
}
```

#### Transaction Collection
```javascript
{
  _id: ObjectId,
  cardId: ObjectId,
  amount: Number,
  currency: String,
  type: String,
  status: String,
  timestamp: Date,
  merchantInfo: Object
}
```

### Indexing Strategy

#### Current Indexes
- **Email indexes**: Unique indexes on email fields
- **Status indexes**: Basic indexes on status fields
- **Timestamp indexes**: Indexes on date fields

#### Missing Indexes
- **Compound indexes**: Multi-field query optimization
- **Text indexes**: Search functionality
- **Geospatial indexes**: Location-based queries

### Query Patterns

#### Common Queries
```javascript
// User lookup by email
User.findOne({ email: userEmail });

// Onboarding by client code
IndividualOnboarding.findOne({ clientCode: code });

// Transactions by card
Transaction.find({ cardId: cardId }).sort({ timestamp: -1 });

// Active records
Model.find({ status: 'active', deleted_at: null });
```

## Security Considerations

### 🚨 Critical Vulnerabilities

#### 1. No Input Sanitization
```javascript
// Direct parameter usage in queries
User.findOne({ email: req.body.email }); // Potential NoSQL injection
```
**Risk**: NoSQL injection attacks
**Fix**: Implement input sanitization and validation

#### 2. Sensitive Data in Logs
```javascript
// Log model stores request/response bodies
requestBody: req.body,  // May contain passwords, tokens
responseBody: res.responseBody
```
**Risk**: Sensitive data exposure in logs
**Fix**: Sanitize logged data

#### 3. Weak Connection Security
```javascript
// No connection encryption specified
mongoose.connect(process.env.DATABASE_URL, {})
```
**Risk**: Unencrypted database connections
**Fix**: Enforce SSL/TLS connections

#### 4. No Access Control
- **Issue**: No database-level access controls
- **Risk**: Application compromise leads to full database access
- **Fix**: Implement database user roles and permissions

### 🔒 Security Strengths
- **Password Hashing**: Proper bcrypt implementation
- **Environment Variables**: Database credentials in env vars
- **Schema Validation**: Basic input validation

### Recommended Security Fixes

#### Immediate (1 week)
1. **Implement input sanitization**
2. **Enable SSL/TLS connections**
3. **Sanitize logged data**
4. **Add query parameter validation**

#### Short-term (1 month)
1. **Implement database access controls**
2. **Add query monitoring**
3. **Implement data encryption at rest**
4. **Add audit logging**

#### Long-term (3 months)
1. **Implement field-level encryption**
2. **Add data masking for non-production**
3. **Implement database activity monitoring**
4. **Add compliance reporting**

## Operational Procedures

### Database Maintenance
- **Backups**: Manual backup required
- **Index Maintenance**: No automated optimization
- **Cleanup**: No automated data cleanup
- **Monitoring**: Manual monitoring required

### Common Issues & Troubleshooting

#### Issue: Connection Timeouts
- **Symptoms**: Database connection errors
- **Causes**: Network issues, database overload
- **Resolution**: Check network connectivity and database status

#### Issue: Slow Queries
- **Symptoms**: Application performance degradation
- **Causes**: Missing indexes, large collections
- **Resolution**: Analyze query patterns and add indexes

#### Issue: Disk Space
- **Symptoms**: Write operation failures
- **Causes**: Database storage full
- **Resolution**: Clean up old data or expand storage

### Performance Optimization

#### Current Limitations
- **Single Connection**: No connection pooling optimization
- **No Caching**: No query result caching
- **Large Collections**: No partitioning strategy
- **Log Storage**: Logs in main database affect performance

#### Optimization Recommendations
1. **Connection Pool Tuning**: Optimize pool size
2. **Index Optimization**: Add compound indexes
3. **Query Optimization**: Analyze and optimize slow queries
4. **Data Archival**: Implement data lifecycle management

## API & Integration Points

### Internal Consumers
- All controllers and services
- Authentication middleware
- Logging middleware
- Webhook handlers

### Data Access Patterns
- **CRUD Operations**: Standard create, read, update, delete
- **Aggregation**: Limited aggregation pipeline usage
- **Transactions**: No multi-document transactions
- **Bulk Operations**: Limited bulk operation usage

## Code Quality Notes

### Schema Design Issues
- **Inconsistent Naming**: Mixed camelCase and snake_case
- **Missing Validation**: Some fields lack proper validation
- **No Relationships**: Limited use of Mongoose population
- **Magic Strings**: Status values not centralized

### Duplicate Logic
- **Validation Rules**: Repeated validation patterns
- **Schema Patterns**: Similar schema structures
- **Connection Logic**: Duplicated connection setup

### Complexity Hotspots
- **Large Models**: Some models with many fields
- **Complex Validation**: Business rule validation in models
- **Mixed Patterns**: Inconsistent model patterns

## Performance Considerations

### Current Performance Issues
- **N+1 Queries**: Potential N+1 query problems
- **Large Documents**: Some documents may grow large
- **No Pagination**: Limited pagination implementation
- **Log Storage**: Performance impact from log storage

### Scalability Concerns
- **Single Instance**: No clustering or sharding
- **Connection Limits**: Default connection limits
- **Memory Usage**: Large document memory usage
- **Index Maintenance**: Index maintenance overhead

## Ownership & Contact

### Responsible Team
- **Primary**: Backend Development Team
- **DBA**: Database Administrator (if available)
- **DevOps**: Infrastructure Team (database infrastructure)

### Documentation Links
- **Schema Documentation**: ❌ Not available
- **Data Dictionary**: ❌ Not documented
- **Backup Procedures**: ❌ Not documented

## Recommendations Summary

1. **Security**: Implement input sanitization and connection encryption
2. **Performance**: Add proper indexing and connection optimization
3. **Monitoring**: Implement database monitoring and alerting
4. **Backup**: Implement automated backup and recovery
5. **Testing**: Add comprehensive database testing
6. **Documentation**: Create schema documentation and data dictionary

### Migration Priority
**HIGH PRIORITY** - Database security and performance issues affect entire application stability and security.
