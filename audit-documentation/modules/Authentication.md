# Authentication Module Documentation

## Module Identity
- **Name**: Authentication System
- **Type**: Core Security Module
- **Primary Files**: 
  - `routes/authRoutes.js`
  - `controllers/authController.js`
  - `middleware/authMiddleware.js`
  - `models/user.js`

## Purpose and Functionality

### Business Purpose
Provides secure user authentication and authorization for the Ryvyl financial platform, supporting multiple authentication methods including traditional login and two-factor authentication (2FA).

### Core Functions
- User registration and login
- JWT token generation and validation
- Two-factor authentication (TOTP)
- Password reset functionality
- Session management
- Role-based access control

### Endpoints Exposed
- `POST /api/auth/login` - User authentication
- `POST /api/auth/register` - User registration
- `POST /api/auth/verify-2fa` - Two-factor authentication verification
- `POST /api/auth/logout` - User logout
- `POST /api/auth/refresh` - Token refresh

## Technical Architecture

### Authentication Flow
```mermaid
graph TD
    A[Login Request] --> B[Credential Validation]
    B --> C{2FA Enabled?}
    C -->|Yes| D[Generate Temp Token]
    C -->|No| E[Generate Full Token]
    D --> F[2FA Verification]
    F --> G[Generate Full Token]
    E --> H[Set HTTP-Only Cookie]
    G --> H
    H --> I[Return Success Response]
```

### Controllers
- **authController.js**: Legacy session-based authentication (deprecated)
- **authRoutes.js**: Modern JWT-based authentication handlers

### Database Interaction
- **User Model**: MongoDB collection for user data
- **Session Storage**: Mixed approach (in-memory + JWT)
- **2FA Secrets**: Stored encrypted in user documents

### Async Operations
- Password hashing (bcrypt)
- Database queries (Mongoose)
- Email notifications (Postmark)

## Dependencies & Integrations

### NPM Libraries
- **jsonwebtoken**: JWT token generation and verification
- **bcrypt**: Password hashing
- **speakeasy**: TOTP 2FA implementation
- **qrcode**: QR code generation for 2FA setup
- **express-session**: Session management (legacy)

### External APIs
- **Postmark**: Email service for notifications
- **MongoDB**: User data persistence

### Internal Dependencies
- User model and schema
- Role and permission models
- Logging middleware

## Development Info

### Build Process
- No specific build requirements
- Standard Node.js module loading
- Environment variable configuration required

### Testing
- **Status**: ❌ No tests implemented
- **Critical Gap**: Authentication is completely untested
- **Risk**: High security risk due to lack of test coverage

### Linting
- **Status**: ❌ No ESLint configuration
- **Code Quality**: Inconsistent formatting and patterns

## Deployment & Operations

### Environment Variables Required
```bash
JWT_SECRET=<strong-secret-key>
DATABASE_URL=<mongodb-connection-string>
EMAIL_USER=<email-service-user>
EMAIL_PASS=<email-service-password>
NODE_ENV=<production|development>
```

### Scaling Considerations
- **Stateless Design**: JWT tokens enable horizontal scaling
- **Session Storage**: In-memory sessions prevent clustering
- **Database Load**: User lookups on every authenticated request

### Configuration Management
- Environment-based configuration
- No configuration validation
- Hardcoded fallback values

## Monitoring & Health

### Health Checks
- **Status**: ❌ No dedicated health checks
- **Recommendation**: Implement authentication service health endpoint

### Logging
- **Request Logging**: Comprehensive HTTP request/response logging
- **Security Events**: Limited security event logging
- **Error Tracking**: Basic console error logging

### Metrics
- **Status**: ❌ No authentication metrics collected
- **Missing Metrics**:
  - Login success/failure rates
  - 2FA adoption rates
  - Token refresh patterns
  - Failed authentication attempts

## Database Usage

### User Schema
```javascript
{
  name: String,
  email: String (unique),
  password: String (hashed),
  roles: [ObjectId],
  dashboard: String,
  twoFactorSecret: String,
  twoFactorEnabled: Boolean,
  status: String,
  lastLoginAt: Date,
  lastLoginIP: String
}
```

### Queries
- **User lookup by email**: `User.findOne({ email })`
- **User creation**: `new User().save()`
- **Password verification**: `bcrypt.compare()`

### Indexes
- **Email index**: Unique index on email field
- **Performance**: Basic indexing, room for optimization

## Security Considerations

### 🚨 Critical Vulnerabilities

#### 1. Hardcoded API Keys
```javascript
// userController.js:21
const client = new postmark.ServerClient("************************************");
```
**Fix**: Move to environment variables

#### 2. Missing Rate Limiting
- **Issue**: No rate limiting on authentication endpoints
- **Risk**: Brute force attacks
- **Fix**: Implement express-rate-limit

#### 3. Weak Session Management
- **Issue**: Mixed JWT and session authentication
- **Risk**: Session fixation, inconsistent security
- **Fix**: Standardize on JWT-only authentication

#### 4. Information Disclosure
```javascript
// Exposes user existence
if (!user) {
    throw new Error('User not found');
}
```
**Fix**: Use generic "Invalid credentials" message

### 🔒 Security Strengths
- **Password Hashing**: Proper bcrypt implementation
- **JWT Implementation**: Secure token generation
- **2FA Support**: TOTP-based two-factor authentication
- **HTTP-Only Cookies**: Prevents XSS token theft

### Recommended Fixes

#### Immediate (1 week)
1. **Remove hardcoded API keys**
2. **Implement rate limiting**
3. **Add input validation**
4. **Standardize error messages**

#### Short-term (1 month)
1. **Add comprehensive testing**
2. **Implement security event logging**
3. **Add password complexity requirements**
4. **Implement account lockout mechanism**

#### Long-term (3 months)
1. **Implement refresh token rotation**
2. **Add device tracking**
3. **Implement risk-based authentication**
4. **Add OAuth2/OIDC support**

## Operational Procedures

### Starting the Service
1. Ensure MongoDB is running
2. Set required environment variables
3. Start Node.js application
4. Verify authentication endpoints respond

### Common Issues & Troubleshooting

#### Issue: JWT Token Verification Fails
- **Symptoms**: 401 Unauthorized responses
- **Causes**: 
  - Incorrect JWT_SECRET
  - Expired tokens
  - Malformed Authorization header
- **Resolution**: Check environment variables and token format

#### Issue: 2FA Setup Fails
- **Symptoms**: QR code generation errors
- **Causes**: Missing speakeasy configuration
- **Resolution**: Verify 2FA secret generation

#### Issue: Database Connection Errors
- **Symptoms**: User lookup failures
- **Causes**: MongoDB connection issues
- **Resolution**: Check DATABASE_URL and MongoDB status

### Maintenance Tasks
- **Token Secret Rotation**: Manual process (needs automation)
- **User Cleanup**: No automated inactive user cleanup
- **Security Audits**: No scheduled security reviews

## API & Integration Points

### Internal Consumers
- All protected API endpoints
- User management system
- Role-based access control

### External Integrations
- Frontend applications (web, mobile)
- Third-party API consumers
- Webhook authentication

### Authentication Headers
```http
Authorization: Bearer <jwt-token>
Content-Type: application/json
```

## Code Quality Notes

### Comments & Documentation
- **Coverage**: ~15% of functions documented
- **Quality**: Basic comments, missing business logic documentation
- **TODOs**: Email sending commented out (production issue)

### Duplicate Logic
- JWT verification logic duplicated
- Error response formatting inconsistent
- Password validation scattered

### Complexity Hotspots
- **authRoutes.js**: Mixed authentication flows
- **User model**: Complex validation logic
- **2FA implementation**: Needs refactoring

## Ownership & Contact

### Responsible Team
- **Primary**: Backend Development Team
- **Security**: Security Team (consultation)
- **DevOps**: Infrastructure Team (deployment)

### Documentation Links
- **API Docs**: ❌ Not available
- **Security Policies**: ❌ Not documented
- **Incident Response**: ❌ Not documented

### Emergency Contacts
- **On-call**: Backend team rotation
- **Security Issues**: Security team escalation
- **Infrastructure**: DevOps team support

## Recommendations Summary

1. **Immediate Security Fixes**: Remove hardcoded secrets, add rate limiting
2. **Testing Implementation**: Critical for security validation
3. **Monitoring Enhancement**: Add security metrics and alerting
4. **Documentation**: Complete API documentation and security procedures
5. **Code Quality**: Refactor duplicate logic and improve error handling
