const Joi = require("joi")

const addressSchema = Joi.object({
    street: Joi.string().required().trim().max(200),
    building_number: Joi.string().allow("").trim().max(20),
    apartment_number: Joi.string().allow("").trim().max(20),
    city: Joi.string().required().trim().max(100),
    state: Joi.string().allow("").trim().max(100),
    postal_code: Joi.string().required().trim().max(20),
    country: Joi.string().required().trim().max(100),
})

const b2bRegistrationSchema = Joi.object({
    // Basic company information (existing + mapped)
    companyName: Joi.string().required().trim().max(200),
    company_name: Joi.string().trim().max(200), // Alternative field name
    clientCode: Joi.string().allow("").trim().max(50),
    phoneNumber: Joi.string().required().trim().max(20),
    company_phone: Joi.string().trim().max(20), // Alternative field name
    authPhoneNumber: Joi.string().allow("").trim().max(20),
    email: Joi.string().required().email().lowercase().trim(),
    company_email: Joi.string().email().lowercase().trim(), // Alternative field name
    nip: Joi.string().allow("").trim().max(20),
    regon: Joi.string().allow("").trim().max(20),
    embossedName: Joi.string().allow("").trim().max(100),

    // Extended company information
    company_industry: Joi.string().allow("").trim().max(100),
    company_number: Joi.string().allow("").trim().max(50),
    registration_date: Joi.date().iso().max("now"),
    contact_name: Joi.string().allow("").trim().max(100),
    contact_role: Joi.string().allow("").trim().max(100),
    country_of_incorporation: Joi.string().allow("").trim().max(100),
    company_website: Joi.string().allow("").uri().trim(),

    // Card Programme Purpose
    type_of_business: Joi.string().allow("").trim().max(100),
    card_usage: Joi.string().allow("").trim().max(500),
    cardholder_groups: Joi.string().allow("").trim().max(200),
    fund_loading: Joi.number().min(0).allow(""),

    // Merchant Business Case Details
    business_sector: Joi.string().allow("").trim().max(100),
    regions: Joi.string().allow("").trim().max(200),
    countries: Joi.string().allow("").trim().max(200),
    business_purpose: Joi.string().allow("").trim().max(1000),
    card_user_groups: Joi.string().allow("").trim().max(200),
    number_of_cards: Joi.number().integer().min(0).allow(""),
    monthly_loading_value: Joi.number().min(0).allow(""),

    // Administrator Information
    admin_name: Joi.string().allow("").trim().max(100),
    admin_role: Joi.string().allow("").trim().max(100),
    admin_email: Joi.string().allow("").email().lowercase().trim(),
    admin_phone: Joi.string().allow("").trim().max(20),

    // Address fields
    address: addressSchema, // Legacy support
    registered_address: addressSchema,
    operational_address: addressSchema,
})

const validateB2BRegistration = (data) => {
    return b2bRegistrationSchema.validate(data, {
        abortEarly: false,
        allowUnknown: true, // Allow additional fields
    })
}

module.exports = {
    validateB2BRegistration,
    b2bRegistrationSchema,
}
