const fs = require('fs');
const path = require('path');
const dotenv = require('dotenv');
const twilio = require('twilio');
const Card = require("../models/AccountCard");
const {getCard} = require("../utils/cardUtils");
const {sendGetRequest} = require("../config/ApiInstense");
const bcrypt = require("bcryptjs");
const IndividualOnboarding = require('../models/IndividualOnboarding');


// Load environment variables
dotenv.config();

// Initialize Twilio client
const client = twilio(process.env.TWILIO_ACCOUNT_SID, process.env.TWILIO_AUTH_TOKEN);

// Simple in-memory store for OTPs
const otpStore = {};

// Utility functions
function generateOTP() {
    return Math.floor(100000 + Math.random() * 900000).toString(); // 6-digit OTP
}

function isOTPExpired(entry) {
    return !entry || Date.now() > entry.expiresAt;
}

function maskCardNumber(cardNumber) {
    const firstSix = cardNumber.slice(0, 6);
    const lastFour = cardNumber.slice(-4);
    return firstSix + '******' + lastFour;
}

// Add this utility function at the top of the file
function maskPhoneNumber(phoneNumber) {
    if (!phoneNumber) return '';
    const visibleDigits = 4;
    return phoneNumber.slice(0, -visibleDigits).replace(/\d/g, '*') + phoneNumber.slice(-visibleDigits);
}


async function getCardKey(cardNumber) {
    if (!cardNumber) {
        console.log("No card number provided.");
        return null;
    }

    const masked = maskCardNumber(cardNumber);
    console.log("Original:", cardNumber, "Masked:", masked);

    const accountCard = await Card.findOne({ cardMask: masked });

    if (accountCard) {
        console.log("Card Key:", accountCard.cardKey);
        return accountCard.cardKey;
    } else {
        console.log("Card not found");
        return null;
    }
}

// Service implementation functions
const serviceImplementation = {
    verifyReg: async (cardNumber, cardType) => {
        try {
            console.log("verifyReg called with card:", cardNumber, cardType);

            const cardKey = await getCardKey(cardNumber);

            let cardDetails = null;
            if (cardKey) {
                cardDetails = await getCard(cardKey);
            }

            if (!cardDetails) {
                return {
                    code: 0,
                    errorMessage: 'Card not found',
                    errorDetail: 'No details found for given card'
                };
            }

            return {
                cardInfo: {
                    card_ID: cardNumber,
                    context_Blob: 'xyz123',
                    regStatus: cardDetails.status === "active" ? 1 : 0,
                    authRequired: 1,
                    authTypeSup: [1, 2],
                    lanCode: '',
                    twoFA: true
                },
                code: 1,
                errorMessage: 'Card found',
                errorDetail: 'Card info fetched successfully'
            };
        } catch (err) {
            console.error("Error in verifyReg:", err);
            return {
                code: -1,
                errorMessage: 'Server Error',
                errorDetail: err.message
            };
        }
    },

    preAuth: (cardId, cardNumber) => {
        console.log("preAuth called with card:", cardId, cardNumber);
        return { code: 1 };
    },

    initAuth: async (cardId, cardNumber, smsTemplate, authType) => {
        console.log("initAuth called with card:", cardId, cardNumber, "authType:", authType);


        const cardKey = await getCardKey(cardNumber);

        let cardDetails = null;
        if (cardKey) {
            cardDetails = await getCard(cardKey);
        }

        if (!cardDetails) {
            return {
                code: 0,
                errorMessage: 'Card not found',
                errorDetail: 'No details found for given card'
            };
        }


        const selectedCard = await Card.findOne({cardKey: cardKey}).populate("onboarding")
        let phoneNumber = selectedCard?.onboarding?.personalInfo?.authPhoneNumber;
        // In a real implementation, you would get the phone number from the card details
        // phoneNumber = "+91 98848 60077"; // Hardcoded for demo
        const otp = generateOTP();
        const expiresAt = Date.now() + 2 * 60 * 1000; // 2 minutes

        try {
            if (process.env.TWILIO_ACCOUNT_SID && process.env.TWILIO_AUTH_TOKEN) {
                await client.messages.create({
                    body: `Your verification code is: ${otp}`,
                    from: process.env.TWILIO_PHONE_NUMBER,
                    to: phoneNumber
                });
                console.log(`✅ OTP sent to ${phoneNumber}: ${otp}`);
            } else {
                console.log(`✅ [MOCK] OTP for ${phoneNumber}: ${otp}`);
            }

            otpStore[phoneNumber] = { otp, expiresAt };
        } catch (err) {
            // console.error('❌ Failed to send OTP:', err);
            return {
                code: -1,
                errorMessage: 'Failed to send OTP',
                errorDetail: err.message
            };
        }

        return {
            authData: {
                data: {
                    name: 'mobileNo',
                    authType: '2',
                    value: maskCardNumber(phoneNumber)
                }
            },
            code: 0
        };
    },

    verifyAuth: async (cardId, cardNumber, token, authType) => {
        console.log("verifyAuth called with card:", cardId, cardNumber, "token:", token, "authType:", authType);
        const cardKey = await getCardKey(cardNumber);

        let cardDetails = null;
        if (cardKey) {
            cardDetails = await getCard(cardKey);
        }

        if (!cardDetails) {
            return {
                code: 0,
                errorMessage: 'Card not found',
                errorDetail: 'No details found for given card'
            };
        }
        const selectedCard = await Card.findOne({cardKey: cardKey}).populate("onboarding")
        if (authType === '1') {

            console.log(selectedCard.current_password)

            if (!selectedCard || !selectedCard.current_password) {
                return {
                    code: 1,
                    errorMessage: 'Password not set',
                    errorDetail: 'Password not set in the system'
                };
            }

            // Compare the password with the hashed password
            const isMatch = await bcrypt.compare(token, selectedCard.current_password);
            // const isMatch = token.trim() === selectedCard.current_password.trim();

            if (!isMatch) {
                console.log("Password does not match the stored password", token, selectedCard.current_password);
                return {
                    code: 1,
                    errorMessage: 'Invalid password',
                    errorDetail: 'Password does not match the stored password'
                };
            } else {
                console.log("Password matched the stored password", token, selectedCard.current_password);
                return {
                    code: 0,
                    errorMessage: 'Password matched',
                    errorDetail: 'Password matched the stored password'
                };
            }
        } else if (authType === '2') {


            let phoneNumber = selectedCard?.onboarding?.personalInfo?.authPhoneNumber;
            const userOtp = token;

            const stored = otpStore[phoneNumber];

            if (!stored) {
                return {
                    code: 2,
                    errorMessage: 'No OTP found for this number',
                    errorDetail: 'OTP might have expired or was never generated'
                };
            }

            if (isOTPExpired(stored)) {
                delete otpStore[phoneNumber];
                return {
                    code: 2,
                    errorMessage: 'OTP expired',
                    errorDetail: 'The OTP is no longer valid'
                };
            }

            if (stored.otp !== userOtp) {
                return {
                    code: 1,
                    errorMessage: 'Invalid OTP',
                    errorDetail: 'The provided OTP does not match'
                };
            }

            // Success: delete OTP after successful verification
            delete otpStore[phoneNumber];

            return {
                code: 0,
                errorMessage: 'OTP Verified',
                errorDetail: 'User authenticated successfully'
            };
        }
    }
};

// Function to generate SOAP response
function generateSoapResponse(operation, responseData) {
    let responseXml = "";

    if (operation === "verifyReg") {
        responseXml = `<?xml version='1.0' encoding='UTF-8'?>
<S:Envelope xmlns:S="http://www.w3.org/2003/05/soap-envelope">
    <S:Body>
        <ns2:verifyRegResp xmlns:ns2="http://www.gpayments.com/caas/">
            ${responseData.cardInfo ? `<ns2:cardInfo>
                <card_ID>${responseData.cardInfo.card_ID}</card_ID>
                <context_Blob>${responseData.cardInfo.context_Blob}</context_Blob>
                <regStatus>${responseData.cardInfo.regStatus}</regStatus>
                <authRequired>${responseData.cardInfo.authRequired}</authRequired>
                <authTypeSup>${responseData.cardInfo.authTypeSup[0]}</authTypeSup>
                ${responseData.cardInfo.authTypeSup[1] ? `<authTypeSup>${responseData.cardInfo.authTypeSup[1]}</authTypeSup>` : ''}
                <lanCode>${responseData.cardInfo.lanCode}</lanCode>
                <twoFA>${responseData.cardInfo.twoFA}</twoFA>
            </ns2:cardInfo>` : ''}
            <code>${responseData.code}</code>
            <ns2:errorMessage>${responseData.errorMessage}</ns2:errorMessage>
            <ns2:errorDetail>${responseData.errorDetail}</ns2:errorDetail>
        </ns2:verifyRegResp>
    </S:Body>
</S:Envelope>`;
    } else if (operation === "preAuth") {
        responseXml = `<?xml version='1.0' encoding='UTF-8'?>
<S:Envelope xmlns:S="http://www.w3.org/2003/05/soap-envelope">
    <S:Body>
        <ns2:preAuthResp xmlns:ns2="http://www.gpayments.com/caas/">
            <code>${responseData.code}</code>
            <ns2:errorMessage>${responseData.errorMessage || ''}</ns2:errorMessage>
            <ns2:errorDetail>${responseData.errorDetail || ''}</ns2:errorDetail>
        </ns2:preAuthResp>
    </S:Body>
</S:Envelope>`;
    } else if (operation === "initAuth") {
        if (responseData.authData?.data.name && responseData.authData?.data.authType && responseData.authData?.data.value) {


        responseXml = `<?xml version='1.0' encoding='UTF-8'?>
<S:Envelope xmlns:S="http://www.w3.org/2003/05/soap-envelope">
    <S:Body>
        <ns2:initAuthResp xmlns:ns2="http://www.gpayments.com/caas/">
            <authData>
                <data name="${responseData.authData?.data.name}" authType="${responseData.authData?.data.authType}">
                    <value>${responseData.authData?.data.value}</value>
                </data>
            </authData>
            <code>${responseData.code}</code>
        </ns2:initAuthResp>
    </S:Body>
</S:Envelope>`;
        } else {
            //////////////================///
            responseXml = `<?xml version='1.0' encoding='UTF-8'?>
<S:Envelope xmlns:S="http://www.w3.org/2003/05/soap-envelope">
    <S:Body>
        <ns2:initAuthResp xmlns:ns2="http://www.gpayments.com/caas/">
            <code>2</code>
            <ns2:errorMessage>${responseData.errorMessage || ''}</ns2:errorMessage>
            <ns2:errorDetail>${responseData.errorDetail || ''}</ns2:errorDetail>
        </ns2:initAuthResp>
    </S:Body>
</S:Envelope>`;
        }
    } else if (operation === "verifyAuth") {
        responseXml = `<?xml version='1.0' encoding='UTF-8'?>
<S:Envelope xmlns:S="http://www.w3.org/2003/05/soap-envelope">
    <S:Body>
        <ns2:verifyAuthResp xmlns:ns2="http://www.gpayments.com/caas/">
            <code>${responseData.code}</code>
            <ns2:errorMessage>${responseData.errorMessage || ''}</ns2:errorMessage>
            <ns2:errorDetail>${responseData.errorDetail || ''}</ns2:errorDetail>
        </ns2:verifyAuthResp>
    </S:Body>
</S:Envelope>`;
    }

    return responseXml;
}

// Simple SOAP handler
const simpleSoapHandler = async (req, res) => {
    try {
        // Get the raw request body
        const body = req.body;
        console.log("Received SOAP request:", body.substring(0, 200) + "...");

        // Determine the operation based on the request content
        let operation = "";
        if (body.includes("verifyRegReq")) {
            operation = "verifyReg";
        } else if (body.includes("preAuthReq")) {
            operation = "preAuth";
        } else if (body.includes("initAuthReq")) {
            operation = "initAuth";
        } else if (body.includes("verifyAuthReq")) {
            operation = "verifyAuth";
        } else {
            throw new Error("Unknown operation in SOAP request");
        }

        console.log("Detected operation:", operation);

        // Extract basic parameters using regex (simplified approach)
        let params = {};
        if (operation === "verifyReg") {
            const cardNumberMatch = body.match(/<number>(.*?)<\/number>/);
            const cardTypeMatch = body.match(/<type>(.*?)<\/type>/);
            params = {
                cardNumber: cardNumberMatch ? cardNumberMatch[1] : "",
                cardType: cardTypeMatch ? cardTypeMatch[1] : ""
            };
        } else if (operation === "preAuth") {
            const cardIdMatch = body.match(/<id>(.*?)<\/id>/);
            const cardNumberMatch = body.match(/<number>(.*?)<\/number>/);
            params = {
                cardId: cardIdMatch ? cardIdMatch[1] : "",
                cardNumber: cardNumberMatch ? cardNumberMatch[1] : ""
            };
        } else if (operation === "initAuth") {
            const cardIdMatch = body.match(/<id>(.*?)<\/id>/);
            const cardNumberMatch = body.match(/<number>(.*?)<\/number>/);
            const smsTemplateMatch = body.match(/<SMS>(.*?)<\/SMS>/);
            const authTypeMatch = body.match(/<authType>(.*?)<\/authType>/);
            params = {
                cardId: cardIdMatch ? cardIdMatch[1] : "",
                cardNumber: cardNumberMatch ? cardNumberMatch[1] : "",
                smsTemplate: smsTemplateMatch ? smsTemplateMatch[1] : "",
                authType: authTypeMatch ? authTypeMatch[1] : ""
            };
        } else if (operation === "verifyAuth") {
            const cardIdMatch = body.match(/<id>(.*?)<\/id>/);
            const cardNumberMatch = body.match(/<number>(.*?)<\/number>/);
            const tokenMatch = body.match(/<token.*?>(.*?)<\/token>/);
            const authTypeMatch = body.match(/<token.*?authType="(.*?)"/);
            params = {
                cardId: cardIdMatch ? cardIdMatch[1] : "",
                cardNumber: cardNumberMatch ? cardNumberMatch[1] : "",
                token: tokenMatch ? tokenMatch[1] : "",
                authType: authTypeMatch ? authTypeMatch[1] : ""
            };
        }

        // Process the request based on the operation
        let responseData;

        if (operation === "verifyReg") {
            responseData = await serviceImplementation.verifyReg(params.cardNumber, params.cardType);
        } else if (operation === "preAuth") {
            responseData = serviceImplementation.preAuth(params.cardId, params.cardNumber);
        } else if (operation === "initAuth") {
            responseData = await serviceImplementation.initAuth(
                params.cardId,
                params.cardNumber,
                params.smsTemplate,
                params.authType
            );
        } else if (operation === "verifyAuth") {
            responseData = await serviceImplementation.verifyAuth(
                params.cardId,
                params.cardNumber,
                params.token,
                params.authType
            );
        } else {
            throw new Error(`Unsupported operation: ${operation}`);
        }

        // Generate SOAP response
        const soapResponse = generateSoapResponse(operation, responseData);
        console.log("Generated SOAP response:", soapResponse.substring(0, 200) + "...");

        // Return the SOAP response
        res.set('Content-Type', 'application/soap+xml; charset=utf-8');
        res.send(soapResponse);
    } catch (error) {
        console.error("SOAP server error:", error);
        res.status(500).send(`SOAP server error: ${error.message}`);
    }
};

// Get WSDL handler
const getWsdl = (req, res) => {
    const wsdlPath = path.join(__dirname, '..', '..', 'public', 'verify-service.wsdl');

    try {
        // Try to read from file system
        const wsdlContent = fs.readFileSync(wsdlPath, 'utf8');
        res.set('Content-Type', 'application/xml');
        res.send(wsdlContent);
    } catch (error) {
        console.error("Error reading WSDL file:", error);

        // Use the hardcoded WSDL as fallback
        const wsdlContent = `<?xml version="1.0" encoding="UTF-8"?>
<definitions
        xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
        xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/"
        xmlns:xsd="http://www.w3.org/2001/XMLSchema"
        xmlns:tns="http://www.gpayments.com/caas/"
        name="VerifyService"
        targetNamespace="http://www.gpayments.com/caas/"
        xmlns="http://schemas.xmlsoap.org/wsdl/"
>
    <!-- === Types === -->
    <types>
        <xsd:schema targetNamespace="http://www.gpayments.com/caas/">
            <!-- Card Type -->
            <xsd:complexType name="CardType">
                <xsd:sequence>
                    <xsd:element name="id" type="xsd:string" minOccurs="0"/>
                    <xsd:element name="number" type="xsd:string"/>
                    <xsd:element name="type" type="xsd:string" minOccurs="0"/>
                    <xsd:element name="context_Blob" type="xsd:string" minOccurs="0"/>
                    <xsd:element name="lanCode" type="xsd:string" minOccurs="0"/>
                </xsd:sequence>
            </xsd:complexType>

            <!-- Transaction Type -->
            <xsd:complexType name="TransactionType">
                <xsd:sequence>
                    <xsd:element name="purchaseAmount" type="xsd:string"/>
                    <xsd:element name="purchaseExponent" type="xsd:string"/>
                    <xsd:element name="purchaseCurrency" type="xsd:string"/>
                    <xsd:element name="purchaseDate" type="xsd:dateTime"/>
                    <xsd:element name="merchantId" type="xsd:string"/>
                    <xsd:element name="merchantName" type="xsd:string"/>
                    <xsd:element name="merchantCountry" type="xsd:string"/>
                    <xsd:element name="acqBin" type="xsd:string"/>
                    <xsd:element name="theeDSProtocolVersion" type="xsd:string"/>
                    <xsd:element name="cardExpiry" type="xsd:string"/>
                    <xsd:element name="issuerName" type="xsd:string"/>
                    <xsd:element name="acsTransId" type="xsd:string"/>
                    <xsd:element name="threeDSTransId" type="xsd:string"/>
                    <xsd:element name="dsTransId" type="xsd:string"/>
                    <xsd:element name="threeDSRequestorID" type="xsd:string"/>
                    <xsd:element name="threeDSRequestorName" type="xsd:string"/>
                    <xsd:element name="threeDSServerRefNumber" type="xsd:string"/>
                    <xsd:element name="threeDSServerOperatorID" type="xsd:string"/>
                    <xsd:element name="threeDSRequestorURL" type="xsd:string"/>
                    <xsd:element name="threeDSServerURL" type="xsd:string"/>
                    <xsd:element name="deviceChannel" type="xsd:string"/>
                    <xsd:element name="dsReferenceNumber" type="xsd:string"/>
                    <xsd:element name="payTokenInd" type="xsd:string"/>
                    <xsd:element name="mcc" type="xsd:string"/>
                    <xsd:element name="messageCategory" type="xsd:string"/>
                    <xsd:element name="transType" type="xsd:string"/>
                    <xsd:element name="acctType" type="xsd:string"/>
                    <xsd:element name="threeDSRequestorAuthenticationInd" type="xsd:string"/>
                </xsd:sequence>
            </xsd:complexType>

            <!-- Parameter Type -->
            <xsd:complexType name="ParamType">
                <xsd:attribute name="key" type="xsd:string" use="required"/>
                <xsd:attribute name="cookie" type="xsd:string"/>
            </xsd:complexType>

            <!-- Parameters Type -->
            <xsd:complexType name="ParamsType">
                <xsd:sequence>
                    <xsd:element name="param" type="tns:ParamType" minOccurs="0" maxOccurs="unbounded"/>
                </xsd:sequence>
            </xsd:complexType>

            <!-- Auth Data Type -->
            <xsd:complexType name="AuthDataType">
                <xsd:sequence>
                    <xsd:element name="data" minOccurs="0">
                        <xsd:complexType>
                            <xsd:sequence>
                                <xsd:element name="value" type="xsd:string"/>
                            </xsd:sequence>
                            <xsd:attribute name="name" type="xsd:string"/>
                            <xsd:attribute name="authType" type="xsd:string"/>
                        </xsd:complexType>
                    </xsd:element>
                </xsd:sequence>
            </xsd:complexType>

            <!-- verifyRegReq -->
            <xsd:element name="verifyRegReq">
                <xsd:complexType>
                    <xsd:sequence>
                        <xsd:element name="card" type="tns:CardType"/>
                        <xsd:element name="transaction" type="tns:TransactionType"/>
                        <xsd:element name="headerParams" type="tns:ParamsType" minOccurs="0"/>
                        <xsd:element name="extensionParams" type="tns:ParamsType" minOccurs="0"/>
                        <xsd:element name="additionalParams" type="tns:ParamsType" minOccurs="0"/>
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>

            <!-- verifyRegResp -->
            <xsd:element name="verifyRegResp">
                <xsd:complexType>
                    <xsd:sequence>
                        <xsd:element name="cardInfo" minOccurs="0">
                            <xsd:complexType>
                                <xsd:sequence>
                                    <xsd:element name="card_ID" type="xsd:string"/>
                                    <xsd:element name="context_Blob" type="xsd:string"/>
                                    <xsd:element name="regStatus" type="xsd:int"/>
                                    <xsd:element name="authRequired" type="xsd:int"/>
                                    <xsd:element name="authTypeSup" type="xsd:int" maxOccurs="unbounded"/>
                                    <xsd:element name="lanCode" type="xsd:string"/>
                                    <xsd:element name="twoFA" type="xsd:boolean"/>
                                </xsd:sequence>
                            </xsd:complexType>
                        </xsd:element>
                        <xsd:element name="code" type="xsd:int"/>
                        <xsd:element name="errorMessage" type="xsd:string"/>
                        <xsd:element name="errorDetail" type="xsd:string"/>
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>

            <!-- preAuthReq -->
            <xsd:element name="preAuthReq">
                <xsd:complexType>
                    <xsd:sequence>
                        <xsd:element name="card" type="tns:CardType"/>
                        <xsd:element name="transaction" type="tns:TransactionType"/>
                        <xsd:element name="headerParams" type="tns:ParamsType" minOccurs="0"/>
                        <xsd:element name="additionalParams" type="tns:ParamsType" minOccurs="0"/>
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>

            <!-- preAuthResp -->
            <xsd:element name="preAuthResp">
                <xsd:complexType>
                    <xsd:sequence>
                        <xsd:element name="code" type="xsd:int"/>
                        <xsd:element name="errorMessage" type="xsd:string" minOccurs="0"/>
                        <xsd:element name="errorDetail" type="xsd:string" minOccurs="0"/>
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>

            <!-- initAuthReq -->
            <xsd:element name="initAuthReq">
                <xsd:complexType>
                    <xsd:sequence>
                        <xsd:element name="card" type="tns:CardType"/>
                        <xsd:element name="transaction" type="tns:TransactionType"/>
                        <xsd:element name="SMS" type="xsd:string" minOccurs="0"/>
                        <xsd:element name="authType" type="xsd:string" minOccurs="0"/>
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>

            <!-- initAuthResp -->
            <xsd:element name="initAuthResp">
                <xsd:complexType>
                    <xsd:sequence>
                        <xsd:element name="authData" type="tns:AuthDataType"/>
                        <xsd:element name="code" type="xsd:int"/>
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>

            <!-- verifyAuthReq -->
            <xsd:element name="verifyAuthReq">
                <xsd:complexType>
                    <xsd:sequence>
                        <xsd:element name="card" type="tns:CardType"/>
                        <xsd:element name="token">
                            <xsd:complexType>
                                <xsd:simpleContent>
                                    <xsd:extension base="xsd:string">
                                        <xsd:attribute name="authType" type="xsd:string"/>
                                    </xsd:extension>
                                </xsd:simpleContent>
                            </xsd:complexType>
                        </xsd:element>
                        <xsd:element name="transaction" type="tns:TransactionType"/>
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>

            <!-- verifyAuthResp -->
            <xsd:element name="verifyAuthResp">
                <xsd:complexType>
                    <xsd:sequence>
                        <xsd:element name="code" type="xsd:int"/>
                        <xsd:element name="errorMessage" type="xsd:string" minOccurs="0"/>
                        <xsd:element name="errorDetail" type="xsd:string" minOccurs="0"/>
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>
        </xsd:schema>
    </types>

    <!-- === Messages === -->
    <message name="VerifyRegRequest">
        <part name="verifyRegReq" element="tns:verifyRegReq"/>
    </message>
    <message name="VerifyRegResponse">
        <part name="verifyRegResp" element="tns:verifyRegResp"/>
    </message>

    <message name="PreAuthRequest">
        <part name="preAuthReq" element="tns:preAuthReq"/>
    </message>
    <message name="PreAuthResponse">
        <part name="preAuthResp" element="tns:preAuthResp"/>
    </message>

    <message name="InitAuthRequest">
        <part name="initAuthReq" element="tns:initAuthReq"/>
    </message>
    <message name="InitAuthResponse">
        <part name="initAuthResp" element="tns:initAuthResp"/>
    </message>

    <message name="VerifyAuthRequest">
        <part name="verifyAuthReq" element="tns:verifyAuthReq"/>
    </message>
    <message name="VerifyAuthResponse">
        <part name="verifyAuthResp" element="tns:verifyAuthResp"/>
    </message>

    <!-- === Port Type === -->
    <portType name="VerifyPortType">
        <operation name="verifyReg">
            <input message="tns:VerifyRegRequest"/>
            <output message="tns:VerifyRegResponse"/>
        </operation>
        <operation name="preAuth">
            <input message="tns:PreAuthRequest"/>
            <output message="tns:PreAuthResponse"/>
        </operation>
        <operation name="initAuth">
            <input message="tns:InitAuthRequest"/>
            <output message="tns:InitAuthResponse"/>
        </operation>
        <operation name="verifyAuth">
            <input message="tns:VerifyAuthRequest"/>
            <output message="tns:VerifyAuthResponse"/>
        </operation>
    </portType>

    <!-- === Binding (SOAP 1.2) === -->
    <binding name="VerifyBinding" type="tns:VerifyPortType">
        <soap12:binding style="document" transport="http://www.w3.org/2003/05/soap/bindings/HTTP/"/>

        <operation name="verifyReg">
            <soap12:operation soapAction="http://www.gpayments.com/caas/verifyReg"/>
            <input><soap12:body use="literal"/></input>
            <output><soap12:body use="literal"/></output>
        </operation>
        <operation name="preAuth">
            <soap12:operation soapAction="http://www.gpayments.com/caas/preAuth"/>
            <input><soap12:body use="literal"/></input>
            <output><soap12:body use="literal"/></output>
        </operation>
        <operation name="initAuth">
            <soap12:operation soapAction="http://www.gpayments.com/caas/initAuth"/>
            <input><soap12:body use="literal"/></input>
            <output><soap12:body use="literal"/></output>
        </operation>
        <operation name="verifyAuth">
            <soap12:operation soapAction="http://www.gpayments.com/caas/verifyAuth"/>
            <input><soap12:body use="literal"/></input>
            <output><soap12:body use="literal"/></output>
        </operation>
    </binding>

    <!-- === Service === -->
    <service name="VerifyService">
        <documentation>Ryvyl SOAP Verification Service</documentation>
        <port name="VerifyPort" binding="tns:VerifyBinding">
            <soap12:address location="http://localhost:3000/api/soap"/>
        </port>
    </service>
</definitions>`;

        res.set('Content-Type', 'application/xml');
        res.send(wsdlContent);
    }
};

module.exports = {
    simpleSoapHandler,
    getWsdl
};