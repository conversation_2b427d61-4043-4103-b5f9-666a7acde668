classDiagram
class Ticket {
+String paxName
+Date purchaseDate
+String airlineName
+String pnr
+String source
+List~String~ sectors
+Decimal payable
+Decimal receivable
+Decimal profitLoss
}

    class GroupTicket {
        +String airlineName
        +List~String~ paxNames
        +List~String~ sectors
        +String source
        +Decimal payable
        +Decimal receivable
        +Decimal profitLoss
    }

    class UmrahPackage {
        +String source
        +String paxName
        +int numPax
        +Decimal profitLoss
        +boolean includesVisa
        +boolean includesTicket
        +boolean includesHotel
    }

    class Visa {
        +String type (Umrah, Work, Visit)
        +String paxName
        +String status
        +String umrahVisaMonth (optional)
    }

    class Expense {
        +String type (Office, Home)
        +String category
        +String description
        +Decimal amount
        +Date date
    }

    class FinancialRecord {
        +String transactionType
        +Decimal amount
        +Date date
        +String relatedServiceId
        +boolean isPayable
        +boolean isReceivable
        +boolean isProfit
        +boolean isLoss
    }

    Ticket "1" -- "1" FinancialRecord : records
    GroupTicket "1" -- "1" FinancialRecord : records
    UmrahPackage "1" -- "1" FinancialRecord : records
    Visa "1" -- "0..1" FinancialRecord : may affect
    Expense "1" -- "1" FinancialRecord : records