import express from 'express';
import path from 'path';
import fs from 'fs';
import bodyParser from 'body-parser';
import cors from 'cors';
import { fileURLToPath } from 'url';

// Get directory name in ESM
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Import routes
import soapRoutes from './routes/soap.js';
import mongoose from "mongoose";



const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(bodyParser.text({ type: '*/*', limit: '1mb' }));

app.use(express.json());
app.use(express.static(path.join(__dirname, 'public')));

// Set view engine
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views'));

// Connect to MongoDB
mongoose.connect(process.env.DATABASE_URL, {})
    .then(() => console.log('MongoDB connected'))
    .catch(err => console.error(err));
//
// Routes
app.use('/api', soapRoutes);

// Home page
app.get('/', (req, res) => {
    res.render('home');
});

// Sample requests page
app.get('/sample-requests', (req, res) => {
    res.render('sample-requests');
});

// Client example page
app.get('/client-example', (req, res) => {
    res.render('client-example');
});

// Start server
app.listen(PORT, () => {
    console.log(`Server running on port ${PORT}`);
});

export default app;