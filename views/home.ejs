<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ryvyl SOAP Verification Service</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
</head>
<body class="bg-gray-100 min-h-screen">
    <main class="flex min-h-screen flex-col items-center justify-between p-8">
        <div class="w-full max-w-4xl">
            <h1 class="text-3xl font-bold mb-8 text-center">Ryvyl SOAP Verification Service</h1>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="bg-white rounded-lg shadow-md overflow-hidden">
                    <div class="p-6">
                        <h2 class="text-xl font-semibold mb-2">Sample Requests</h2>
                        <p class="text-gray-600 mb-4">
                            Test the SOAP service with pre-configured sample requests based on the provided examples.
                        </p>
                        <p>This page contains sample SOAP requests for all four operations:</p>
                        <ul class="list-disc list-inside mt-2">
                            <li>Verify Registration</li>
                            <li>Pre-Authentication</li>
                            <li>Initialize Authentication</li>
                            <li>Verify Authentication</li>
                        </ul>
                    </div>
                    <div class="bg-gray-50 px-6 py-4">
                        <a href="/sample-requests" class="block w-full bg-blue-500 hover:bg-blue-600 text-white font-semibold py-2 px-4 rounded text-center">
                            View Sample Requests
                        </a>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-md overflow-hidden">
                    <div class="p-6">
                        <h2 class="text-xl font-semibold mb-2">Custom SOAP Client</h2>
                        <p class="text-gray-600 mb-4">Create and send custom SOAP requests to test the service.</p>
                        <p>This page allows you to:</p>
                        <ul class="list-disc list-inside mt-2">
                            <li>Edit SOAP request XML directly</li>
                            <li>Modify SOAP Action headers</li>
                            <li>Send custom requests to the service</li>
                            <li>View the raw SOAP responses</li>
                        </ul>
                    </div>
                    <div class="bg-gray-50 px-6 py-4">
                        <a href="/client-example" class="block w-full bg-blue-500 hover:bg-blue-600 text-white font-semibold py-2 px-4 rounded text-center">
                            Open SOAP Client
                        </a>
                    </div>
                </div>
            </div>

            <div class="mt-8 bg-white rounded-lg shadow-md overflow-hidden">
                <div class="p-6">
                    <h2 class="text-xl font-semibold mb-2">WSDL Information</h2>
                    <p class="text-gray-600 mb-4">Details about the SOAP service WSDL</p>
                    <p>The WSDL for this service is available at:</p>
                    <pre class="bg-gray-100 p-2 rounded-md mt-2 overflow-x-auto">
                        <code>http://localhost:3000/api/wsdl</code>
                    </pre>

                    <p class="mt-4">The service implements four operations:</p>
                    <ul class="list-disc list-inside mt-2">
                        <li>
                            <strong>verifyReg</strong> - Verifies if a card is registered in the system
                        </li>
                        <li>
                            <strong>preAuth</strong> - Performs pre-authentication for a card
                        </li>
                        <li>
                            <strong>initAuth</strong> - Initializes the authentication process
                        </li>
                        <li>
                            <strong>verifyAuth</strong> - Verifies an authentication token
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </main>
</body>
</html>