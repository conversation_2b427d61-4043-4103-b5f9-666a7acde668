<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sample SOAP Requests</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <style>
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
<div class="container mx-auto p-8">
    <h1 class="text-3xl font-bold mb-8">Sample SOAP Requests</h1>
    <p class="mb-8">
        This page contains sample SOAP requests based on the provided examples. Click on any request to send it to the
        SOAP server.
    </p>

    <div class="mb-4">
        <div class="grid grid-cols-4 gap-2">
            <button class="tab-button bg-blue-500 hover:bg-blue-600 text-white font-semibold py-2 px-4 rounded active" data-tab="verifyReg">Verify Registration</button>
            <button class="tab-button bg-blue-500 hover:bg-blue-600 text-white font-semibold py-2 px-4 rounded" data-tab="preAuth">Pre Auth</button>
            <button class="tab-button bg-blue-500 hover:bg-blue-600 text-white font-semibold py-2 px-4 rounded" data-tab="initAuth">Init Auth</button>
            <button class="tab-button bg-blue-500 hover:bg-blue-600 text-white font-semibold py-2 px-4 rounded" data-tab="verifyAuth">Verify Auth</button>
        </div>
    </div>

    <div id="verifyReg" class="tab-content active">
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="p-6">
                <h2 class="text-xl font-semibold mb-2">Verify Card Registration</h2>
                <p class="text-gray-600 mb-4">Sample request to verify if a card is registered</p>
                <textarea id="verifyRegRequest" class="w-full h-80 p-2 border rounded-md font-mono" readonly></textarea>
            </div>
            <div class="bg-gray-50 px-6 py-4 flex justify-end">
                <button onclick="sendSoapRequest('verifyReg')" class="bg-blue-500 hover:bg-blue-600 text-white font-semibold py-2 px-4 rounded" id="verifyRegButton">
                    Send Request
                </button>
            </div>
        </div>
    </div>

    <div id="preAuth" class="tab-content">
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="p-6">
                <h2 class="text-xl font-semibold mb-2">Pre-Authentication</h2>
                <p class="text-gray-600 mb-4">Sample request to perform pre-authentication</p>
                <textarea id="preAuthRequest" class="w-full h-80 p-2 border rounded-md font-mono" readonly></textarea>
            </div>
            <div class="bg-gray-50 px-6 py-4 flex justify-end">
                <button onclick="sendSoapRequest('preAuth')" class="bg-blue-500 hover:bg-blue-600 text-white font-semibold py-2 px-4 rounded" id="preAuthButton">
                    Send Request
                </button>
            </div>
        </div>
    </div>

    <div id="initAuth" class="tab-content">
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="p-6">
                <h2 class="text-xl font-semibold mb-2">Initialize Authentication</h2>
                <p class="text-gray-600 mb-4">Sample request to initialize authentication</p>
                <textarea id="initAuthRequest" class="w-full h-80 p-2 border rounded-md font-mono" readonly></textarea>
            </div>
            <div class="bg-gray-50 px-6 py-4 flex justify-end">
                <button onclick="sendSoapRequest('initAuth')" class="bg-blue-500 hover:bg-blue-600 text-white font-semibold py-2 px-4 rounded" id="initAuthButton">
                    Send Request
                </button>
            </div>
        </div>
    </div>

    <div id="verifyAuth" class="tab-content">
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="p-6">
                <h2 class="text-xl font-semibold mb-2">Verify Authentication</h2>
                <p class="text-gray-600 mb-4">Sample request to verify an authentication token</p>
                <textarea id="verifyAuthRequest" class="w-full h-80 p-2 border rounded-md font-mono" readonly></textarea>
            </div>
            <div class="bg-gray-50 px-6 py-4 flex justify-end">
                <button onclick="sendSoapRequest('verifyAuth')" class="bg-blue-500 hover:bg-blue-600 text-white font-semibold py-2 px-4 rounded" id="verifyAuthButton">
                    Send Request
                </button>
            </div>
        </div>
    </div>

    <div id="errorContainer" class="mt-8 bg-white rounded-lg shadow-md overflow-hidden border-red-500 hidden">
        <div class="p-6">
            <h2 class="text-xl font-semibold mb-2 text-red-500">Error</h2>
            <pre id="errorContent" class="bg-red-50 p-4 rounded-md overflow-auto max-h-96 text-sm text-red-800"></pre>
        </div>
    </div>

    <div id="responseContainer" class="mt-8 bg-white rounded-lg shadow-md overflow-hidden hidden">
        <div class="p-6">
            <h2 class="text-xl font-semibold mb-2">SOAP Response</h2>
            <pre id="responseContent" class="bg-gray-100 p-4 rounded-md overflow-auto max-h-96 text-sm"></pre>
        </div>
    </div>
</div>

<script>
    // Sample SOAP requests
    const verifyRegRequest = `<?xml version='1.0' encoding='UTF-8'?>
<S:Envelope xmlns:S="http://www.w3.org/2003/05/soap-envelope">
    <S:Body>
        <ns2:verifyRegReq xmlns:ns2="http://www.gpayments.com/caas/">
            <ns2:card>
                <id></id>
                <number>*************</number>
                <type>VbV</type>
                <context_Blob></context_Blob>
            </ns2:card>
            <ns2:transaction>
                <purchaseAmount>10000</purchaseAmount>
                <purchaseExponent>2</purchaseExponent>
                <purchaseCurrency>036</purchaseCurrency>
                <purchaseDate>2023-06-27T11:26:38.000Z</purchaseDate>
                <merchantId>123456789012345</merchantId>
                <merchantName>Test Merchant</merchantName>
                <merchantCountry>840</merchantCountry>
                <acqBin>41234567890</acqBin>
                <theeDSProtocolVersion>2.1.0</theeDSProtocolVersion>
                <cardExpiry>2508</cardExpiry>
                <issuerName>remote</issuerName>
                <acsTransId>36e6c66a-3dc6-4a59-a722-96c718824981</acsTransId>
                <threeDSTransId>e6e9bf9b-af70-4f20-ad3a-52ce69de1c15</threeDSTransId>
                <dsTransId>2d5e6910-4d86-4d84-b6a3-9125b80b9ba1</dsTransId>
                <threeDSRequestorID>123456789.visa</threeDSRequestorID>
                <threeDSRequestorName>3dsclient.local.visa</threeDSRequestorName>
                <threeDSServerRefNumber>3DS_LOA_SER_GPPL_020100_00075</threeDSServerRefNumber>
                <threeDSServerOperatorID>1jpeeLAWgGFgS1Ri9tX9</threeDSServerOperatorID>
                <threeDSRequestorURL>http://gpayments.com</threeDSRequestorURL>
                <threeDSServerURL>https://amir-test3.testlab.3dsecure.cloud:9605/api/v2/ds/result/request</threeDSServerURL>
                <deviceChannel>02</deviceChannel>
                <dsReferenceNumber>3DS_LOA_ACS_GPPL_020200_00442</dsReferenceNumber>
                <payTokenInd>0</payTokenInd>
                <mcc>2020</mcc>
                <messageCategory>01</messageCategory>
                <transType>01</transType>
                <acctType>03</acctType>
                <threeDSRequestorAuthenticationInd>01</threeDSRequestorAuthenticationInd>
            </ns2:transaction>
            <headerParams>
                <param key="browserJavaEnabled" cookie="false">false</param>
                <param key="browserTZ" cookie="false">-180</param>
                <param key="browserLanguage" cookie="false">en-US</param>
                <param key="Accept-Language" cookie="false">en-US</param>
                <param key="User-Agent" cookie="false">Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</param>
                <param key="Accept" cookie="false">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</param>
                <param key="proxy-ip" cookie="false">************</param>
                <param key="browserColorDepth" cookie="false">24</param>
                <param key="browserScreenHeight" cookie="false">1050</param>
                <param key="browserScreenWidth" cookie="false">1680</param>
            </headerParams>
            <extensionParams/>
            <additionalParams>
                <param key="shipAddrState">NSW</param>
                <param key="shipAddrCity">Sydney</param>
                <param key="shipAddrCountry">036</param>
                <param key="shipAddrLine1">Unit 1</param>
                <param key="shipAddrLine2">123 Street</param>
                <param key="shipAddrPostCode">2000</param>
                <param key="billAddrCity">Sydney</param>
                <param key="billAddrCountry">036</param>
                <param key="billAddrLine1">Unit 1</param>
                <param key="billAddrLine2">123 Street</param>
                <param key="billAddrState">NSW</param>
                <param key="billAddrPostCode">2000</param>
                <param key="threeDSCompInd">U</param>
                <param key="threeDSRequestorAuthenticationInd">01</param>
                <param key="threeDSRequestorChallengeInd">01</param>
                <param key="addrMatch">Y</param>
                <param key="cardExpiryDate">2508</param>
                <param key="acctID">personal account</param>
                <param key="email"><EMAIL></param>
                <param key="mobilePhone.cc">61</param>
                <param key="mobilePhone.subscriber">**********</param>
            </additionalParams>
        </ns2:verifyRegReq>
    </S:Body>
</S:Envelope>`;

    const preAuthRequest = `<?xml version='1.0' encoding='UTF-8'?>
<S:Envelope xmlns:S="http://www.w3.org/2003/05/soap-envelope">
    <S:Body>
        <ns2:preAuthReq xmlns:ns2="http://www.gpayments.com/caas/">
            <ns2:card>
                <id>*************</id>
                <number>*************</number>
                <type>VbV</type>
                <context_Blob>595</context_Blob>
                <lanCode>0</lanCode>
            </ns2:card>
            <ns2:transaction>
                <purchaseAmount>10000</purchaseAmount>
                <purchaseExponent>2</purchaseExponent>
                <purchaseCurrency>036</purchaseCurrency>
                <purchaseDate>2023-06-28T08:20:17.000Z</purchaseDate>
                <merchantId>123456789012345</merchantId>
                <merchantName>Test Merchant</merchantName>
                <merchantCountry>840</merchantCountry>
                <acqBin>41234567890</acqBin>
                <theeDSProtocolVersion>2.1.0</theeDSProtocolVersion>
                <cardExpiry>2508</cardExpiry>
                <issuerName>remote</issuerName>
                <acsTransId>ef6a1709-acad-4943-b86b-95c6f1e019ee</acsTransId>
                <threeDSTransId>d92d6f98-8bf5-4238-9408-9a41aae1860d</threeDSTransId>
                <dsTransId>afc34d97-f65b-4f75-8e4b-5b102ae0c4ba</dsTransId>
                <threeDSRequestorID>123456789.visa</threeDSRequestorID>
                <threeDSRequestorName>3dsclient.local.visa</threeDSRequestorName>
                <threeDSServerRefNumber>3DS_LOA_SER_GPPL_020100_00075</threeDSServerRefNumber>
                <threeDSServerOperatorID>1jpeeLAWgGFgS1Ri9tX9</threeDSServerOperatorID>
                <threeDSRequestorURL>http://gpayments.com</threeDSRequestorURL>
                <threeDSServerURL>https://amir-test3.testlab.3dsecure.cloud:9605/api/v2/ds/result/request</threeDSServerURL>
                <deviceChannel>02</deviceChannel>
                <dsReferenceNumber>3DS_LOA_ACS_GPPL_020200_00442</dsReferenceNumber>
                <payTokenInd>0</payTokenInd>
                <mcc>2020</mcc>
                <messageCategory>01</messageCategory>
                <transType>01</transType>
                <acctType>03</acctType>
                <threeDSRequestorAuthenticationInd>01</threeDSRequestorAuthenticationInd>
            </ns2:transaction>
            <headerParams>
                <param key="browserJavaEnabled" cookie="false">false</param>
                <param key="browserTZ" cookie="false">-180</param>
                <param key="browserLanguage" cookie="false">en-US</param>
                <param key="Accept-Language" cookie="false">en-US</param>
                <param key="User-Agent" cookie="false">Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</param>
                <param key="Accept" cookie="false">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</param>
                <param key="proxy-ip" cookie="false">************</param>
                <param key="browserColorDepth" cookie="false">24</param>
                <param key="browserScreenHeight" cookie="false">1050</param>
                <param key="browserScreenWidth" cookie="false">1680</param>
            </headerParams>
            <additionalParams>
                <param key="shipAddrState">NSW</param>
                <param key="shipAddrCity">Sydney</param>
                <param key="shipAddrCountry">036</param>
                <param key="shipAddrLine1">Unit 1</param>
                <param key="shipAddrLine2">123 Street</param>
                <param key="shipAddrPostCode">2000</param>
                <param key="billAddrCity">Sydney</param>
                <param key="billAddrCountry">036</param>
                <param key="billAddrLine1">Unit 1</param>
                <param key="billAddrLine2">123 Street</param>
                <param key="billAddrState">NSW</param>
                <param key="billAddrPostCode">2000</param>
                <param key="threeDSCompInd">U</param>
                <param key="threeDSRequestorAuthenticationInd">01</param>
                <param key="threeDSRequestorChallengeInd">01</param>
                <param key="addrMatch">Y</param>
                <param key="cardExpiryDate">2508</param>
                <param key="acctID">personal account</param>
                <param key="email"><EMAIL></param>
                <param key="mobilePhone.cc">61</param>
                <param key="mobilePhone.subscriber">**********</param>
            </additionalParams>
        </ns2:preAuthReq>
    </S:Body>
</S:Envelope>`;

    const initAuthRequest = `<?xml version='1.0' encoding='UTF-8'?>
<S:Envelope xmlns:S="http://www.w3.org/2003/05/soap-envelope">
    <S:Body>
        <ns2:initAuthReq xmlns:ns2="http://www.gpayments.com/caas/">
            <ns2:card>
                <id>*************</id>
                <number>*************</number>
                <type>VbV</type>
                <context_Blob>595</context_Blob>
                <lanCode>0</lanCode>
            </ns2:card>
            <ns2:transaction>
                <purchaseAmount>10000</purchaseAmount>
                <purchaseExponent>2</purchaseExponent>
                <purchaseCurrency>036</purchaseCurrency>
                <purchaseDate>2023-06-27T11:26:38.000Z</purchaseDate>
                <merchantId>123456789012345</merchantId>
                <merchantName>Test Merchant</merchantName>
                <merchantCountry>840</merchantCountry>
                <acqBin>41234567890</acqBin>
                <theeDSProtocolVersion>2.1.0</theeDSProtocolVersion>
                <cardExpiry>2508</cardExpiry>
                <issuerName>remote</issuerName>
                <acsTransId>36e6c66a-3dc6-4a59-a722-96c718824981</acsTransId>
                <threeDSTransId>e6e9bf9b-af70-4f20-ad3a-52ce69de1c15</threeDSTransId>
                <dsTransId>2d5e6910-4d86-4d84-b6a3-9125b80b9ba1</dsTransId>
                <threeDSRequestorID>123456789.visa</threeDSRequestorID>
                <threeDSRequestorName>3dsclient.local.visa</threeDSRequestorName>
                <threeDSServerRefNumber>3DS_LOA_SER_GPPL_020100_00075</threeDSServerRefNumber>
                <threeDSServerOperatorID>1jpeeLAWgGFgS1Ri9tX9</threeDSServerOperatorID>
                <threeDSRequestorURL>http://gpayments.com</threeDSRequestorURL>
                <threeDSServerURL>https://amir-test3.testlab.3dsecure.cloud:9605/api/v2/ds/result/request</threeDSServerURL>
                <deviceChannel>02</deviceChannel>
                <dsReferenceNumber>3DS_LOA_ACS_GPPL_020200_00442</dsReferenceNumber>
                <payTokenInd>0</payTokenInd>
                <mcc>2020</mcc>
                <messageCategory>01</messageCategory>
                <transType>01</transType>
                <acctType>03</acctType>
                <threeDSRequestorAuthenticationInd>01</threeDSRequestorAuthenticationInd>
            </ns2:transaction>
            <SMS>Your OTP is {0}</SMS>
            <authType>2</authType>
        </ns2:initAuthReq>
    </S:Body>
</S:Envelope>`;

    const verifyAuthRequest = `<?xml version='1.0' encoding='UTF-8'?>
<S:Envelope xmlns:S="http://www.w3.org/2003/05/soap-envelope">
    <S:Body>
        <ns2:verifyAuthReq xmlns:ns2="http://www.gpayments.com/caas/">
            <ns2:card>
                <id>*************</id>
                <number>*************</number>
                <type>VbV</type>
                <context_Blob>595</context_Blob>
                <lanCode>0</lanCode>
            </ns2:card>
            <token authType="1">100203045</token>
            <ns2:transaction>
                <purchaseAmount>10000</purchaseAmount>
                <purchaseExponent>2</purchaseExponent>
                <purchaseCurrency>036</purchaseCurrency>
                <purchaseDate>2023-06-27T11:26:38.000Z</purchaseDate>
                <merchantId>123456789012345</merchantId>
                <merchantName>Test Merchant</merchantName>
                <merchantCountry>840</merchantCountry>
                <acqBin>41234567890</acqBin>
                <theeDSProtocolVersion>2.1.0</theeDSProtocolVersion>
                <cardExpiry>2508</cardExpiry>
                <issuerName>remote</issuerName>
                <acsTransId>36e6c66a-3dc6-4a59-a722-96c718824981</acsTransId>
                <threeDSTransId>e6e9bf9b-af70-4f20-ad3a-52ce69de1c15</threeDSTransId>
                <dsTransId>2d5e6910-4d86-4d84-b6a3-9125b80b9ba1</dsTransId>
                <threeDSRequestorID>123456789.visa</threeDSRequestorID>
                <threeDSRequestorName>3dsclient.local.visa</threeDSRequestorName>
                <threeDSServerRefNumber>3DS_LOA_SER_GPPL_020100_00075</threeDSServerRefNumber>
                <threeDSServerOperatorID>1jpeeLAWgGFgS1Ri9tX9</threeDSServerOperatorID>
                <threeDSRequestorURL>http://gpayments.com</threeDSRequestorURL>
                <threeDSServerURL>https://amir-test3.testlab.3dsecure.cloud:9605/api/v2/ds/result/request</threeDSServerURL>
                <deviceChannel>02</deviceChannel>
                <dsReferenceNumber>3DS_LOA_ACS_GPPL_020200_00442</dsReferenceNumber>
                <payTokenInd>0</payTokenInd>
                <mcc>2020</mcc>
                <messageCategory>01</messageCategory>
                <transType>01</transType>
                <acctType>03</acctType>
                <threeDSRequestorAuthenticationInd>01</threeDSRequestorAuthenticationInd>
            </ns2:transaction>
        </ns2:verifyAuthReq>
    </S:Body>
</S:Envelope>`;

    // Initialize textareas with sample requests
    document.getElementById('verifyRegRequest').value = verifyRegRequest;
    document.getElementById('preAuthRequest').value = preAuthRequest;
    document.getElementById('initAuthRequest').value = initAuthRequest;
    document.getElementById('verifyAuthRequest').value = verifyAuthRequest;

    // Tab switching
    const tabButtons = document.querySelectorAll('.tab-button');
    const tabContents = document.querySelectorAll('.tab-content');

    tabButtons.forEach(button => {
        button.addEventListener('click', () => {
            const tabId = button.getAttribute('data-tab');

            // Hide all tab contents
            tabContents.forEach(content => {
                content.classList.remove('active');
            });

            // Remove active class from all buttons
            tabButtons.forEach(btn => {
                btn.classList.remove('active');
            });

            // Show the selected tab content
            document.getElementById(tabId).classList.add('active');

            // Add active class to the clicked button
            button.classList.add('active');
        });
    });

    // Send SOAP request
    async function sendSoapRequest(operation) {
        // Hide previous responses
        document.getElementById('errorContainer').classList.add('hidden');
        document.getElementById('responseContainer').classList.add('hidden');

        // Disable button and show loading state
        const button = document.getElementById(`${operation}Button`);
        const originalText = button.textContent;
        button.textContent = 'Sending...';
        button.disabled = true;

        try {
            const soapAction = `http://www.gpayments.com/caas/${operation}`;
            const soapRequest = document.getElementById(`${operation}Request`).value;

            console.log(`Sending ${operation} request...`);

            const response = await fetch('/api/soap', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/soap+xml; charset=utf-8',
                    'SOAPAction': soapAction
                },
                body: soapRequest
            });

            const responseText = await response.text();

            if (!response.ok) {
                console.error('Server error:', responseText);
                document.getElementById('errorContent').textContent = `Server error: ${response.status} ${response.statusText}\n\n${responseText}`;
                document.getElementById('errorContainer').classList.remove('hidden');
                return;
            }

            console.log(`Received response for ${operation}:`, responseText.substring(0, 100) + '...');
            document.getElementById('responseContent').textContent = responseText;
            document.getElementById('responseContainer').classList.remove('hidden');
        } catch (error) {
            console.error('Error sending SOAP request:', error);
            document.getElementById('errorContent').textContent = `Error: ${error}`;
            document.getElementById('errorContainer').classList.remove('hidden');
        } finally {
            // Restore button state
            button.textContent = originalText;
            button.disabled = false;
        }
    }
</script>
</body>
</html>