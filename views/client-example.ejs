<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SOAP Client Example</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
</head>
<body class="bg-gray-100 min-h-screen">
<div class="container mx-auto p-8">
    <h1 class="text-3xl font-bold mb-8">SOAP Client Example</h1>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="p-6">
                <h2 class="text-xl font-semibold mb-2">SOAP Request</h2>
                <p class="text-gray-600 mb-4">Enter your SOAP request XML and action</p>

                <div class="mb-4">
                    <label class="block text-sm font-medium mb-2">SOAP Action</label>
                    <input
                            type="text"
                            id="soapAction"
                            value="http://www.gpayments.com/caas/verifyReg"
                            class="w-full p-2 border rounded-md"
                    />
                </div>

                <div>
                    <label class="block text-sm font-medium mb-2">SOAP Request XML</label>
                    <textarea
                            id="soapRequest"
                            class="w-full h-80 p-2 border rounded-md font-mono"
                    ><?xml version='1.0' encoding='UTF-8'?>
                        <S:Envelope xmlns:S="http://www.w3.org/2003/05/soap-envelope">
    <S:Body>
        <ns2:verifyRegReq xmlns:ns2="http://www.gpayments.com/caas/">
            <ns2:card>
                <id></id>
                <number>*************</number>
                <type>VbV</type>
                <context_Blob></context_Blob>
            </ns2:card>
            <ns2:transaction>
                <purchaseAmount>10000</purchaseAmount>
                <purchaseExponent>2</purchaseExponent>
                <purchaseCurrency>036</purchaseCurrency>
                <purchaseDate>2023-06-27T11:26:38.000Z</purchaseDate>
                <merchantId>123456789012345</merchantId>
                <merchantName>Test Merchant</merchantName>
                <merchantCountry>840</merchantCountry>
                <acqBin>41234567890</acqBin>
                <theeDSProtocolVersion>2.1.0</theeDSProtocolVersion>
                <cardExpiry>2508</cardExpiry>
                <issuerName>remote</issuerName>
                <acsTransId>36e6c66a-3dc6-4a59-a722-96c718824981</acsTransId>
                <threeDSTransId>e6e9bf9b-af70-4f20-ad3a-52ce69de1c15</threeDSTransId>
                <dsTransId>2d5e6910-4d86-4d84-b6a3-9125b80b9ba1</dsTransId>
                <threeDSRequestorID>123456789.visa</threeDSRequestorID>
                <threeDSRequestorName>3dsclient.local.visa</threeDSRequestorName>
                <threeDSServerRefNumber>3DS_LOA_SER_GPPL_020100_00075</threeDSServerRefNumber>
                <threeDSServerOperatorID>1jpeeLAWgGFgS1Ri9tX9</threeDSServerOperatorID>
                <threeDSRequestorURL>http://gpayments.com</threeDSRequestorURL>
                <threeDSServerURL>https://amir-test3.testlab.3dsecure.cloud:9605/api/v2/ds/result/request</threeDSServerURL>
                <deviceChannel>02</deviceChannel>
                <dsReferenceNumber>3DS_LOA_ACS_GPPL_020200_00442</dsReferenceNumber>
                <payTokenInd>0</payTokenInd>
                <mcc>2020</mcc>
                <messageCategory>01</messageCategory>
                <transType>01</transType>
                <acctType>03</acctType>
                <threeDSRequestorAuthenticationInd>01</threeDSRequestorAuthenticationInd>
            </ns2:transaction>
            <headerParams>
                <param key="browserJavaEnabled" cookie="false">false</param>
                <param key="browserTZ" cookie="false">-180</param>
                <param key="browserLanguage" cookie="false">en-US</param>
                <param key="Accept-Language" cookie="false">en-US</param>
                <param key="User-Agent" cookie="false">Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</param>
                <param key="Accept" cookie="false">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</param>
                <param key="proxy-ip" cookie="false">************</param>
                <param key="browserColorDepth" cookie="false">24</param>
                <param key="browserScreenHeight" cookie="false">1050</param>
                <param key="browserScreenWidth" cookie="false">1680</param>
            </headerParams>
            <extensionParams/>
            <additionalParams>
                <param key="shipAddrState">NSW</param>
                <param key="shipAddrCity">Sydney</param>
                <param key="shipAddrCountry">036</param>
                <param key="shipAddrLine1">Unit 1</param>
                <param key="shipAddrLine2">123 Street</param>
                <param key="shipAddrPostCode">2000</param>
                <param key="billAddrCity">Sydney</param>
                <param key="billAddrCountry">036</param>
                <param key="billAddrLine1">Unit 1</param>
                <param key="billAddrLine2">123 Street</param>
                <param key="billAddrState">NSW</param>
                <param key="billAddrPostCode">2000</param>
                <param key="threeDSCompInd">U</param>
                <param key="threeDSRequestorAuthenticationInd">01</param>
                <param key="threeDSRequestorChallengeInd">01</param>
                <param key="addrMatch">Y</param>
                <param key="cardExpiryDate">2508</param>
                <param key="acctID">personal account</param>
                <param key="email"><EMAIL></param>
                <param key="mobilePhone.cc">61</param>
                <param key="mobilePhone.subscriber">**********</param>
            </additionalParams>
        </ns2:verifyRegReq>
    </S:Body>
</S:Envelope></textarea>
                </div>
            </div>
            <div class="bg-gray-50 px-6 py-4">
                <button id="sendButton" onclick="sendSoapRequest()" class="w-full bg-blue-500 hover:bg-blue-600 text-white font-semibold py-2 px-4 rounded">
                    Send SOAP Request
                </button>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="p-6">
                <h2 class="text-xl font-semibold mb-2">SOAP Response</h2>
                <textarea id="response" readonly class="w-full h-96 p-2 border rounded-md font-mono"></textarea>
            </div>
        </div>
    </div>
</div>

<script>
    async function sendSoapRequest() {
        const soapRequest = document.getElementById('soapRequest').value;
        const soapAction = document.getElementById('soapAction').value;
        const responseTextarea = document.getElementById('response');
        const sendButton = document.getElementById('sendButton');

        // Clear previous response
        responseTextarea.value = '';

        // Disable button and show loading state
        const originalText = sendButton.textContent;
        sendButton.textContent = 'Sending...';
        sendButton.disabled = true;

        try {
            const response = await fetch('/api/soap', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/soap+xml; charset=utf-8',
                    'SOAPAction': soapAction
                },
                body: soapRequest
            });

            const responseText = await response.text();

            if (!response.ok) {
                responseTextarea.value = `Error: ${response.status} ${response.statusText}\n\n${responseText}`;
            } else {
                responseTextarea.value = responseText;
            }
        } catch (error) {
            console.error('Error sending SOAP request:', error);
            responseTextarea.value = `Error: ${error}`;
        } finally {
            // Restore button state
            sendButton.textContent = originalText;
            sendButton.disabled = false;
        }
    }
</script>
</body>
</html>