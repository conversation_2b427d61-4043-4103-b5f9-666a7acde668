# Ryvyl Backend Deployment Files

## 📁 Directory Structure

```
deployment/
├── DEPLOYMENT.md              # Comprehensive deployment guide
├── ENVIRONMENT.md             # Environment variables documentation
├── Dockerfile                 # Multi-stage Docker build
├── .dockerignore              # Docker build optimization
├── docker-compose.yml         # Complete multi-service stack
├── mongo-init.js              # MongoDB initialization script
├── portainer-stack.yml        # Portainer stack configuration
├── ecosystem.config.js        # PM2 process management
├── k8s/                       # Kubernetes manifests
│   ├── deployment.yaml        # Application deployment
│   ├── service.yaml           # Service definitions
│   ├── ingress.yaml           # Ingress controllers
│   ├── configmap.yaml         # Configuration management
│   └── secret.example.yaml    # Secrets template
├── systemd/                   # Systemd service files
│   └── ryvyl-backend.service  # Service definition
└── nginx/                     # Nginx configurations
    ├── reverse-proxy.conf     # Production reverse proxy
    └── nginx.conf             # Docker nginx config
```

## 🚀 Quick Start

### 1. Docker Compose (Recommended for Development)
```bash
# Copy environment file
cp ../example.env .env
# Edit .env with your values

# Start all services
docker-compose up -d

# View logs
docker-compose logs -f app
```

### 2. Kubernetes (Production)
```bash
# Create namespace
kubectl create namespace ryvyl-backend

# Create secrets
kubectl create secret generic ryvyl-secrets \
  --from-literal=jwt-secret="your-jwt-secret" \
  --from-literal=database-url="*************************************" \
  -n ryvyl-backend

# Deploy application
kubectl apply -f k8s/ -n ryvyl-backend
```

### 3. PM2/systemd (Bare Metal)
```bash
# Install dependencies
npm ci --only=production

# Copy ecosystem config
cp deployment/ecosystem.config.js .

# Start with PM2
pm2 start ecosystem.config.js --env production

# Install systemd service
sudo cp deployment/systemd/ryvyl-backend.service /etc/systemd/system/
sudo systemctl enable ryvyl-backend
sudo systemctl start ryvyl-backend
```

## 📋 Pre-Deployment Checklist

### Security Requirements
- [ ] All security vulnerabilities from audit fixed
- [ ] Hardcoded API keys removed
- [ ] CORS properly configured
- [ ] Input validation implemented
- [ ] Rate limiting enabled
- [ ] HTTPS certificates obtained

### Environment Setup
- [ ] Environment variables configured
- [ ] Database connection tested
- [ ] External services configured
- [ ] File upload directories created
- [ ] Log directories created with proper permissions

### Infrastructure Requirements
- [ ] MongoDB instance available
- [ ] Redis instance available (optional)
- [ ] SSL certificates ready
- [ ] Domain names configured
- [ ] Firewall rules configured
- [ ] Backup strategy planned

## 🔧 Configuration Files

### Environment Variables
See `ENVIRONMENT.md` for complete documentation of all environment variables.

**Required Variables:**
- `NODE_ENV`: Application environment
- `DATABASE_URL`: MongoDB connection string
- `JWT_SECRET`: JWT signing secret (min 32 chars)
- `EMAIL_USER`: SMTP username
- `EMAIL_PASS`: SMTP password
- `POSTMARK_API_KEY`: Postmark API key

### Docker Configuration
- `Dockerfile`: Multi-stage build with security best practices
- `docker-compose.yml`: Complete stack with MongoDB, Redis, and Nginx
- `.dockerignore`: Optimized build context

### Kubernetes Configuration
- `deployment.yaml`: Application deployment with HPA
- `service.yaml`: Service definitions for different scenarios
- `ingress.yaml`: Ingress controllers (Nginx, Traefik, ALB)
- `configmap.yaml`: Non-sensitive configuration
- `secret.example.yaml`: Secrets template

### Process Management
- `ecosystem.config.js`: PM2 configuration with clustering
- `ryvyl-backend.service`: systemd service definition

### Reverse Proxy
- `reverse-proxy.conf`: Production Nginx configuration
- `nginx.conf`: Docker Nginx configuration

## 🔐 Security Considerations

### SSL/TLS Setup
```bash
# Let's Encrypt (recommended)
sudo certbot --nginx -d api.ryvyl.com

# Manual certificate
# Place certificates in /etc/nginx/ssl/
```

### Firewall Configuration
```bash
# Allow HTTP/HTTPS
sudo ufw allow 80
sudo ufw allow 443

# Allow SSH (if needed)
sudo ufw allow 22

# Allow application port (if direct access needed)
sudo ufw allow 3000
```

### Security Headers
All configurations include security headers:
- Strict-Transport-Security
- X-Frame-Options
- X-Content-Type-Options
- X-XSS-Protection
- Content-Security-Policy

## 📊 Monitoring and Logging

### Health Checks
- Application: `GET /api/health`
- Database: Built into application health check
- Redis: Built into application health check

### Log Locations
- **Docker**: `docker logs <container>`
- **PM2**: `/var/log/ryvyl/`
- **systemd**: `journalctl -u ryvyl-backend`
- **Nginx**: `/var/log/nginx/`

### Monitoring Setup
```bash
# PM2 monitoring
pm2 monit

# Docker stats
docker stats

# Kubernetes monitoring
kubectl top pods -n ryvyl-backend
```

## 🔄 Deployment Strategies

### Rolling Updates (Kubernetes)
```bash
# Update image
kubectl set image deployment/ryvyl-backend \
  ryvyl-backend=your-registry/ryvyl-backend:v1.0.1 \
  -n ryvyl-backend

# Check rollout
kubectl rollout status deployment/ryvyl-backend -n ryvyl-backend
```

### Zero-Downtime Updates (PM2)
```bash
# Reload application
pm2 reload ecosystem.config.js --env production

# Or graceful restart
pm2 gracefulReload all
```

### Blue-Green Deployment (Docker Compose)
```bash
# Start new version
docker-compose -f docker-compose.blue.yml up -d

# Switch traffic (update load balancer)
# Stop old version
docker-compose -f docker-compose.green.yml down
```

## 🚨 Troubleshooting

### Common Issues

#### Application Won't Start
```bash
# Check logs
docker logs ryvyl-backend
pm2 logs ryvyl-backend
kubectl logs deployment/ryvyl-backend -n ryvyl-backend

# Check environment variables
env | grep -E "(NODE_ENV|DATABASE_URL|JWT_SECRET)"
```

#### Database Connection Issues
```bash
# Test MongoDB connection
mongosh "************************************"

# Check network connectivity
telnet mongo-host 27017
```

#### SSL Certificate Issues
```bash
# Check certificate validity
openssl x509 -in /etc/nginx/ssl/cert.crt -text -noout

# Test SSL configuration
openssl s_client -connect api.ryvyl.com:443
```

### Emergency Procedures

#### Quick Rollback
```bash
# Kubernetes
kubectl rollout undo deployment/ryvyl-backend -n ryvyl-backend

# PM2
pm2 restart ecosystem.config.js

# Docker Compose
docker-compose down && docker-compose up -d
```

#### Scale Down for Maintenance
```bash
# Kubernetes
kubectl scale deployment ryvyl-backend --replicas=0 -n ryvyl-backend

# Docker Compose
docker-compose stop app
```

## 📈 Performance Optimization

### Application Tuning
- Enable Node.js clustering (PM2 cluster mode)
- Implement Redis caching
- Optimize database queries
- Add connection pooling

### Infrastructure Scaling
- Horizontal pod autoscaling (Kubernetes)
- Load balancer configuration
- CDN for static assets
- Database read replicas

## 🔗 Related Documentation

- [DEPLOYMENT.md](./DEPLOYMENT.md) - Complete deployment guide
- [ENVIRONMENT.md](./ENVIRONMENT.md) - Environment configuration
- [../audit-documentation/](../audit-documentation/) - Security audit results

## 📞 Support

For deployment issues:
1. Check logs first
2. Verify environment configuration
3. Test network connectivity
4. Review security audit recommendations
5. Contact development team if issues persist

## 🎯 Next Steps

1. **Security**: Implement all fixes from security audit
2. **Testing**: Add comprehensive test suite
3. **CI/CD**: Set up automated deployment pipeline
4. **Monitoring**: Implement comprehensive monitoring stack
5. **Documentation**: Keep deployment docs updated
