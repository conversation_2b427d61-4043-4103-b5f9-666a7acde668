version: '3.8'

services:
  # Ryvyl Backend Application
  app:
    image: ${DOCKER_REGISTRY}/ryvyl-backend:${APP_VERSION}
    deploy:
      replicas: ${APP_REPLICAS}
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
      resources:
        limits:
          cpus: '1.0'
          memory: 512M
        reservations:
          cpus: '0.5'
          memory: 256M
      update_config:
        parallelism: 1
        delay: 10s
        failure_action: rollback
        order: start-first
    environment:
      NODE_ENV: production
      PORT: 3000
      DATABASE_URL: mongodb://ryvyl_user:${MONGO_PASSWORD}@mongo:27017/ryvyl?authSource=ryvyl
      JWT_SECRET: ${JWT_SECRET}
      EMAIL_USER: ${EMAIL_USER}
      EMAIL_PASS: ${EMAIL_PASS}
      POSTMARK_API_KEY: ${POSTMARK_API_KEY}
      REDIS_URL: redis://redis:6379
      CORS_ORIGINS: ${CORS_ORIGINS}
      RATE_LIMIT_WINDOW: ${RATE_LIMIT_WINDOW}
      RATE_LIMIT_MAX: ${RATE_LIMIT_MAX}
      LOG_LEVEL: ${LOG_LEVEL}
      UPLOAD_MAX_SIZE: ${UPLOAD_MAX_SIZE}
    volumes:
      - uploads:/app/uploads
      - logs:/app/logs
    networks:
      - ryvyl-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    depends_on:
      - mongo
      - redis

  # MongoDB Database
  mongo:
    image: mongo:${MONGO_VERSION}
    deploy:
      replicas: 1
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M
      placement:
        constraints:
          - node.role == manager
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_ROOT_PASSWORD}
      MONGO_INITDB_DATABASE: ryvyl
      MONGO_PASSWORD: ${MONGO_PASSWORD}
    volumes:
      - mongo-data:/data/db
      - mongo-config:/data/configdb
    networks:
      - ryvyl-network
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Redis Cache
  redis:
    image: redis:${REDIS_VERSION}-alpine
    deploy:
      replicas: 1
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
      resources:
        limits:
          cpus: '0.5'
          memory: 256M
        reservations:
          cpus: '0.25'
          memory: 128M
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis-data:/data
    networks:
      - ryvyl-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx Reverse Proxy
  nginx:
    image: nginx:${NGINX_VERSION}-alpine
    deploy:
      replicas: 1
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
      resources:
        limits:
          cpus: '0.5'
          memory: 128M
        reservations:
          cpus: '0.25'
          memory: 64M
    ports:
      - target: 80
        published: ${HTTP_PORT}
        protocol: tcp
        mode: host
      - target: 443
        published: ${HTTPS_PORT}
        protocol: tcp
        mode: host
    volumes:
      - nginx-config:/etc/nginx/conf.d:ro
      - ssl-certs:/etc/nginx/ssl:ro
      - nginx-logs:/var/log/nginx
    networks:
      - ryvyl-network
    depends_on:
      - app
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Log Aggregator (Optional)
  fluentd:
    image: fluent/fluentd:${FLUENTD_VERSION}
    deploy:
      replicas: 1
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
      resources:
        limits:
          cpus: '0.5'
          memory: 256M
        reservations:
          cpus: '0.25'
          memory: 128M
    volumes:
      - logs:/fluentd/log
      - fluentd-config:/fluentd/etc
    networks:
      - ryvyl-network
    environment:
      FLUENTD_CONF: fluent.conf
      FLUENTD_OPT: -v

  # Monitoring (Optional)
  prometheus:
    image: prom/prometheus:${PROMETHEUS_VERSION}
    deploy:
      replicas: 1
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 256M
      placement:
        constraints:
          - node.role == manager
    ports:
      - target: 9090
        published: ${PROMETHEUS_PORT}
        protocol: tcp
        mode: host
    volumes:
      - prometheus-data:/prometheus
      - prometheus-config:/etc/prometheus
    networks:
      - ryvyl-network
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'

volumes:
  mongo-data:
    driver: local
  mongo-config:
    driver: local
  redis-data:
    driver: local
  uploads:
    driver: local
  logs:
    driver: local
  ssl-certs:
    driver: local
  nginx-logs:
    driver: local
  nginx-config:
    driver: local
  fluentd-config:
    driver: local
  prometheus-data:
    driver: local
  prometheus-config:
    driver: local

networks:
  ryvyl-network:
    driver: overlay
    attachable: true
    ipam:
      config:
        - subnet: **********/16

# Environment Variables Template for Portainer
# Copy these to Portainer Stack Environment Variables:
#
# DOCKER_REGISTRY=your-registry.com
# APP_VERSION=latest
# APP_REPLICAS=2
# MONGO_VERSION=6.0
# REDIS_VERSION=7
# NGINX_VERSION=1.24
# FLUENTD_VERSION=v1.16
# PROMETHEUS_VERSION=latest
#
# HTTP_PORT=80
# HTTPS_PORT=443
# PROMETHEUS_PORT=9090
#
# JWT_SECRET=your-super-secure-jwt-secret-key-here
# MONGO_ROOT_PASSWORD=your-mongo-root-password
# MONGO_PASSWORD=your-mongo-app-password
# REDIS_PASSWORD=your-redis-password
#
# EMAIL_USER=<EMAIL>
# EMAIL_PASS=your-email-password
# POSTMARK_API_KEY=your-postmark-api-key
#
# CORS_ORIGINS=https://app.ryvyl.com,https://admin.ryvyl.com
# RATE_LIMIT_WINDOW=900000
# RATE_LIMIT_MAX=100
# LOG_LEVEL=info
# UPLOAD_MAX_SIZE=5242880
