[Unit]
Description=Ryvyl Backend Node.js Application
Documentation=https://github.com/your-org/ryvyl-backend
After=network.target mongod.service redis.service
Wants=mongod.service redis.service
Requires=network.target

[Service]
# Service Type
Type=forking
PIDFile=/var/run/ryvyl-backend.pid

# User and Group
User=ryvyl
Group=ryvyl

# Working Directory
WorkingDirectory=/home/<USER>/ryvyl-backend

# Environment
Environment=NODE_ENV=production
Environment=PORT=3000
EnvironmentFile=/home/<USER>/ryvyl-backend/.env

# PM2 Commands
ExecStart=/usr/bin/pm2 start ecosystem.config.js --env production
ExecReload=/usr/bin/pm2 reload ecosystem.config.js --env production
ExecStop=/usr/bin/pm2 stop ecosystem.config.js

# Process Management
Restart=always
RestartSec=10
StartLimitInterval=60s
StartLimitBurst=3

# Security Settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/home/<USER>/ryvyl-backend/uploads /home/<USER>/ryvyl-backend/logs /var/log/ryvyl
ProtectKernelTunables=true
ProtectKernelModules=true
ProtectControlGroups=true
RestrictRealtime=true
RestrictNamespaces=true

# Resource Limits
LimitNOFILE=65536
LimitNPROC=4096
MemoryLimit=1G
CPUQuota=200%

# Logging
StandardOutput=journal
StandardError=journal
SyslogIdentifier=ryvyl-backend

# Timeout Settings
TimeoutStartSec=60
TimeoutStopSec=30
TimeoutReloadSec=30

[Install]
WantedBy=multi-user.target
