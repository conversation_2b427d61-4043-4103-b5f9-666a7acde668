# Environment Variables & Secrets Configuration

## 📋 Environment Variables Reference

### Core Application Settings

| Variable | Required | Default | Description | Example |
|----------|----------|---------|-------------|---------|
| `NODE_ENV` | Yes | `development` | Application environment | `production`, `development`, `staging` |
| `PORT` | No | `3000` | HTTP server port | `3000`, `8080` |
| `DATABASE_URL` | Yes | - | MongoDB connection string | `*****************************************` |
| `JWT_SECRET` | Yes | - | JWT signing secret (min 32 chars) | `your-super-secure-jwt-secret-key-here` |

### Email Configuration

| Variable | Required | Default | Description | Example |
|----------|----------|---------|-------------|---------|
| `EMAIL_USER` | Yes | - | SMTP username | `<EMAIL>` |
| `EMAIL_PASS` | Yes | - | SMTP password/app password | `vsayhsggiklklewb` |
| `POSTMARK_API_KEY` | Yes | - | Postmark API key for emails | `************************************` |

### External Services

| Variable | Required | Default | Description | Example |
|----------|----------|---------|-------------|---------|
| `CARD_WEBHOOK` | No | - | Card service webhook URL | `https://api.example.com/webhook` |
| `TWILIO_ACCOUNT_SID` | No | - | Twilio account SID for SMS | `ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxx` |
| `TWILIO_AUTH_TOKEN` | No | - | Twilio authentication token | `your_auth_token_here` |

### Security Settings

| Variable | Required | Default | Description | Example |
|----------|----------|---------|-------------|---------|
| `CORS_ORIGINS` | No | `*` | Allowed CORS origins (comma-separated) | `https://app.ryvyl.com,https://admin.ryvyl.com` |
| `RATE_LIMIT_WINDOW` | No | `900000` | Rate limit window in ms (15 min) | `900000` |
| `RATE_LIMIT_MAX` | No | `100` | Max requests per window | `100` |
| `SESSION_SECRET` | No | - | Session signing secret | `your-session-secret-key` |

### File Upload Settings

| Variable | Required | Default | Description | Example |
|----------|----------|---------|-------------|---------|
| `UPLOAD_MAX_SIZE` | No | `5242880` | Max file size in bytes (5MB) | `5242880` |
| `UPLOAD_ALLOWED_TYPES` | No | `image/jpeg,image/png,application/pdf` | Allowed MIME types | `image/jpeg,image/png` |
| `UPLOAD_STORAGE_PATH` | No | `./uploads` | File storage directory | `/var/uploads` |

### Logging & Monitoring

| Variable | Required | Default | Description | Example |
|----------|----------|---------|-------------|---------|
| `LOG_LEVEL` | No | `info` | Logging level | `error`, `warn`, `info`, `debug` |
| `LOG_FORMAT` | No | `combined` | Log format for Morgan | `combined`, `common`, `dev` |
| `ENABLE_REQUEST_LOGGING` | No | `true` | Enable HTTP request logging | `true`, `false` |

### Database Settings

| Variable | Required | Default | Description | Example |
|----------|----------|---------|-------------|---------|
| `DB_MAX_POOL_SIZE` | No | `10` | MongoDB connection pool size | `10` |
| `DB_CONNECT_TIMEOUT` | No | `30000` | Connection timeout in ms | `30000` |
| `DB_SOCKET_TIMEOUT` | No | `0` | Socket timeout in ms (0 = no timeout) | `0` |

## 🔐 Secrets Management

### Development Environment
```bash
# .env file (never commit to git)
NODE_ENV=development
DATABASE_URL=mongodb://localhost:27017/ryvyl-dev
JWT_SECRET=dev-jwt-secret-change-in-production
EMAIL_USER=<EMAIL>
EMAIL_PASS=test-password
POSTMARK_API_KEY=test-api-key
```

### Docker Secrets
```bash
# Create Docker secrets
echo "your-jwt-secret" | docker secret create jwt_secret -
echo "*************************************" | docker secret create database_url -
echo "your-postmark-key" | docker secret create postmark_api_key -

# Use in docker-compose.yml
services:
  app:
    secrets:
      - jwt_secret
      - database_url
      - postmark_api_key
    environment:
      JWT_SECRET_FILE: /run/secrets/jwt_secret
      DATABASE_URL_FILE: /run/secrets/database_url
      POSTMARK_API_KEY_FILE: /run/secrets/postmark_api_key
```

### Kubernetes Secrets
```bash
# Create Kubernetes secret
kubectl create secret generic ryvyl-secrets \
  --from-literal=jwt-secret="your-jwt-secret" \
  --from-literal=database-url="*************************************" \
  --from-literal=postmark-api-key="your-postmark-key" \
  --from-literal=email-pass="your-email-password" \
  -n ryvyl-backend

# Use in deployment
apiVersion: apps/v1
kind: Deployment
spec:
  template:
    spec:
      containers:
      - name: ryvyl-backend
        env:
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: ryvyl-secrets
              key: jwt-secret
```

### Portainer Secrets
```yaml
# In portainer-stack.yml
version: '3.8'
services:
  app:
    environment:
      JWT_SECRET: ${JWT_SECRET}
      DATABASE_URL: ${DATABASE_URL}
      POSTMARK_API_KEY: ${POSTMARK_API_KEY}
```

## ✅ Environment Validation

### Joi Validation Example
```javascript
// config/validation.js
const Joi = require('joi');

const envSchema = Joi.object({
  NODE_ENV: Joi.string()
    .valid('development', 'production', 'staging')
    .default('development'),
  
  PORT: Joi.number()
    .port()
    .default(3000),
  
  DATABASE_URL: Joi.string()
    .uri({ scheme: ['mongodb', 'mongodb+srv'] })
    .required()
    .messages({
      'string.uri': 'DATABASE_URL must be a valid MongoDB connection string',
      'any.required': 'DATABASE_URL is required'
    }),
  
  JWT_SECRET: Joi.string()
    .min(32)
    .required()
    .messages({
      'string.min': 'JWT_SECRET must be at least 32 characters long',
      'any.required': 'JWT_SECRET is required for security'
    }),
  
  EMAIL_USER: Joi.string()
    .email()
    .required(),
  
  EMAIL_PASS: Joi.string()
    .required(),
  
  POSTMARK_API_KEY: Joi.string()
    .pattern(/^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$/)
    .required()
    .messages({
      'string.pattern.base': 'POSTMARK_API_KEY must be a valid UUID format'
    }),
  
  CORS_ORIGINS: Joi.string()
    .default('*')
    .custom((value, helpers) => {
      if (value === '*' && process.env.NODE_ENV === 'production') {
        return helpers.error('cors.wildcard');
      }
      return value;
    })
    .messages({
      'cors.wildcard': 'CORS_ORIGINS cannot be "*" in production'
    }),
  
  RATE_LIMIT_WINDOW: Joi.number()
    .integer()
    .min(60000) // Minimum 1 minute
    .default(900000),
  
  RATE_LIMIT_MAX: Joi.number()
    .integer()
    .min(1)
    .default(100),
  
  UPLOAD_MAX_SIZE: Joi.number()
    .integer()
    .min(1024) // Minimum 1KB
    .max(50 * 1024 * 1024) // Maximum 50MB
    .default(5 * 1024 * 1024),
  
  LOG_LEVEL: Joi.string()
    .valid('error', 'warn', 'info', 'debug')
    .default('info')
}).unknown();

// Validate environment
const { error, value: env } = envSchema.validate(process.env);

if (error) {
  console.error('Environment validation error:', error.details);
  process.exit(1);
}

module.exports = env;
```

### Zod Validation Example
```javascript
// config/env.js
const { z } = require('zod');

const envSchema = z.object({
  NODE_ENV: z.enum(['development', 'production', 'staging']).default('development'),
  PORT: z.coerce.number().int().min(1).max(65535).default(3000),
  
  DATABASE_URL: z.string()
    .url()
    .refine(url => url.startsWith('mongodb://') || url.startsWith('mongodb+srv://'), {
      message: 'DATABASE_URL must be a valid MongoDB connection string'
    }),
  
  JWT_SECRET: z.string()
    .min(32, 'JWT_SECRET must be at least 32 characters long'),
  
  EMAIL_USER: z.string().email(),
  EMAIL_PASS: z.string().min(1),
  
  POSTMARK_API_KEY: z.string()
    .uuid('POSTMARK_API_KEY must be a valid UUID'),
  
  CORS_ORIGINS: z.string()
    .default('*')
    .refine((value) => {
      if (value === '*' && process.env.NODE_ENV === 'production') {
        return false;
      }
      return true;
    }, 'CORS_ORIGINS cannot be "*" in production'),
  
  RATE_LIMIT_WINDOW: z.coerce.number().int().min(60000).default(900000),
  RATE_LIMIT_MAX: z.coerce.number().int().min(1).default(100),
  
  UPLOAD_MAX_SIZE: z.coerce.number()
    .int()
    .min(1024)
    .max(50 * 1024 * 1024)
    .default(5 * 1024 * 1024),
  
  LOG_LEVEL: z.enum(['error', 'warn', 'info', 'debug']).default('info')
});

try {
  const env = envSchema.parse(process.env);
  module.exports = env;
} catch (error) {
  console.error('Environment validation failed:', error.errors);
  process.exit(1);
}
```

## 🔧 Configuration Loading

### Environment File Loading
```javascript
// config/index.js
const path = require('path');
const dotenv = require('dotenv');

// Load environment file based on NODE_ENV
const envFile = process.env.NODE_ENV === 'production' 
  ? '.env.production'
  : process.env.NODE_ENV === 'staging'
  ? '.env.staging'
  : '.env';

dotenv.config({ path: path.resolve(process.cwd(), envFile) });

// Load and validate environment
const env = require('./env');

module.exports = {
  app: {
    env: env.NODE_ENV,
    port: env.PORT,
    corsOrigins: env.CORS_ORIGINS.split(',').map(origin => origin.trim())
  },
  
  database: {
    url: env.DATABASE_URL,
    options: {
      maxPoolSize: env.DB_MAX_POOL_SIZE,
      connectTimeoutMS: env.DB_CONNECT_TIMEOUT,
      socketTimeoutMS: env.DB_SOCKET_TIMEOUT
    }
  },
  
  auth: {
    jwtSecret: env.JWT_SECRET,
    sessionSecret: env.SESSION_SECRET
  },
  
  email: {
    user: env.EMAIL_USER,
    pass: env.EMAIL_PASS,
    postmarkApiKey: env.POSTMARK_API_KEY
  },
  
  upload: {
    maxSize: env.UPLOAD_MAX_SIZE,
    allowedTypes: env.UPLOAD_ALLOWED_TYPES.split(','),
    storagePath: env.UPLOAD_STORAGE_PATH
  },
  
  security: {
    rateLimitWindow: env.RATE_LIMIT_WINDOW,
    rateLimitMax: env.RATE_LIMIT_MAX
  },
  
  logging: {
    level: env.LOG_LEVEL,
    format: env.LOG_FORMAT,
    enableRequestLogging: env.ENABLE_REQUEST_LOGGING === 'true'
  }
};
```

## 🚨 Security Best Practices

### 1. Secret Generation
```bash
# Generate secure JWT secret
node -e "console.log(require('crypto').randomBytes(64).toString('hex'))"

# Generate session secret
openssl rand -base64 32

# Generate API keys
uuidgen
```

### 2. Environment File Security
```bash
# Set proper permissions
chmod 600 .env
chown app:app .env

# Add to .gitignore
echo ".env*" >> .gitignore
echo "!.env.example" >> .gitignore
```

### 3. Production Checklist
- [ ] All secrets are randomly generated
- [ ] No hardcoded credentials in code
- [ ] CORS origins are specific (not `*`)
- [ ] Rate limiting is enabled
- [ ] File upload limits are set
- [ ] Database connections use authentication
- [ ] HTTPS is enforced
- [ ] Environment files have restricted permissions

## 📝 Environment Templates

### .env.example
```bash
# Application
NODE_ENV=development
PORT=3000

# Database
DATABASE_URL=mongodb://localhost:27017/ryvyl

# Authentication
JWT_SECRET=your-jwt-secret-min-32-chars
SESSION_SECRET=your-session-secret

# Email
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-email-password
POSTMARK_API_KEY=your-postmark-api-key

# Security
CORS_ORIGINS=http://localhost:3000,http://localhost:3001
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=100

# File Upload
UPLOAD_MAX_SIZE=5242880
UPLOAD_ALLOWED_TYPES=image/jpeg,image/png,application/pdf
UPLOAD_STORAGE_PATH=./uploads

# Logging
LOG_LEVEL=info
LOG_FORMAT=combined
ENABLE_REQUEST_LOGGING=true

# External Services
CARD_WEBHOOK=https://your-webhook-url.com
TWILIO_ACCOUNT_SID=your-twilio-sid
TWILIO_AUTH_TOKEN=your-twilio-token
```

### .env.production
```bash
NODE_ENV=production
PORT=3000
DATABASE_URL=mongodb+srv://user:<EMAIL>/ryvyl?retryWrites=true&w=majority
JWT_SECRET=production-jwt-secret-64-chars-minimum
CORS_ORIGINS=https://app.ryvyl.com,https://admin.ryvyl.com
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=50
LOG_LEVEL=warn
ENABLE_REQUEST_LOGGING=false
```

## 🔍 Troubleshooting

### Common Issues

1. **Invalid MongoDB URL**: Ensure proper encoding of special characters
2. **JWT Secret Too Short**: Must be at least 32 characters
3. **CORS Issues**: Check origins match exactly (including protocol)
4. **File Upload Fails**: Verify upload directory permissions
5. **Email Not Sending**: Check SMTP credentials and firewall rules

### Debugging Commands
```bash
# Test environment loading
node -e "require('dotenv').config(); console.log(process.env)"

# Validate specific variables
node -e "console.log('JWT_SECRET length:', process.env.JWT_SECRET?.length)"

# Test database connection
node -e "require('mongoose').connect(process.env.DATABASE_URL).then(() => console.log('DB OK'))"
```
