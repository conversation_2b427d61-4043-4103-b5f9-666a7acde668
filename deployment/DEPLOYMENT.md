# Ryvyl Backend Deployment Guide

## 🚀 Overview

This guide provides comprehensive deployment instructions for the Ryvyl Backend Node.js application across multiple deployment strategies:

- **Docker** (Single container)
- **Docker Compose** (Multi-service stack)
- **Portainer** (Container management UI)
- **PM2/systemd** (Bare-metal deployment)
- **Kubernetes** (Container orchestration)

## ⚠️ Security Notice

**CRITICAL**: Before deploying, ensure all security vulnerabilities identified in the audit have been resolved. See `audit-documentation/` for details.

## 📋 Pre-Deployment Checklist

### 1. Prerequisites
- [ ] Node.js 18+ installed locally
- [ ] Docker 20.10+ and Docker Compose 2.0+
- [ ] MongoDB instance available
- [ ] SSL certificates ready (for production)
- [ ] Domain name configured (for production)

### 2. Code Preparation
- [ ] All security vulnerabilities fixed
- [ ] Environment variables configured
- [ ] Database connection tested
- [ ] Dependencies updated and audited
- [ ] Tests passing (when implemented)

### 3. Required Files
- [ ] `.dockerignore` created
- [ ] `package-lock.json` committed
- [ ] Environment files prepared
- [ ] SSL certificates obtained
- [ ] Backup strategy planned

### 4. Security Checklist
- [ ] Remove hardcoded API keys
- [ ] Configure CORS properly
- [ ] Add input validation
- [ ] Enable HTTPS
- [ ] Set up proper authentication

## 🔧 Local Development Setup

### 1. Clone and Install
```bash
git clone <repository-url>
cd ryvyl-backend-main
npm install
```

### 2. Environment Setup
```bash
cp example.env .env
# Edit .env with your configuration
```

### 3. Local Testing
```bash
# Start MongoDB locally
docker run -d --name mongo -p 27017:27017 mongo:6.0

# Start the application
npm start

# Test endpoints
curl http://localhost:3000/api/health
```

## 🐳 Docker Deployment

### 1. Build Docker Image
```bash
# Build the image
docker build -t ryvyl-backend:latest .

# Tag for registry (optional)
docker tag ryvyl-backend:latest your-registry/ryvyl-backend:v1.0.0
```

### 2. Run Single Container
```bash
# Create network
docker network create ryvyl-network

# Run MongoDB
docker run -d \
  --name ryvyl-mongo \
  --network ryvyl-network \
  -v ryvyl-mongo-data:/data/db \
  -e MONGO_INITDB_ROOT_USERNAME=admin \
  -e MONGO_INITDB_ROOT_PASSWORD=secure_password \
  mongo:6.0

# Run application
docker run -d \
  --name ryvyl-backend \
  --network ryvyl-network \
  -p 3000:3000 \
  -e DATABASE_URL="************************************************************************" \
  -e JWT_SECRET="your-jwt-secret" \
  -e NODE_ENV="production" \
  ryvyl-backend:latest
```

### 3. Docker Management
```bash
# View logs
docker logs -f ryvyl-backend

# Update application
docker pull your-registry/ryvyl-backend:v1.0.1
docker stop ryvyl-backend
docker rm ryvyl-backend
# Run with new image

# Backup data
docker exec ryvyl-mongo mongodump --out /backup
docker cp ryvyl-mongo:/backup ./backup
```

## 🐙 Docker Compose Deployment

### 1. Deploy Stack
```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f

# Scale application
docker-compose up -d --scale app=3
```

### 2. Management Commands
```bash
# Update services
docker-compose pull
docker-compose up -d

# Backup database
docker-compose exec mongo mongodump --out /backup

# Restart specific service
docker-compose restart app

# View service status
docker-compose ps
```

### 3. Production Considerations
```bash
# Use production compose file
docker-compose -f docker-compose.prod.yml up -d

# Enable log rotation
docker-compose -f docker-compose.yml -f docker-compose.logging.yml up -d
```

## 🚢 Portainer Deployment

### 1. Install Portainer
```bash
# Create volume
docker volume create portainer_data

# Run Portainer
docker run -d \
  -p 8000:8000 \
  -p 9443:9443 \
  --name portainer \
  --restart=always \
  -v /var/run/docker.sock:/var/run/docker.sock \
  -v portainer_data:/data \
  portainer/portainer-ce:latest
```

### 2. Deploy via Portainer UI
1. Access Portainer at `https://your-server:9443`
2. Go to **Stacks** → **Add Stack**
3. Paste content from `portainer-stack.yml`
4. Configure environment variables
5. Click **Deploy the stack**

### 3. Stack Management
- **Update**: Modify stack and click **Update the stack**
- **Rollback**: Use **Duplicate/Edit** to revert changes
- **Scaling**: Modify replica count in stack definition
- **Logs**: View in **Containers** section

## ⚙️ PM2/systemd Deployment (Bare Metal)

### 1. Server Preparation
```bash
# Install Node.js 18+
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install PM2 globally
sudo npm install -g pm2

# Create application user
sudo useradd -m -s /bin/bash ryvyl
sudo usermod -aG sudo ryvyl
```

### 2. Application Setup
```bash
# Switch to app user
sudo su - ryvyl

# Clone repository
git clone <repository-url> /home/<USER>/ryvyl-backend
cd /home/<USER>/ryvyl-backend

# Install dependencies
npm ci --only=production

# Create environment file
cp example.env .env
# Edit .env with production values
```

### 3. PM2 Configuration
```bash
# Create PM2 ecosystem file
cat > ecosystem.config.js << 'EOF'
module.exports = {
  apps: [{
    name: 'ryvyl-backend',
    script: 'index.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    error_file: '/var/log/ryvyl/error.log',
    out_file: '/var/log/ryvyl/out.log',
    log_file: '/var/log/ryvyl/combined.log',
    time: true
  }]
};
EOF

# Create log directory
sudo mkdir -p /var/log/ryvyl
sudo chown ryvyl:ryvyl /var/log/ryvyl

# Start with PM2
pm2 start ecosystem.config.js
pm2 save
```

### 4. systemd Integration
```bash
# Generate systemd service
sudo pm2 startup systemd -u ryvyl --hp /home/<USER>

# Copy service file
sudo cp systemd/ryvyl-backend.service /etc/systemd/system/

# Enable and start service
sudo systemctl daemon-reload
sudo systemctl enable ryvyl-backend
sudo systemctl start ryvyl-backend
```

### 5. Management Commands
```bash
# Service management
sudo systemctl status ryvyl-backend
sudo systemctl restart ryvyl-backend
sudo systemctl stop ryvyl-backend

# PM2 management
pm2 list
pm2 logs ryvyl-backend
pm2 restart ryvyl-backend
pm2 reload ryvyl-backend  # Zero-downtime restart
```

## ☸️ Kubernetes Deployment

### 1. Prerequisites
```bash
# Ensure kubectl is configured
kubectl cluster-info

# Create namespace
kubectl create namespace ryvyl-backend
```

### 2. Deploy Secrets and ConfigMaps
```bash
# Create secrets (replace with actual values)
kubectl create secret generic ryvyl-secrets \
  --from-literal=jwt-secret="your-jwt-secret" \
  --from-literal=database-url="*************************************" \
  --from-literal=email-pass="your-email-password" \
  -n ryvyl-backend

# Apply ConfigMap
kubectl apply -f k8s/configmap.yaml -n ryvyl-backend
```

### 3. Deploy Application
```bash
# Apply all manifests
kubectl apply -f k8s/ -n ryvyl-backend

# Check deployment status
kubectl get pods -n ryvyl-backend
kubectl get services -n ryvyl-backend
kubectl get ingress -n ryvyl-backend
```

### 4. Scaling and Updates
```bash
# Scale deployment
kubectl scale deployment ryvyl-backend --replicas=5 -n ryvyl-backend

# Rolling update
kubectl set image deployment/ryvyl-backend \
  ryvyl-backend=your-registry/ryvyl-backend:v1.0.1 \
  -n ryvyl-backend

# Check rollout status
kubectl rollout status deployment/ryvyl-backend -n ryvyl-backend

# Rollback if needed
kubectl rollout undo deployment/ryvyl-backend -n ryvyl-backend
```

### 5. Monitoring and Debugging
```bash
# View logs
kubectl logs -f deployment/ryvyl-backend -n ryvyl-backend

# Debug pod issues
kubectl describe pod <pod-name> -n ryvyl-backend

# Port forward for testing
kubectl port-forward service/ryvyl-backend 3000:3000 -n ryvyl-backend
```

## 🔒 TLS/SSL Setup

### 1. Nginx Reverse Proxy
```bash
# Install Nginx
sudo apt update
sudo apt install nginx

# Copy configuration
sudo cp nginx/reverse-proxy.conf /etc/nginx/sites-available/ryvyl-backend
sudo ln -s /etc/nginx/sites-available/ryvyl-backend /etc/nginx/sites-enabled/

# Test configuration
sudo nginx -t
sudo systemctl reload nginx
```

### 2. Let's Encrypt SSL
```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Obtain certificate
sudo certbot --nginx -d your-domain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

### 3. Docker/Kubernetes TLS
For containerized deployments, use:
- **Traefik** with automatic Let's Encrypt
- **cert-manager** for Kubernetes
- **Load balancer** SSL termination (AWS ALB, GCP LB)

## 📊 Monitoring and Logging

### 1. Application Monitoring
```bash
# PM2 monitoring
pm2 monit

# Docker stats
docker stats

# Kubernetes metrics
kubectl top pods -n ryvyl-backend
```

### 2. Log Management
```bash
# Centralized logging with ELK stack
docker run -d \
  --name elasticsearch \
  -p 9200:9200 \
  -e "discovery.type=single-node" \
  elasticsearch:7.17.0

# Log rotation
sudo logrotate -f /etc/logrotate.d/ryvyl-backend
```

### 3. Health Checks
```bash
# Application health endpoint
curl http://localhost:3000/api/health

# Database connectivity
curl http://localhost:3000/api/health/db

# Kubernetes probes automatically handle health checks
```

## 🔄 Backup and Recovery

### 1. Database Backup
```bash
# MongoDB backup
mongodump --uri="*****************************************" --out=/backup/$(date +%Y%m%d)

# Automated backup script
#!/bin/bash
BACKUP_DIR="/backup/$(date +%Y%m%d_%H%M%S)"
mkdir -p $BACKUP_DIR
mongodump --uri="$DATABASE_URL" --out=$BACKUP_DIR
tar -czf $BACKUP_DIR.tar.gz $BACKUP_DIR
rm -rf $BACKUP_DIR
```

### 2. Application Backup
```bash
# Code backup
git archive --format=tar.gz --output=ryvyl-backend-$(date +%Y%m%d).tar.gz HEAD

# Configuration backup
tar -czf config-backup-$(date +%Y%m%d).tar.gz .env ecosystem.config.js
```

## 🚨 Troubleshooting

### Common Issues

#### 1. Application Won't Start
```bash
# Check logs
docker logs ryvyl-backend
pm2 logs ryvyl-backend
kubectl logs deployment/ryvyl-backend -n ryvyl-backend

# Common causes:
# - Missing environment variables
# - Database connection issues
# - Port conflicts
# - Permission issues
```

#### 2. Database Connection Issues
```bash
# Test MongoDB connection
mongo "************************************"

# Check network connectivity
telnet mongo-host 27017
```

#### 3. Performance Issues
```bash
# Check resource usage
docker stats
pm2 monit
kubectl top pods -n ryvyl-backend

# Scale if needed
docker-compose up -d --scale app=3
kubectl scale deployment ryvyl-backend --replicas=5 -n ryvyl-backend
```

### Emergency Procedures

#### 1. Quick Rollback
```bash
# Docker Compose
docker-compose down
docker-compose up -d

# Kubernetes
kubectl rollout undo deployment/ryvyl-backend -n ryvyl-backend

# PM2
pm2 restart ryvyl-backend
```

#### 2. Emergency Maintenance
```bash
# Put application in maintenance mode
# (Implement maintenance endpoint in application)
curl -X POST http://localhost:3000/api/maintenance/enable

# Scale down to zero
kubectl scale deployment ryvyl-backend --replicas=0 -n ryvyl-backend
```

## 📈 Performance Optimization

### 1. Application Tuning
- Enable Node.js clustering (PM2 cluster mode)
- Implement Redis caching
- Optimize database queries
- Add connection pooling

### 2. Infrastructure Scaling
- Horizontal pod autoscaling (Kubernetes)
- Load balancer configuration
- CDN for static assets
- Database read replicas

### 3. Monitoring Setup
- Prometheus + Grafana
- Application Performance Monitoring (APM)
- Log aggregation (ELK/EFK stack)
- Alerting (PagerDuty, Slack)

## 🎯 Next Steps

1. **Security Hardening**: Implement all security fixes from audit
2. **Testing**: Add comprehensive test suite
3. **CI/CD**: Set up automated deployment pipeline
4. **Monitoring**: Implement comprehensive monitoring stack
5. **Documentation**: Keep deployment docs updated

For detailed environment configuration, see `ENVIRONMENT.md`.
For specific configuration files, check the respective files in this directory.
