# Dependencies
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment files
.env*
!.env.example

# Git
.git
.gitignore
.gitattributes

# Documentation
README.md
CHANGELOG.md
*.md
docs/
audit-documentation/
deployment/

# Testing
test/
tests/
coverage/
.nyc_output
*.test.js
*.spec.js
jest.config.js
.jest/

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# next.js build output
.next

# nuxt.js build output
.nuxt

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# Temporary folders
tmp/
temp/

# Build artifacts
dist/
build/

# Local development files
.local
.cache

# Docker files
Dockerfile*
docker-compose*.yml
.dockerignore

# CI/CD files
.github/
.gitlab-ci.yml
.travis.yml
.circleci/

# Backup files
*.backup
*.bak
*.tmp

# SSL certificates (should be mounted as volumes)
ssl/
certs/
*.pem
*.key
*.crt

# Uploads directory (should be mounted as volume)
uploads/

# Local database files
*.db
*.sqlite
*.sqlite3
