module.exports = {
  apps: [
    {
      // Application Configuration
      name: 'ryvyl-backend',
      script: 'index.js',
      cwd: '/home/<USER>/ryvyl-backend',
      
      // Process Management
      instances: 'max', // Use all available CPU cores
      exec_mode: 'cluster',
      
      // Environment Variables
      env: {
        NODE_ENV: 'development',
        PORT: 3000,
        LOG_LEVEL: 'debug'
      },
      env_staging: {
        NODE_ENV: 'staging',
        PORT: 3000,
        LOG_LEVEL: 'info'
      },
      env_production: {
        NODE_ENV: 'production',
        PORT: 3000,
        LOG_LEVEL: 'warn'
      },
      
      // Logging Configuration
      log_file: '/var/log/ryvyl/combined.log',
      out_file: '/var/log/ryvyl/out.log',
      error_file: '/var/log/ryvyl/error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,
      time: true,
      
      // Process Monitoring
      pid_file: '/var/run/ryvyl-backend.pid',
      
      // Auto Restart Configuration
      autorestart: true,
      watch: false, // Disable in production
      max_memory_restart: '1G',
      restart_delay: 4000,
      max_restarts: 10,
      min_uptime: '10s',
      
      // Health Monitoring
      health_check_grace_period: 3000,
      health_check_fatal_exceptions: true,
      
      // Advanced Options
      node_args: [
        '--max-old-space-size=1024',
        '--optimize-for-size'
      ],
      
      // Kill Timeout
      kill_timeout: 5000,
      listen_timeout: 8000,
      
      // Source Map Support
      source_map_support: true,
      
      // Interpreter Options
      interpreter: 'node',
      interpreter_args: '--harmony',
      
      // Cron Restart (optional - restart daily at 3 AM)
      cron_restart: '0 3 * * *',
      
      // Environment File
      env_file: '/home/<USER>/ryvyl-backend/.env'
    },
    
    // SOAP Service (if running separately)
    {
      name: 'ryvyl-soap-service',
      script: 'server.js',
      cwd: '/home/<USER>/ryvyl-backend',
      
      // Process Management
      instances: 2,
      exec_mode: 'cluster',
      
      // Environment Variables
      env: {
        NODE_ENV: 'development',
        PORT: 30080
      },
      env_staging: {
        NODE_ENV: 'staging',
        PORT: 30080
      },
      env_production: {
        NODE_ENV: 'production',
        PORT: 30080
      },
      
      // Logging Configuration
      log_file: '/var/log/ryvyl/soap-combined.log',
      out_file: '/var/log/ryvyl/soap-out.log',
      error_file: '/var/log/ryvyl/soap-error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,
      time: true,
      
      // Process Monitoring
      pid_file: '/var/run/ryvyl-soap-service.pid',
      
      // Auto Restart Configuration
      autorestart: true,
      watch: false,
      max_memory_restart: '512M',
      restart_delay: 4000,
      max_restarts: 10,
      min_uptime: '10s',
      
      // Advanced Options
      node_args: [
        '--max-old-space-size=512'
      ],
      
      // Kill Timeout
      kill_timeout: 5000,
      listen_timeout: 8000,
      
      // Environment File
      env_file: '/home/<USER>/ryvyl-backend/.env'
    }
  ],
  
  // Deployment Configuration
  deploy: {
    production: {
      user: 'ryvyl',
      host: ['your-production-server.com'],
      ref: 'origin/main',
      repo: '**************:your-org/ryvyl-backend.git',
      path: '/home/<USER>/ryvyl-backend',
      'pre-deploy-local': '',
      'post-deploy': 'npm ci --only=production && pm2 reload ecosystem.config.js --env production',
      'pre-setup': '',
      'ssh_options': 'StrictHostKeyChecking=no'
    },
    
    staging: {
      user: 'ryvyl',
      host: ['your-staging-server.com'],
      ref: 'origin/develop',
      repo: '**************:your-org/ryvyl-backend.git',
      path: '/home/<USER>/ryvyl-backend-staging',
      'pre-deploy-local': '',
      'post-deploy': 'npm ci --only=production && pm2 reload ecosystem.config.js --env staging',
      'pre-setup': '',
      'ssh_options': 'StrictHostKeyChecking=no'
    }
  }
};
