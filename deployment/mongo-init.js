// MongoDB initialization script for Ryvyl Backend
// This script creates the application database and user

// Switch to the ryvyl database
db = db.getSiblingDB('ryvyl');

// Create application user with read/write permissions
db.createUser({
  user: 'ryvyl_user',
  pwd: process.env.MONGO_PASSWORD || 'change_this_password',
  roles: [
    {
      role: 'readWrite',
      db: 'ryvyl'
    }
  ]
});

// Create indexes for better performance
db.users.createIndex({ "email": 1 }, { unique: true });
db.users.createIndex({ "status": 1 });
db.users.createIndex({ "dashboard": 1 });
db.users.createIndex({ "createdAt": 1 });

db.individualOnboarding.createIndex({ "clientCode": 1 }, { unique: true });
db.individualOnboarding.createIndex({ "personalInfo.email": 1 });
db.individualOnboarding.createIndex({ "status": 1 });
db.individualOnboarding.createIndex({ "createdAt": 1 });

db.companies.createIndex({ "ryvyl_id": 1 }, { unique: true });
db.companies.createIndex({ "company_name": 1 });
db.companies.createIndex({ "status": 1 });

db.cardPrograms.createIndex({ "company": 1 });
db.cardPrograms.createIndex({ "status": 1 });
db.cardPrograms.createIndex({ "created_at": 1 });

db.activities.createIndex({ "timestamp": -1 });
db.activities.createIndex({ "userId": 1 });
db.activities.createIndex({ "ip": 1 });
db.activities.createIndex({ "pathname": 1 });

db.logs.createIndex({ "timestamp": -1 });
db.logs.createIndex({ "level": 1 });

// Create TTL index for logs (auto-delete after 30 days)
db.logs.createIndex({ "timestamp": 1 }, { expireAfterSeconds: 2592000 });

print('Database initialization completed successfully');
print('Created user: ryvyl_user');
print('Created indexes for performance optimization');
print('Set up TTL index for log cleanup');
