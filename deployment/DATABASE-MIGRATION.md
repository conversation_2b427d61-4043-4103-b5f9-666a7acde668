# Database Migration Guide - Ryvyl Backend

## 📋 Overview

This guide covers migrating the Ryvyl Backend database from the current MongoDB instance to a new one, including data preservation, schema updates, security improvements, and performance optimizations.

## 🎯 Migration Scenarios

### 1. **Production Database Migration**
- Moving to a new MongoDB cluster
- Upgrading MongoDB version
- Changing hosting providers
- Implementing replica sets

### 2. **Development to Production**
- Migrating development data to production
- Sanitizing sensitive data
- Applying production-ready configurations

### 3. **Schema Updates**
- Adding new fields and indexes
- Removing deprecated fields
- Updating validation rules
- Implementing security improvements

## ⚠️ Pre-Migration Checklist

### Critical Requirements
- [ ] **Security audit fixes implemented** (see audit-documentation/)
- [ ] **Full database backup created**
- [ ] **Application downtime scheduled**
- [ ] **New database instance ready**
- [ ] **Migration scripts tested**
- [ ] **Rollback plan prepared**

### Environment Preparation
- [ ] MongoDB tools installed (`mongodump`, `mongorestore`, `mongosh`)
- [ ] Network connectivity between old and new databases
- [ ] Sufficient disk space for backup files
- [ ] Access credentials for both databases
- [ ] Application stopped or in maintenance mode

## 🔧 Migration Methods

### Method 1: Full Database Dump and Restore (Recommended)

#### Step 1: Create Full Backup
```bash
#!/bin/bash
# backup-database.sh

# Configuration
SOURCE_URI="******************************************"
BACKUP_DIR="/backup/ryvyl-migration-$(date +%Y%m%d_%H%M%S)"
LOG_FILE="/var/log/ryvyl-migration.log"

# Create backup directory
mkdir -p "$BACKUP_DIR"

echo "$(date): Starting database backup..." | tee -a "$LOG_FILE"

# Full database dump
mongodump \
  --uri="$SOURCE_URI" \
  --out="$BACKUP_DIR" \
  --gzip \
  --verbose 2>&1 | tee -a "$LOG_FILE"

# Verify backup
if [ $? -eq 0 ]; then
    echo "$(date): Backup completed successfully" | tee -a "$LOG_FILE"
    echo "Backup location: $BACKUP_DIR" | tee -a "$LOG_FILE"
    
    # Calculate backup size
    du -sh "$BACKUP_DIR" | tee -a "$LOG_FILE"
else
    echo "$(date): Backup failed!" | tee -a "$LOG_FILE"
    exit 1
fi
```

#### Step 2: Prepare New Database
```bash
#!/bin/bash
# prepare-new-database.sh

NEW_URI="****************************************"
DB_NAME="ryvyl"

echo "$(date): Preparing new database..." | tee -a "$LOG_FILE"

# Connect to new database and create application user
mongosh "$NEW_URI" --eval "
// Switch to ryvyl database
use $DB_NAME;

// Create application user
db.createUser({
  user: 'ryvyl_user',
  pwd: 'secure_new_password',
  roles: [
    { role: 'readWrite', db: '$DB_NAME' },
    { role: 'dbAdmin', db: '$DB_NAME' }
  ]
});

print('Database user created successfully');
"
```

#### Step 3: Restore Data to New Database
```bash
#!/bin/bash
# restore-database.sh

NEW_URI="***************************************************************"
BACKUP_DIR="/backup/ryvyl-migration-20241201_120000"

echo "$(date): Starting database restore..." | tee -a "$LOG_FILE"

# Restore database
mongorestore \
  --uri="$NEW_URI" \
  --gzip \
  --drop \
  --verbose \
  "$BACKUP_DIR/ryvyl" 2>&1 | tee -a "$LOG_FILE"

# Verify restore
if [ $? -eq 0 ]; then
    echo "$(date): Restore completed successfully" | tee -a "$LOG_FILE"
else
    echo "$(date): Restore failed!" | tee -a "$LOG_FILE"
    exit 1
fi
```

### Method 2: Live Migration with Replica Set

#### Step 1: Add New Server to Replica Set
```javascript
// Connect to primary server
mongosh "mongodb://primary-server:27017/admin"

// Add new server as secondary
rs.add("new-server:27017")

// Wait for initial sync
rs.status()
```

#### Step 2: Promote New Server
```javascript
// Step down current primary
rs.stepDown()

// Verify new primary
rs.status()

// Remove old servers
rs.remove("old-server1:27017")
rs.remove("old-server2:27017")
```

### Method 3: Application-Level Migration

#### Step 1: Dual-Write Setup
```javascript
// config/database.js
const mongoose = require('mongoose');

const oldConnection = mongoose.createConnection(process.env.OLD_DATABASE_URL);
const newConnection = mongoose.createConnection(process.env.NEW_DATABASE_URL);

// Dual-write middleware
const dualWriteMiddleware = async (req, res, next) => {
  if (process.env.MIGRATION_MODE === 'dual-write') {
    // Write to both databases
    req.oldDb = oldConnection;
    req.newDb = newConnection;
  }
  next();
};

module.exports = { oldConnection, newConnection, dualWriteMiddleware };
```

## 🔄 Schema Migration Scripts

### Security Improvements Migration
```javascript
// migrations/001-security-improvements.js
const { MongoClient } = require('mongodb');

async function securityMigration(db) {
  console.log('Starting security improvements migration...');
  
  // 1. Remove hardcoded API keys from user documents
  await db.collection('users').updateMany(
    { 'apiKeys.postmark': { $exists: true } },
    { $unset: { 'apiKeys.postmark': 1 } }
  );
  
  // 2. Hash any plain text passwords (if any exist)
  const bcrypt = require('bcrypt');
  const usersWithPlainPasswords = await db.collection('users').find({
    password: { $not: /^\$2[aby]\$/ } // Not bcrypt hashed
  }).toArray();
  
  for (const user of usersWithPlainPasswords) {
    const hashedPassword = await bcrypt.hash(user.password, 12);
    await db.collection('users').updateOne(
      { _id: user._id },
      { $set: { password: hashedPassword } }
    );
  }
  
  // 3. Add security fields
  await db.collection('users').updateMany(
    { lastLoginAt: { $exists: false } },
    { 
      $set: { 
        lastLoginAt: null,
        lastLoginIP: null,
        failedLoginAttempts: 0,
        accountLocked: false,
        twoFactorEnabled: false
      }
    }
  );
  
  console.log('Security improvements migration completed');
}

module.exports = { securityMigration };
```

### Index Creation Migration
```javascript
// migrations/002-create-indexes.js
async function createIndexes(db) {
  console.log('Creating performance indexes...');
  
  // User indexes
  await db.collection('users').createIndex({ email: 1 }, { unique: true });
  await db.collection('users').createIndex({ status: 1 });
  await db.collection('users').createIndex({ dashboard: 1 });
  await db.collection('users').createIndex({ createdAt: 1 });
  await db.collection('users').createIndex({ lastLoginAt: 1 });
  
  // Individual onboarding indexes
  await db.collection('individualOnboarding').createIndex({ clientCode: 1 }, { unique: true });
  await db.collection('individualOnboarding').createIndex({ 'personalInfo.email': 1 });
  await db.collection('individualOnboarding').createIndex({ status: 1 });
  await db.collection('individualOnboarding').createIndex({ createdAt: 1 });
  
  // Company indexes
  await db.collection('companies').createIndex({ ryvyl_id: 1 }, { unique: true });
  await db.collection('companies').createIndex({ company_name: 1 });
  await db.collection('companies').createIndex({ status: 1 });
  
  // Activity indexes
  await db.collection('activities').createIndex({ timestamp: -1 });
  await db.collection('activities').createIndex({ userId: 1 });
  await db.collection('activities').createIndex({ ip: 1 });
  await db.collection('activities').createIndex({ pathname: 1 });
  
  // Log indexes with TTL
  await db.collection('logs').createIndex({ timestamp: -1 });
  await db.collection('logs').createIndex({ level: 1 });
  await db.collection('logs').createIndex(
    { timestamp: 1 }, 
    { expireAfterSeconds: 2592000 } // 30 days
  );
  
  console.log('Index creation completed');
}

module.exports = { createIndexes };
```

### Data Cleanup Migration
```javascript
// migrations/003-data-cleanup.js
async function dataCleanup(db) {
  console.log('Starting data cleanup...');
  
  // 1. Remove sensitive data from logs
  await db.collection('logs').updateMany(
    { 'requestBody.password': { $exists: true } },
    { $unset: { 'requestBody.password': 1 } }
  );
  
  await db.collection('logs').updateMany(
    { 'requestBody.token': { $exists: true } },
    { $unset: { 'requestBody.token': 1 } }
  );
  
  // 2. Remove old temporary data
  await db.collection('temporaryData').deleteMany({
    createdAt: { $lt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) } // 7 days old
  });
  
  // 3. Standardize status values
  await db.collection('users').updateMany(
    { status: { $in: ['Active', 'ACTIVE'] } },
    { $set: { status: 'active' } }
  );
  
  await db.collection('users').updateMany(
    { status: { $in: ['Inactive', 'INACTIVE'] } },
    { $set: { status: 'inactive' } }
  );
  
  // 4. Remove duplicate entries (if any)
  const duplicateEmails = await db.collection('users').aggregate([
    { $group: { _id: '$email', count: { $sum: 1 }, docs: { $push: '$_id' } } },
    { $match: { count: { $gt: 1 } } }
  ]).toArray();
  
  for (const duplicate of duplicateEmails) {
    // Keep the first document, remove others
    const [keep, ...remove] = duplicate.docs;
    await db.collection('users').deleteMany({ _id: { $in: remove } });
  }
  
  console.log('Data cleanup completed');
}

module.exports = { dataCleanup };
```

## 🔧 Complete Migration Script

### Master Migration Script
```javascript
// migrate-database.js
const { MongoClient } = require('mongodb');
const { securityMigration } = require('./migrations/001-security-improvements');
const { createIndexes } = require('./migrations/002-create-indexes');
const { dataCleanup } = require('./migrations/003-data-cleanup');

async function runMigration() {
  const sourceUri = process.env.SOURCE_DATABASE_URL;
  const targetUri = process.env.TARGET_DATABASE_URL;
  
  console.log('Starting database migration...');
  console.log(`Source: ${sourceUri.replace(/\/\/.*@/, '//***:***@')}`);
  console.log(`Target: ${targetUri.replace(/\/\/.*@/, '//***:***@')}`);
  
  try {
    // Connect to target database
    const client = new MongoClient(targetUri);
    await client.connect();
    const db = client.db();
    
    // Run migrations in order
    await securityMigration(db);
    await createIndexes(db);
    await dataCleanup(db);
    
    // Verify migration
    await verifyMigration(db);
    
    await client.close();
    console.log('Migration completed successfully!');
    
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  }
}

async function verifyMigration(db) {
  console.log('Verifying migration...');
  
  // Check collections exist
  const collections = await db.listCollections().toArray();
  const collectionNames = collections.map(c => c.name);
  
  const requiredCollections = [
    'users', 'individualOnboarding', 'companies', 
    'activities', 'logs', 'cardPrograms'
  ];
  
  for (const collection of requiredCollections) {
    if (!collectionNames.includes(collection)) {
      throw new Error(`Missing collection: ${collection}`);
    }
  }
  
  // Check indexes
  const userIndexes = await db.collection('users').indexes();
  const emailIndex = userIndexes.find(idx => idx.key && idx.key.email);
  if (!emailIndex || !emailIndex.unique) {
    throw new Error('Email unique index missing');
  }
  
  // Check data integrity
  const userCount = await db.collection('users').countDocuments();
  const onboardingCount = await db.collection('individualOnboarding').countDocuments();
  
  console.log(`Verification completed:`);
  console.log(`- Users: ${userCount}`);
  console.log(`- Onboarding records: ${onboardingCount}`);
  console.log(`- Collections: ${collectionNames.length}`);
}

// Run migration
if (require.main === module) {
  runMigration();
}

module.exports = { runMigration };
```

## 🚀 Execution Steps

### 1. Pre-Migration
```bash
# Set environment variables
export SOURCE_DATABASE_URL="******************************************"
export TARGET_DATABASE_URL="******************************************"

# Stop application
sudo systemctl stop ryvyl-backend
# or
pm2 stop ryvyl-backend
# or
kubectl scale deployment ryvyl-backend --replicas=0 -n ryvyl-backend

# Create backup
./backup-database.sh
```

### 2. Migration Execution
```bash
# Method 1: Full dump/restore
./prepare-new-database.sh
./restore-database.sh
node migrate-database.js

# Method 2: Live migration (if using replica sets)
# Follow replica set steps above

# Method 3: Application-level (gradual migration)
export MIGRATION_MODE="dual-write"
# Start application with dual-write mode
```

### 3. Post-Migration
```bash
# Update application configuration
export DATABASE_URL="$TARGET_DATABASE_URL"

# Start application
sudo systemctl start ryvyl-backend
# or
pm2 start ecosystem.config.js --env production
# or
kubectl scale deployment ryvyl-backend --replicas=3 -n ryvyl-backend

# Verify application health
curl http://localhost:3000/api/health
```

## ✅ Verification Checklist

### Data Integrity
- [ ] All collections migrated
- [ ] Document counts match
- [ ] Indexes created successfully
- [ ] Unique constraints working
- [ ] Relationships preserved

### Security Improvements
- [ ] Hardcoded secrets removed
- [ ] Passwords properly hashed
- [ ] Sensitive data sanitized
- [ ] Access controls implemented
- [ ] Audit trails preserved

### Performance
- [ ] Indexes optimized
- [ ] Query performance tested
- [ ] Connection pooling configured
- [ ] TTL indexes working
- [ ] Backup strategy implemented

### Application Testing
- [ ] Authentication working
- [ ] User registration working
- [ ] File uploads working
- [ ] API endpoints responding
- [ ] SOAP service working

## 🔄 Rollback Procedure

### Emergency Rollback
```bash
#!/bin/bash
# rollback.sh

echo "$(date): Starting emergency rollback..." | tee -a "$LOG_FILE"

# Stop application
sudo systemctl stop ryvyl-backend

# Restore original database URL
export DATABASE_URL="$SOURCE_DATABASE_URL"

# Start application
sudo systemctl start ryvyl-backend

# Verify rollback
curl http://localhost:3000/api/health

echo "$(date): Rollback completed" | tee -a "$LOG_FILE"
```

## 📊 Monitoring During Migration

### Key Metrics to Monitor
- Database connection status
- Application response times
- Error rates
- Memory usage
- Disk space
- Network connectivity

### Monitoring Commands
```bash
# Database status
mongosh "$NEW_URI" --eval "db.runCommand({serverStatus: 1})"

# Application health
watch -n 5 'curl -s http://localhost:3000/api/health'

# System resources
htop
df -h
netstat -an | grep 27017
```

## 🎯 Best Practices

### Before Migration
1. **Test migration on staging environment**
2. **Create multiple backup copies**
3. **Document current database state**
4. **Plan for extended downtime**
5. **Prepare rollback procedures**

### During Migration
1. **Monitor all systems continuously**
2. **Keep detailed logs**
3. **Verify each step before proceeding**
4. **Have team members on standby**
5. **Communicate status to stakeholders**

### After Migration
1. **Verify all functionality thoroughly**
2. **Monitor performance for 24-48 hours**
3. **Keep old database for 1 week minimum**
4. **Update documentation**
5. **Review and improve migration process**

## 📞 Emergency Contacts

- **Database Administrator**: [Contact Info]
- **Development Team Lead**: [Contact Info]
- **Infrastructure Team**: [Contact Info]
- **Business Stakeholders**: [Contact Info]

## 📝 Migration Log Template

```
Migration Date: [DATE]
Migration Type: [Full/Live/Application-level]
Downtime Window: [START] - [END]
Team Members: [NAMES]

Pre-Migration Checklist:
[ ] Backup created
[ ] New database prepared
[ ] Application stopped
[ ] Team notified

Migration Steps:
[ ] Step 1: [DESCRIPTION] - [STATUS] - [TIME]
[ ] Step 2: [DESCRIPTION] - [STATUS] - [TIME]
[ ] Step 3: [DESCRIPTION] - [STATUS] - [TIME]

Post-Migration Verification:
[ ] Data integrity verified
[ ] Application health checked
[ ] Performance tested
[ ] Security verified

Issues Encountered:
[DESCRIPTION OF ANY ISSUES AND RESOLUTIONS]

Lessons Learned:
[IMPROVEMENTS FOR FUTURE MIGRATIONS]
```

This migration guide provides a comprehensive approach to safely migrating the Ryvyl Backend database while implementing security improvements and performance optimizations identified in the security audit.
