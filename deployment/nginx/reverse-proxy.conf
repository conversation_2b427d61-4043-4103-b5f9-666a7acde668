# Ryvyl Backend Nginx Reverse Proxy Configuration
# Place this file in /etc/nginx/sites-available/ryvyl-backend
# Create symlink: ln -s /etc/nginx/sites-available/ryvyl-backend /etc/nginx/sites-enabled/

# Rate limiting zones
limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
limit_req_zone $binary_remote_addr zone=login:10m rate=1r/s;
limit_req_zone $binary_remote_addr zone=upload:10m rate=2r/s;

# Upstream backend servers
upstream ryvyl_backend {
    least_conn;
    server 127.0.0.1:3000 max_fails=3 fail_timeout=30s weight=1;
    # Add more backend servers for load balancing
    # server 127.0.0.1:3001 max_fails=3 fail_timeout=30s weight=1;
    # server 127.0.0.1:3002 max_fails=3 fail_timeout=30s weight=1;
    keepalive 32;
}

# SOAP service upstream
upstream ryvyl_soap {
    server 127.0.0.1:30080 max_fails=3 fail_timeout=30s;
    keepalive 16;
}

# HTTP server - redirect to HTTPS
server {
    listen 80;
    listen [::]:80;
    server_name api.ryvyl.com admin-api.ryvyl.com;
    
    # Let's Encrypt challenge
    location /.well-known/acme-challenge/ {
        root /var/www/certbot;
    }
    
    # Redirect all HTTP traffic to HTTPS
    location / {
        return 301 https://$server_name$request_uri;
    }
}

# HTTPS server - Main API
server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name api.ryvyl.com;
    
    # SSL Configuration
    ssl_certificate /etc/nginx/ssl/api.ryvyl.com.crt;
    ssl_certificate_key /etc/nginx/ssl/api.ryvyl.com.key;
    ssl_trusted_certificate /etc/nginx/ssl/api.ryvyl.com.chain.crt;
    
    # SSL Security Settings
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    ssl_session_tickets off;
    ssl_stapling on;
    ssl_stapling_verify on;
    
    # Security Headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Permissions-Policy "geolocation=(), microphone=(), camera=()" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self'; frame-ancestors 'none';" always;
    
    # General Settings
    client_max_body_size 10M;
    client_body_timeout 60s;
    client_header_timeout 60s;
    keepalive_timeout 65s;
    send_timeout 60s;
    
    # Gzip Compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;
    
    # Logging
    access_log /var/log/nginx/ryvyl-api-access.log combined;
    error_log /var/log/nginx/ryvyl-api-error.log warn;
    
    # Health check endpoint (bypass auth and rate limiting)
    location = /api/health {
        access_log off;
        proxy_pass http://ryvyl_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # Authentication endpoints with strict rate limiting
    location ~ ^/api/auth/(login|register|reset-password) {
        limit_req zone=login burst=5 nodelay;
        limit_req_status 429;
        
        proxy_pass http://ryvyl_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Request-ID $request_id;
        
        # Timeout settings
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
        
        # Buffer settings
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
    }
    
    # File upload endpoints with upload rate limiting
    location ~ ^/api/.*/upload {
        limit_req zone=upload burst=10 nodelay;
        limit_req_status 429;
        
        client_max_body_size 10M;
        client_body_timeout 120s;
        
        proxy_pass http://ryvyl_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Request-ID $request_id;
        
        # Extended timeouts for file uploads
        proxy_connect_timeout 60s;
        proxy_send_timeout 120s;
        proxy_read_timeout 120s;
        
        # Disable buffering for uploads
        proxy_request_buffering off;
        proxy_buffering off;
    }
    
    # API endpoints with standard rate limiting
    location /api/ {
        limit_req zone=api burst=20 nodelay;
        limit_req_status 429;
        
        proxy_pass http://ryvyl_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Request-ID $request_id;
        
        # Timeout settings
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
        
        # Buffer settings
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
        
        # Cache static API responses
        location ~* \.(json)$ {
            expires 5m;
            add_header Cache-Control "public, no-transform";
        }
    }
    
    # SOAP service endpoints
    location /soap/ {
        proxy_pass http://ryvyl_soap/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header SOAPAction $http_soapaction;
        
        # SOAP specific settings
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        proxy_buffering off;
    }
    
    # Static file serving (if needed)
    location /uploads/ {
        alias /home/<USER>/ryvyl-backend/uploads/;
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header X-Content-Type-Options "nosniff";
        
        # Security: prevent execution of uploaded files
        location ~* \.(php|pl|py|jsp|asp|sh|cgi)$ {
            deny all;
        }
    }
    
    # Default location
    location / {
        proxy_pass http://ryvyl_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Request-ID $request_id;
    }
    
    # Error pages
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;
    
    location = /404.html {
        root /var/www/html;
        internal;
    }
    
    location = /50x.html {
        root /var/www/html;
        internal;
    }
    
    # Deny access to hidden files
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # Deny access to backup files
    location ~ ~$ {
        deny all;
        access_log off;
        log_not_found off;
    }
}

# Admin API server (if separate)
server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name admin-api.ryvyl.com;
    
    # SSL Configuration (same as above)
    ssl_certificate /etc/nginx/ssl/admin-api.ryvyl.com.crt;
    ssl_certificate_key /etc/nginx/ssl/admin-api.ryvyl.com.key;
    ssl_trusted_certificate /etc/nginx/ssl/admin-api.ryvyl.com.chain.crt;
    
    # SSL Security Settings (same as above)
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    ssl_session_tickets off;
    ssl_stapling on;
    ssl_stapling_verify on;
    
    # Security Headers (same as above)
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    
    # IP Whitelist for admin access (optional)
    # allow ***********/24;
    # allow 10.0.0.0/8;
    # deny all;
    
    # Basic Auth for additional security (optional)
    # auth_basic "Admin Area";
    # auth_basic_user_file /etc/nginx/.htpasswd;
    
    # Logging
    access_log /var/log/nginx/ryvyl-admin-access.log combined;
    error_log /var/log/nginx/ryvyl-admin-error.log warn;
    
    # Proxy to backend
    location / {
        limit_req zone=api burst=10 nodelay;
        
        proxy_pass http://ryvyl_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Request-ID $request_id;
    }
}
