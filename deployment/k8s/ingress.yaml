apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ryvyl-backend-ingress
  namespace: ryvyl-backend
  labels:
    app: ryvyl-backend
  annotations:
    # Nginx Ingress Controller annotations
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "10m"
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "30"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "30"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "30"
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
    
    # Security headers
    nginx.ingress.kubernetes.io/configuration-snippet: |
      add_header X-Frame-Options "SAMEORIGIN" always;
      add_header X-Content-Type-Options "nosniff" always;
      add_header X-XSS-Protection "1; mode=block" always;
      add_header Referrer-Policy "strict-origin-when-cross-origin" always;
      add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self'; frame-ancestors 'none';" always;
    
    # CORS configuration
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/cors-allow-origin: "https://app.ryvyl.com,https://admin.ryvyl.com"
    nginx.ingress.kubernetes.io/cors-allow-methods: "GET, POST, PUT, DELETE, OPTIONS"
    nginx.ingress.kubernetes.io/cors-allow-headers: "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization"
    nginx.ingress.kubernetes.io/cors-expose-headers: "Content-Length,Content-Range"
    nginx.ingress.kubernetes.io/cors-max-age: "86400"
    
    # Let's Encrypt / cert-manager annotations
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    cert-manager.io/acme-challenge-type: "http01"
    
    # AWS ALB Ingress Controller annotations (alternative)
    # kubernetes.io/ingress.class: "alb"
    # alb.ingress.kubernetes.io/scheme: "internet-facing"
    # alb.ingress.kubernetes.io/target-type: "ip"
    # alb.ingress.kubernetes.io/healthcheck-path: "/api/health"
    # alb.ingress.kubernetes.io/healthcheck-interval-seconds: "30"
    # alb.ingress.kubernetes.io/healthcheck-timeout-seconds: "10"
    # alb.ingress.kubernetes.io/healthy-threshold-count: "2"
    # alb.ingress.kubernetes.io/unhealthy-threshold-count: "3"
    # alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}, {"HTTPS": 443}]'
    # alb.ingress.kubernetes.io/ssl-redirect: "443"
    # alb.ingress.kubernetes.io/certificate-arn: "arn:aws:acm:region:account:certificate/cert-id"
spec:
  tls:
  - hosts:
    - api.ryvyl.com
    - admin-api.ryvyl.com
    secretName: ryvyl-backend-tls
  rules:
  - host: api.ryvyl.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: ryvyl-backend-service
            port:
              number: 80
  - host: admin-api.ryvyl.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: ryvyl-backend-service
            port:
              number: 80

---
# Alternative Ingress for staging environment
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ryvyl-backend-staging-ingress
  namespace: ryvyl-backend
  labels:
    app: ryvyl-backend
    environment: staging
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/auth-type: "basic"
    nginx.ingress.kubernetes.io/auth-secret: "basic-auth"
    nginx.ingress.kubernetes.io/auth-realm: "Authentication Required - Staging Environment"
    cert-manager.io/cluster-issuer: "letsencrypt-staging"
spec:
  tls:
  - hosts:
    - staging-api.ryvyl.com
    secretName: ryvyl-backend-staging-tls
  rules:
  - host: staging-api.ryvyl.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: ryvyl-backend-service
            port:
              number: 80

---
# Traefik Ingress (alternative ingress controller)
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ryvyl-backend-traefik-ingress
  namespace: ryvyl-backend
  labels:
    app: ryvyl-backend
  annotations:
    kubernetes.io/ingress.class: "traefik"
    traefik.ingress.kubernetes.io/router.entrypoints: "websecure"
    traefik.ingress.kubernetes.io/router.tls: "true"
    traefik.ingress.kubernetes.io/router.tls.certresolver: "letsencrypt"
    traefik.ingress.kubernetes.io/router.middlewares: "ryvyl-backend-security@kubernetescrd"
spec:
  tls:
  - hosts:
    - api.ryvyl.com
    secretName: ryvyl-backend-traefik-tls
  rules:
  - host: api.ryvyl.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: ryvyl-backend-service
            port:
              number: 80

---
# Traefik Middleware for security headers
apiVersion: traefik.containo.us/v1alpha1
kind: Middleware
metadata:
  name: security-headers
  namespace: ryvyl-backend
spec:
  headers:
    customRequestHeaders:
      X-Forwarded-Proto: "https"
    customResponseHeaders:
      X-Frame-Options: "SAMEORIGIN"
      X-Content-Type-Options: "nosniff"
      X-XSS-Protection: "1; mode=block"
      Referrer-Policy: "strict-origin-when-cross-origin"
      Permissions-Policy: "geolocation=(), microphone=(), camera=()"
    contentSecurityPolicy: "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self'; frame-ancestors 'none';"

---
# Rate limiting middleware for Traefik
apiVersion: traefik.containo.us/v1alpha1
kind: Middleware
metadata:
  name: rate-limit
  namespace: ryvyl-backend
spec:
  rateLimit:
    burst: 100
    average: 50
    period: "1m"
