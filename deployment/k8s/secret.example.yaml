# Kubernetes Secrets Example
# DO NOT commit this file with real values to version control
# Use this as a template and create actual secrets with kube<PERSON>l or your CI/CD pipeline

apiVersion: v1
kind: Secret
metadata:
  name: ryvyl-secrets
  namespace: ryvyl-backend
  labels:
    app: ryvyl-backend
type: Opaque
data:
  # All values must be base64 encoded
  # Use: echo -n "your-secret-value" | base64
  
  # JWT Secret (minimum 32 characters)
  # Example: echo -n "your-super-secure-jwt-secret-key-here-32-chars-min" | base64
  jwt-secret: eW91ci1zdXBlci1zZWN1cmUtand0LXNlY3JldC1rZXktaGVyZS0zMi1jaGFycy1taW4=
  
  # Database URL
  # Example: echo -n "**************************************************************************" | base64
  database-url: ****************************************************************************************************
  
  # Email Configuration
  # Example: echo -n "<EMAIL>" | base64
  email-user: ********************************
  # Example: echo -n "your-email-password" | base64
  email-pass: eW91ci1lbWFpbC1wYXNzd29yZA==
  
  # Postmark API Key
  # Example: echo -n "************************************" | base64
  postmark-api-key: ************************************************
  
  # MongoDB Root Password
  # Example: echo -n "your-mongo-root-password" | base64
  mongo-root-password: eW91ci1tb25nby1yb290LXBhc3N3b3Jk
  
  # MongoDB Application Password
  # Example: echo -n "your-mongo-app-password" | base64
  mongo-password: eW91ci1tb25nby1hcHAtcGFzc3dvcmQ=
  
  # Redis Password
  # Example: echo -n "your-redis-password" | base64
  redis-password: eW91ci1yZWRpcy1wYXNzd29yZA==
  
  # Session Secret
  # Example: echo -n "your-session-secret-key" | base64
  session-secret: eW91ci1zZXNzaW9uLXNlY3JldC1rZXk=
  
  # Twilio Credentials (if using SMS)
  # Example: echo -n "ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxx" | base64
  twilio-account-sid: QUN4eHh4eHh4eHh4eHh4eHh4eHh4eHh4eHh4eHh4eHh4eHh4
  # Example: echo -n "your-twilio-auth-token" | base64
  twilio-auth-token: eW91ci10d2lsaW8tYXV0aC10b2tlbg==

---
# TLS Secret for HTTPS
apiVersion: v1
kind: Secret
metadata:
  name: ryvyl-backend-tls
  namespace: ryvyl-backend
  labels:
    app: ryvyl-backend
type: kubernetes.io/tls
data:
  # Base64 encoded TLS certificate
  # Example: cat your-cert.crt | base64 -w 0
  tls.crt: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0t...
  
  # Base64 encoded TLS private key
  # Example: cat your-key.key | base64 -w 0
  tls.key: LS0tLS1CRUdJTiBQUklWQVRFIEtFWS0tLS0t...

---
# Docker Registry Secret (if using private registry)
apiVersion: v1
kind: Secret
metadata:
  name: registry-secret
  namespace: ryvyl-backend
  labels:
    app: ryvyl-backend
type: kubernetes.io/dockerconfigjson
data:
  # Docker registry authentication
  # Example: kubectl create secret docker-registry registry-secret \
  #   --docker-server=your-registry.com \
  #   --docker-username=your-username \
  #   --docker-password=your-password \
  #   --docker-email=<EMAIL> \
  #   --dry-run=client -o yaml | grep dockerconfigjson
  .dockerconfigjson: ********************************************************************************************************************************************************************************************************************************

---
# Basic Auth Secret for staging environment
apiVersion: v1
kind: Secret
metadata:
  name: basic-auth
  namespace: ryvyl-backend
  labels:
    app: ryvyl-backend
    environment: staging
type: Opaque
data:
  # Basic auth credentials for staging
  # Example: htpasswd -c auth admin
  # Then: cat auth | base64
  auth: YWRtaW46JGFwcjEkSDZ1c2k2M0kkWFMwNzJZVXU4YkJYZ3V5R0pBZUxjLgoK

---
# External Secrets Operator Example (if using external secret management)
apiVersion: external-secrets.io/v1beta1
kind: SecretStore
metadata:
  name: vault-backend
  namespace: ryvyl-backend
spec:
  provider:
    vault:
      server: "https://vault.example.com"
      path: "secret"
      version: "v2"
      auth:
        kubernetes:
          mountPath: "kubernetes"
          role: "ryvyl-backend"

---
apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: ryvyl-external-secrets
  namespace: ryvyl-backend
spec:
  refreshInterval: 1h
  secretStoreRef:
    name: vault-backend
    kind: SecretStore
  target:
    name: ryvyl-secrets
    creationPolicy: Owner
  data:
  - secretKey: jwt-secret
    remoteRef:
      key: ryvyl/backend
      property: jwt_secret
  - secretKey: database-url
    remoteRef:
      key: ryvyl/backend
      property: database_url
  - secretKey: postmark-api-key
    remoteRef:
      key: ryvyl/backend
      property: postmark_api_key

# Commands to create secrets manually:
#
# 1. Create namespace:
# kubectl create namespace ryvyl-backend
#
# 2. Create secrets from command line:
# kubectl create secret generic ryvyl-secrets \
#   --from-literal=jwt-secret="your-jwt-secret" \
#   --from-literal=database-url="*************************************" \
#   --from-literal=email-user="<EMAIL>" \
#   --from-literal=email-pass="your-email-password" \
#   --from-literal=postmark-api-key="your-postmark-key" \
#   -n ryvyl-backend
#
# 3. Create TLS secret:
# kubectl create secret tls ryvyl-backend-tls \
#   --cert=path/to/tls.crt \
#   --key=path/to/tls.key \
#   -n ryvyl-backend
#
# 4. Create registry secret:
# kubectl create secret docker-registry registry-secret \
#   --docker-server=your-registry.com \
#   --docker-username=your-username \
#   --docker-password=your-password \
#   --docker-email=<EMAIL> \
#   -n ryvyl-backend
