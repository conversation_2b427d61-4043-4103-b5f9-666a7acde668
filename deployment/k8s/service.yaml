apiVersion: v1
kind: Service
metadata:
  name: ryvyl-backend-service
  namespace: ryvyl-backend
  labels:
    app: ryvyl-backend
  annotations:
    service.beta.kubernetes.io/aws-load-balancer-type: "nlb"
    service.beta.kubernetes.io/aws-load-balancer-backend-protocol: "http"
    service.beta.kubernetes.io/aws-load-balancer-healthcheck-path: "/api/health"
    service.beta.kubernetes.io/aws-load-balancer-healthcheck-interval: "30"
    service.beta.kubernetes.io/aws-load-balancer-healthcheck-timeout: "10"
    service.beta.kubernetes.io/aws-load-balancer-healthy-threshold: "2"
    service.beta.kubernetes.io/aws-load-balancer-unhealthy-threshold: "3"
spec:
  type: ClusterIP
  ports:
  - name: http
    port: 80
    targetPort: http
    protocol: TCP
  selector:
    app: ryvyl-backend

---
apiVersion: v1
kind: Service
metadata:
  name: ryvyl-backend-headless
  namespace: ryvyl-backend
  labels:
    app: ryvyl-backend
spec:
  type: ClusterIP
  clusterIP: None
  ports:
  - name: http
    port: 3000
    targetPort: http
    protocol: TCP
  selector:
    app: ryvyl-backend

---
# MongoDB Service
apiVersion: v1
kind: Service
metadata:
  name: mongodb-service
  namespace: ryvyl-backend
  labels:
    app: mongodb
spec:
  type: ClusterIP
  ports:
  - name: mongodb
    port: 27017
    targetPort: 27017
    protocol: TCP
  selector:
    app: mongodb

---
# Redis Service
apiVersion: v1
kind: Service
metadata:
  name: redis-service
  namespace: ryvyl-backend
  labels:
    app: redis
spec:
  type: ClusterIP
  ports:
  - name: redis
    port: 6379
    targetPort: 6379
    protocol: TCP
  selector:
    app: redis

---
# External LoadBalancer Service (for cloud providers)
apiVersion: v1
kind: Service
metadata:
  name: ryvyl-backend-lb
  namespace: ryvyl-backend
  labels:
    app: ryvyl-backend
  annotations:
    # AWS Load Balancer annotations
    service.beta.kubernetes.io/aws-load-balancer-type: "external"
    service.beta.kubernetes.io/aws-load-balancer-nlb-target-type: "ip"
    service.beta.kubernetes.io/aws-load-balancer-scheme: "internet-facing"
    service.beta.kubernetes.io/aws-load-balancer-healthcheck-path: "/api/health"
    service.beta.kubernetes.io/aws-load-balancer-healthcheck-interval: "30"
    service.beta.kubernetes.io/aws-load-balancer-healthcheck-timeout: "10"
    service.beta.kubernetes.io/aws-load-balancer-healthy-threshold: "2"
    service.beta.kubernetes.io/aws-load-balancer-unhealthy-threshold: "3"
    
    # GCP Load Balancer annotations
    cloud.google.com/neg: '{"ingress": true}'
    cloud.google.com/backend-config: '{"default": "ryvyl-backend-config"}'
    
    # Azure Load Balancer annotations
    service.beta.kubernetes.io/azure-load-balancer-health-probe-request-path: "/api/health"
spec:
  type: LoadBalancer
  ports:
  - name: http
    port: 80
    targetPort: http
    protocol: TCP
  - name: https
    port: 443
    targetPort: http
    protocol: TCP
  selector:
    app: ryvyl-backend
  sessionAffinity: None
  externalTrafficPolicy: Local

---
# NodePort Service (for on-premises or testing)
apiVersion: v1
kind: Service
metadata:
  name: ryvyl-backend-nodeport
  namespace: ryvyl-backend
  labels:
    app: ryvyl-backend
spec:
  type: NodePort
  ports:
  - name: http
    port: 80
    targetPort: http
    nodePort: 30080
    protocol: TCP
  selector:
    app: ryvyl-backend
