apiVersion: apps/v1
kind: Deployment
metadata:
  name: ryvyl-backend
  namespace: ryvyl-backend
  labels:
    app: ryvyl-backend
    version: v1.0.0
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: ryvyl-backend
  template:
    metadata:
      labels:
        app: ryvyl-backend
        version: v1.0.0
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "3000"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: ryvyl-backend
      securityContext:
        runAsNonRoot: true
        runAsUser: 1001
        runAsGroup: 1001
        fsGroup: 1001
      containers:
      - name: ryvyl-backend
        image: your-registry/ryvyl-backend:latest
        imagePullPolicy: Always
        ports:
        - name: http
          containerPort: 3000
          protocol: TCP
        env:
        - name: NODE_ENV
          value: "production"
        - name: PORT
          value: "3000"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: ryvyl-secrets
              key: database-url
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: ryvyl-secrets
              key: jwt-secret
        - name: EMAIL_USER
          valueFrom:
            secretKeyRef:
              name: ryvyl-secrets
              key: email-user
        - name: EMAIL_PASS
          valueFrom:
            secretKeyRef:
              name: ryvyl-secrets
              key: email-pass
        - name: POSTMARK_API_KEY
          valueFrom:
            secretKeyRef:
              name: ryvyl-secrets
              key: postmark-api-key
        - name: REDIS_URL
          valueFrom:
            configMapKeyRef:
              name: ryvyl-config
              key: redis-url
        - name: CORS_ORIGINS
          valueFrom:
            configMapKeyRef:
              name: ryvyl-config
              key: cors-origins
        - name: RATE_LIMIT_WINDOW
          valueFrom:
            configMapKeyRef:
              name: ryvyl-config
              key: rate-limit-window
        - name: RATE_LIMIT_MAX
          valueFrom:
            configMapKeyRef:
              name: ryvyl-config
              key: rate-limit-max
        - name: LOG_LEVEL
          valueFrom:
            configMapKeyRef:
              name: ryvyl-config
              key: log-level
        - name: UPLOAD_MAX_SIZE
          valueFrom:
            configMapKeyRef:
              name: ryvyl-config
              key: upload-max-size
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /api/health
            port: http
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /api/health
            port: http
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        startupProbe:
          httpGet:
            path: /api/health
            port: http
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 10
        volumeMounts:
        - name: uploads
          mountPath: /app/uploads
        - name: logs
          mountPath: /app/logs
        - name: tmp
          mountPath: /tmp
      volumes:
      - name: uploads
        persistentVolumeClaim:
          claimName: ryvyl-uploads-pvc
      - name: logs
        persistentVolumeClaim:
          claimName: ryvyl-logs-pvc
      - name: tmp
        emptyDir: {}
      imagePullSecrets:
      - name: registry-secret
      nodeSelector:
        kubernetes.io/os: linux
      tolerations:
      - key: "node.kubernetes.io/not-ready"
        operator: "Exists"
        effect: "NoExecute"
        tolerationSeconds: 300
      - key: "node.kubernetes.io/unreachable"
        operator: "Exists"
        effect: "NoExecute"
        tolerationSeconds: 300

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: ryvyl-backend
  namespace: ryvyl-backend
  labels:
    app: ryvyl-backend

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: ryvyl-uploads-pvc
  namespace: ryvyl-backend
  labels:
    app: ryvyl-backend
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 10Gi
  storageClassName: fast-ssd

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: ryvyl-logs-pvc
  namespace: ryvyl-backend
  labels:
    app: ryvyl-backend
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 5Gi
  storageClassName: fast-ssd

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: ryvyl-backend-hpa
  namespace: ryvyl-backend
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: ryvyl-backend
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
