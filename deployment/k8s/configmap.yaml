apiVersion: v1
kind: ConfigMap
metadata:
  name: ryvyl-config
  namespace: ryvyl-backend
  labels:
    app: ryvyl-backend
data:
  # Application Configuration
  node-env: "production"
  port: "3000"
  
  # Redis Configuration
  redis-url: "redis://redis-service:6379"
  
  # Security Configuration
  cors-origins: "https://app.ryvyl.com,https://admin.ryvyl.com"
  rate-limit-window: "900000"
  rate-limit-max: "100"
  
  # File Upload Configuration
  upload-max-size: "5242880"
  upload-allowed-types: "image/jpeg,image/png,image/jpg,application/pdf"
  upload-storage-path: "/app/uploads"
  
  # Logging Configuration
  log-level: "info"
  log-format: "combined"
  enable-request-logging: "true"
  
  # Database Configuration
  db-max-pool-size: "10"
  db-connect-timeout: "30000"
  db-socket-timeout: "0"
  
  # External Services
  card-webhook: "https://api.example.com/webhook"
  
  # Monitoring Configuration
  metrics-enabled: "true"
  metrics-port: "9090"
  metrics-path: "/metrics"

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: nginx-config
  namespace: ryvyl-backend
  labels:
    app: nginx
data:
  nginx.conf: |
    user nginx;
    worker_processes auto;
    error_log /var/log/nginx/error.log warn;
    pid /var/run/nginx.pid;
    
    events {
        worker_connections 1024;
        use epoll;
        multi_accept on;
    }
    
    http {
        include /etc/nginx/mime.types;
        default_type application/octet-stream;
        
        # Logging
        log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                        '$status $body_bytes_sent "$http_referer" '
                        '"$http_user_agent" "$http_x_forwarded_for"';
        access_log /var/log/nginx/access.log main;
        
        # Performance
        sendfile on;
        tcp_nopush on;
        tcp_nodelay on;
        keepalive_timeout 65;
        types_hash_max_size 2048;
        client_max_body_size 10M;
        
        # Gzip compression
        gzip on;
        gzip_vary on;
        gzip_min_length 1024;
        gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
        
        # Security headers
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header Referrer-Policy "strict-origin-when-cross-origin" always;
        
        # Rate limiting
        limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
        limit_req_zone $binary_remote_addr zone=login:10m rate=1r/s;
        
        # Upstream backend
        upstream backend {
            least_conn;
            server ryvyl-backend-service:80 max_fails=3 fail_timeout=30s;
            keepalive 32;
        }
        
        # HTTP server (redirect to HTTPS)
        server {
            listen 80;
            server_name _;
            return 301 https://$host$request_uri;
        }
        
        # HTTPS server
        server {
            listen 443 ssl http2;
            server_name api.ryvyl.com admin-api.ryvyl.com;
            
            # SSL configuration
            ssl_certificate /etc/nginx/ssl/tls.crt;
            ssl_certificate_key /etc/nginx/ssl/tls.key;
            ssl_protocols TLSv1.2 TLSv1.3;
            ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
            ssl_prefer_server_ciphers off;
            ssl_session_cache shared:SSL:10m;
            ssl_session_timeout 10m;
            
            # Security headers
            add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
            add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self'; frame-ancestors 'none';" always;
            
            # Health check endpoint
            location /health {
                access_log off;
                return 200 "healthy\n";
                add_header Content-Type text/plain;
            }
            
            # API endpoints with rate limiting
            location /api/auth/login {
                limit_req zone=login burst=5 nodelay;
                proxy_pass http://backend;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
            }
            
            location /api/ {
                limit_req zone=api burst=20 nodelay;
                proxy_pass http://backend;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_connect_timeout 30s;
                proxy_send_timeout 30s;
                proxy_read_timeout 30s;
            }
            
            # Default location
            location / {
                proxy_pass http://backend;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
            }
        }
    }

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: mongodb-config
  namespace: ryvyl-backend
  labels:
    app: mongodb
data:
  mongod.conf: |
    storage:
      dbPath: /data/db
      journal:
        enabled: true
    systemLog:
      destination: file
      logAppend: true
      path: /var/log/mongodb/mongod.log
    net:
      port: 27017
      bindIp: 0.0.0.0
    processManagement:
      timeZoneInfo: /usr/share/zoneinfo
    security:
      authorization: enabled
    replication:
      replSetName: rs0

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: redis-config
  namespace: ryvyl-backend
  labels:
    app: redis
data:
  redis.conf: |
    # Network
    bind 0.0.0.0
    port 6379
    
    # General
    daemonize no
    supervised no
    pidfile /var/run/redis_6379.pid
    loglevel notice
    logfile ""
    
    # Persistence
    save 900 1
    save 300 10
    save 60 10000
    stop-writes-on-bgsave-error yes
    rdbcompression yes
    rdbchecksum yes
    dbfilename dump.rdb
    dir /data
    
    # Append only file
    appendonly yes
    appendfilename "appendonly.aof"
    appendfsync everysec
    no-appendfsync-on-rewrite no
    auto-aof-rewrite-percentage 100
    auto-aof-rewrite-min-size 64mb
    
    # Security
    requirepass changeme
    
    # Memory management
    maxmemory 256mb
    maxmemory-policy allkeys-lru
    
    # Slow log
    slowlog-log-slower-than 10000
    slowlog-max-len 128
