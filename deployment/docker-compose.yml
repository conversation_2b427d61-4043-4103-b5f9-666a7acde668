version: '3.8'

services:
  # Ryvyl Backend Application
  app:
    build:
      context: ..
      dockerfile: deployment/Dockerfile
    image: ryvyl-backend:latest
    container_name: ryvyl-backend
    restart: unless-stopped
    depends_on:
      mongo:
        condition: service_healthy
      redis:
        condition: service_healthy
    environment:
      NODE_ENV: production
      PORT: 3000
      DATABASE_URL: mongodb://ryvyl_user:${MONGO_PASSWORD}@mongo:27017/ryvyl?authSource=ryvyl
      JWT_SECRET: ${JWT_SECRET}
      EMAIL_USER: ${EMAIL_USER}
      EMAIL_PASS: ${EMAIL_PASS}
      POSTMARK_API_KEY: ${POSTMARK_API_KEY}
      REDIS_URL: redis://redis:6379
      CORS_ORIGINS: ${CORS_ORIGINS:-*}
      RATE_LIMIT_WINDOW: 900000
      RATE_LIMIT_MAX: 100
      LOG_LEVEL: info
    volumes:
      - uploads:/app/uploads
      - logs:/app/logs
    networks:
      - ryvyl-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      replicas: 2
      resources:
        limits:
          cpus: '1.0'
          memory: 512M
        reservations:
          cpus: '0.5'
          memory: 256M

  # MongoDB Database
  mongo:
    image: mongo:6.0
    container_name: ryvyl-mongo
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_ROOT_PASSWORD}
      MONGO_INITDB_DATABASE: ryvyl
    volumes:
      - mongo-data:/data/db
      - mongo-config:/data/configdb
      - ./mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    networks:
      - ryvyl-network
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: ryvyl-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis-data:/data
    networks:
      - ryvyl-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 256M
        reservations:
          cpus: '0.25'
          memory: 128M

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: ryvyl-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ssl-certs:/etc/nginx/ssl:ro
      - nginx-logs:/var/log/nginx
    depends_on:
      - app
    networks:
      - ryvyl-network
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 128M
        reservations:
          cpus: '0.25'
          memory: 64M

  # MongoDB Express (Development/Staging only)
  mongo-express:
    image: mongo-express:latest
    container_name: ryvyl-mongo-express
    restart: unless-stopped
    profiles:
      - dev
      - staging
    ports:
      - "8081:8081"
    environment:
      ME_CONFIG_MONGODB_ADMINUSERNAME: admin
      ME_CONFIG_MONGODB_ADMINPASSWORD: ${MONGO_ROOT_PASSWORD}
      ME_CONFIG_MONGODB_URL: mongodb://admin:${MONGO_ROOT_PASSWORD}@mongo:27017/
      ME_CONFIG_BASICAUTH_USERNAME: admin
      ME_CONFIG_BASICAUTH_PASSWORD: ${MONGO_EXPRESS_PASSWORD}
    depends_on:
      mongo:
        condition: service_healthy
    networks:
      - ryvyl-network

  # Redis Commander (Development/Staging only)
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: ryvyl-redis-commander
    restart: unless-stopped
    profiles:
      - dev
      - staging
    ports:
      - "8082:8081"
    environment:
      REDIS_HOSTS: local:redis:6379:0:${REDIS_PASSWORD}
      HTTP_USER: admin
      HTTP_PASSWORD: ${REDIS_COMMANDER_PASSWORD}
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - ryvyl-network

volumes:
  mongo-data:
    driver: local
  mongo-config:
    driver: local
  redis-data:
    driver: local
  uploads:
    driver: local
  logs:
    driver: local
  ssl-certs:
    driver: local
  nginx-logs:
    driver: local

networks:
  ryvyl-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
