const VALIDATION_RULES = require("./validation-constants")

// Validation utility functions
const isValidEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
}

const isValidPhone = (phone) => {
    // Only digits, no + prefix, max 16 digits
    const phoneRegex = /^[0-9]{1,16}$/
    return phoneRegex.test(phone)
}

const isValidDate = (dateString) => {
    if (!dateString) return false

    // Check for DD MMM YYYY format
    const dateRegex = /^(0[1-9]|[12][0-9]|3[01]) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) \d{4}$/
    const altDateRegex = /^\d{2}-[A-Za-z]{3}-\d{4}$/ // For 23-Nov-2024 format

    if (!dateRegex.test(dateString) && !altDateRegex.test(dateString)) {
        try {
            // Try parsing as a standard date
            const date = new Date(dateString)
            return !isNaN(date.getTime())
        } catch (e) {
            return false
        }
    }

    return true
}

const isValidApplicationDate = (dateString) => {
    if (!dateString) return false

    // Check for DD MMM YYYY hh:mm:ss format
    const dateRegex =
        /^(0[1-9]|[12][0-9]|3[01])-(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)-\d{4} \d{2} \d{2}:\d{2}:\d{2}$/

    if (!dateRegex.test(dateString)) {
        try {
            // Try parsing as a standard date
            const date = new Date(dateString)
            return !isNaN(date.getTime())
        } catch (e) {
            return false
        }
    }

    return true
}

const isValidObjectId = (id) => {
    return /^[0-9a-fA-F]{24}$/.test(id)
}

const isNotEmpty = (value) => {
    if (value === null || value === undefined) return false
    if (typeof value === "string") return value.trim().length > 0
    return true
}

const isValidLength = (value, maxLength) => {
    if (!value) return true // Empty values are handled by isNotEmpty
    if (typeof value !== "string") return false
    return value.length <= maxLength
}

const isValidClientCode = (code) => {
    // Format RYVL-xxxxxxxx, Max length 15
    const codeRegex = /^RYVL-[a-zA-Z0-9]{1,8}$/
    return codeRegex.test(code) && code.length <= VALIDATION_RULES.MAX_LENGTHS.clientCode
}

const isValidRiskLevel = (level) => {
    if (typeof level !== "number") {
        try {
            level = Number.parseInt(level)
        } catch (e) {
            return false
        }
    }
    return level >= 0 && level <= 999
}

const isValidRiskStatus = (status) => {
    return VALIDATION_RULES.RISK_STATUS_VALUES.includes(status)
}

const isValidApplicationStatus = (status) => {
    return VALIDATION_RULES.APPLICATION_STATUS_VALUES.includes(status)
}

const isValidIdDocumentType = (type) => {
    return VALIDATION_RULES.ID_DOCUMENT_TYPES.includes(type)
}

const isValidCountryCode = (country) => {
    // This is a simplified check - in a real application, you would validate against a list of ISO3166 country codes
    return typeof country === "string" && country.length === 3
}

const isValidTaxCountry = (country) => {
    // This is a simplified check - in a real application, you would validate against a list of ISO3166 country codes
    return typeof country === "string" && country.length <= 2
}

module.exports = {
    isValidEmail,
    isValidPhone,
    isValidDate,
    isValidApplicationDate,
    isValidObjectId,
    isNotEmpty,
    isValidLength,
    isValidClientCode,
    isValidRiskLevel,
    isValidRiskStatus,
    isValidApplicationStatus,
    isValidIdDocumentType,
    isValidCountryCode,
    isValidTaxCountry,
}

