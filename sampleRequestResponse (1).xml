VerifyRegReq:

<?xml version='1.0' encoding='UTF-8'?>
<S:Envelope xmlns:S="http://www.w3.org/2003/05/soap-envelope"a>
    <S:Body>
        <ns2:verifyRegReq xmlns:ns2="http://www.gpayments.com/caas/">
            <ns2:card>
                <id></id>
                <number>*************</number>
                <type>VbV</type>
                <context_Blob></context_Blob>
            </ns2:card>
            <ns2:transaction>
                <purchaseAmount>10000</purchaseAmount>
                <purchaseExponent>2</purchaseExponent>
                <purchaseCurrency>036</purchaseCurrency>
                <purchaseDate>2023-06-27T11:26:38.000Z</purchaseDate>
                <merchantId>123456789012345</merchantId>
                <merchantName>Test Merchant</merchantName>
                <merchantCountry>840</merchantCountry>
                <acqBin>41234567890</acqBin>
                <theeDSProtocolVersion>2.1.0</theeDSProtocolVersion>
                <cardExpiry>2508</cardExpiry>
                <issuerName>remote</issuerName>
                <acsTransId>36e6c66a-3dc6-4a59-a722-96c718824981</acsTransId>
                <threeDSTransId>e6e9bf9b-af70-4f20-ad3a-52ce69de1c15</threeDSTransId>
                <dsTransId>2d5e6910-4d86-4d84-b6a3-9125b80b9ba1</dsTransId>
                <threeDSRequestorID>123456789.visa</threeDSRequestorID>
                <threeDSRequestorName>3dsclient.local.visa</threeDSRequestorName>
                <threeDSServerRefNumber>3DS_LOA_SER_GPPL_020100_00075</threeDSServerRefNumber>
                <threeDSServerOperatorID>1jpeeLAWgGFgS1Ri9tX9</threeDSServerOperatorID>
                <threeDSRequestorURL>http://gpayments.com</threeDSRequestorURL>
                <threeDSServerURL>https://amir-test3.testlab.3dsecure.cloud:9605/api/v2/ds/result/request</threeDSServerURL>
                <deviceChannel>02</deviceChannel>
                <dsReferenceNumber>3DS_LOA_ACS_GPPL_020200_00442</dsReferenceNumber>
                <payTokenInd>0</payTokenInd>
                <mcc>2020</mcc>
                <messageCategory>01</messageCategory>
                <transType>01</transType>
                <acctType>03</acctType>
                <threeDSRequestorAuthenticationInd>01</threeDSRequestorAuthenticationInd>
            </ns2:transaction>
            <headerParams>
                <param key="browserJavaEnabled" cookie="false">false</param>
                <param key="browserTZ" cookie="false">-180</param>
                <param key="browserLanguage" cookie="false">en-US</param>
                <param key="Accept-Language" cookie="false">en-US</param>
                <param key="User-Agent" cookie="false">Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</param>
                <param key="Accept" cookie="false">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</param>
                <param key="proxy-ip" cookie="false">************</param>
                <param key="browserColorDepth" cookie="false">24</param>
                <param key="browserScreenHeight" cookie="false">1050</param>
                <param key="browserScreenWidth" cookie="false">1680</param>
            </headerParams>
            <extensionParams/>
            <additionalParams>
                <param key="shipAddrState">NSW</param>
                <param key="shipAddrCity">Sydney</param>
                <param key="shipAddrCountry">036</param>
                <param key="shipAddrLine1">Unit 1</param>
                <param key="shipAddrLine2">123 Street</param>
                <param key="shipAddrPostCode">2000</param>
                <param key="billAddrCity">Sydney</param>
                <param key="billAddrCountry">036</param>
                <param key="billAddrLine1">Unit 1</param>
                <param key="billAddrLine2">123 Street</param>
                <param key="billAddrState">NSW</param>
                <param key="billAddrPostCode">2000</param>
                <param key="threeDSCompInd">U</param>
                <param key="threeDSRequestorAuthenticationInd">01</param>
                <param key="threeDSRequestorChallengeInd">01</param>
                <param key="addrMatch">Y</param>
                <param key="cardExpiryDate">2508</param>
                <param key="acctID">personal account</param>
                <param key="email"><EMAIL></param>
                <param key="mobilePhone.cc">61</param>
                <param key="mobilePhone.subscriber">**********</param>
            </additionalParams>
        </ns2:verifyRegReq>
    </S:Body>
</S:Envelope>

verifyRegResp:

<?xml version='1.0' encoding='UTF-8'?>
<S:Envelope xmlns:S="http://www.w3.org/2003/05/soap-envelope">
    <S:Body>
        <ns2:verifyRegResp xmlns:ns2="http://www.gpayments.com/caas/">
            <ns2:cardInfo>
                <card_ID>*************</card_ID>
                <context_Blob>595</context_Blob>
                <regStatus>2</regStatus>
                <authRequired>1</authRequired>
                <authTypeSup>1</authTypeSup>
                <authTypeSup>2</authTypeSup>
                <lanCode></lanCode>
                <twoFA>true</twoFA>
            </ns2:cardInfo>
            <code>1</code>
            <ns2:errorMessage>warning-default warning for default response code 1</ns2:errorMessage>
            <ns2:errorDetail>warning-default warning for default response code 1</ns2:errorDetail>
        </ns2:verifyRegResp>
    </S:Body>
</S:Envelope>

preAuthReq

<?xml version='1.0' encoding='UTF-8'?>
<S:Envelope xmlns:S="http://www.w3.org/2003/05/soap-envelope">
    <S:Body>
        <ns2:preAuthReq xmlns:ns2="http://www.gpayments.com/caas/">
            <ns2:card>
                <id>*************</id>
                <number>*************</number>
                <type>VbV</type>
                <context_Blob>595</context_Blob>
                <lanCode>0</lanCode>
            </ns2:card>
            <ns2:transaction>
                <purchaseAmount>10000</purchaseAmount>
                <purchaseExponent>2</purchaseExponent>
                <purchaseCurrency>036</purchaseCurrency>
                <purchaseDate>2023-06-28T08:20:17.000Z</purchaseDate>
                <merchantId>123456789012345</merchantId>
                <merchantName>Test Merchant</merchantName>
                <merchantCountry>840</merchantCountry>
                <acqBin>41234567890</acqBin>
                <theeDSProtocolVersion>2.1.0</theeDSProtocolVersion>
                <cardExpiry>2508</cardExpiry>
                <issuerName>remote</issuerName>
                <acsTransId>ef6a1709-acad-4943-b86b-95c6f1e019ee</acsTransId>
                <threeDSTransId>d92d6f98-8bf5-4238-9408-9a41aae1860d</threeDSTransId>
                <dsTransId>afc34d97-f65b-4f75-8e4b-5b102ae0c4ba</dsTransId>
                <threeDSRequestorID>123456789.visa</threeDSRequestorID>
                <threeDSRequestorName>3dsclient.local.visa</threeDSRequestorName>
                <threeDSServerRefNumber>3DS_LOA_SER_GPPL_020100_00075</threeDSServerRefNumber>
                <threeDSServerOperatorID>1jpeeLAWgGFgS1Ri9tX9</threeDSServerOperatorID>
                <threeDSRequestorURL>http://gpayments.com</threeDSRequestorURL>
                <threeDSServerURL>https://amir-test3.testlab.3dsecure.cloud:9605/api/v2/ds/result/request</threeDSServerURL>
                <deviceChannel>02</deviceChannel>
                <dsReferenceNumber>3DS_LOA_ACS_GPPL_020200_00442</dsReferenceNumber>
                <payTokenInd>0</payTokenInd>
                <mcc>2020</mcc>
                <messageCategory>01</messageCategory>
                <transType>01</transType>
                <acctType>03</acctType>
                <threeDSRequestorAuthenticationInd>01</threeDSRequestorAuthenticationInd>
            </ns2:transaction>
            <headerParams>
                <param key="browserJavaEnabled" cookie="false">false</param>
                <param key="browserTZ" cookie="false">-180</param>
                <param key="browserLanguage" cookie="false">en-US</param>
                <param key="Accept-Language" cookie="false">en-US</param>
                <param key="User-Agent" cookie="false">Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</param>
                <param key="Accept" cookie="false">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</param>
                <param key="proxy-ip" cookie="false">************</param>
                <param key="browserColorDepth" cookie="false">24</param>
                <param key="browserScreenHeight" cookie="false">1050</param>
                <param key="browserScreenWidth" cookie="false">1680</param>
            </headerParams>
            <additionalParams>
                <param key="shipAddrState">NSW</param>
                <param key="shipAddrCity">Sydney</param>
                <param key="shipAddrCountry">036</param>
                <param key="shipAddrLine1">Unit 1</param>
                <param key="shipAddrLine2">123 Street</param>
                <param key="shipAddrPostCode">2000</param>
                <param key="billAddrCity">Sydney</param>
                <param key="billAddrCountry">036</param>
                <param key="billAddrLine1">Unit 1</param>
                <param key="billAddrLine2">123 Street</param>
                <param key="billAddrState">NSW</param>
                <param key="billAddrPostCode">2000</param>
                <param key="threeDSCompInd">U</param>
                <param key="threeDSRequestorAuthenticationInd">01</param>
                <param key="threeDSRequestorChallengeInd">01</param>
                <param key="addrMatch">Y</param>
                <param key="cardExpiryDate">2508</param>
                <param key="acctID">personal account</param>
                <param key="email"><EMAIL></param>
                <param key="mobilePhone.cc">61</param>
                <param key="mobilePhone.subscriber">**********</param>
            </additionalParams>
        </ns2:preAuthReq>
    </S:Body>
</S:Envelope>

preAuthResp:

<?xml version='1.0' encoding='UTF-8'?>
<S:Envelope xmlns:S="http://www.w3.org/2003/05/soap-envelope">
    <S:Body>
        <ns2:preAuthResp xmlns:ns2="http://www.gpayments.com/caas/">
            <code>1</code>
            <ns2:errorMessage></ns2:errorMessage>
            <ns2:errorDetail></ns2:errorDetail>
        </ns2:preAuthResp>
    </S:Body>
</S:Envelope>



initAuthReq:

<?xml version='1.0' encoding='UTF-8'?>
<S:Envelope xmlns:S="http://www.w3.org/2003/05/soap-envelope">
    <S:Body>
        <ns2:initAuthReq xmlns:ns2="http://www.gpayments.com/caas/">
            <ns2:card>
                <id>*************</id>
                <number>*************</number>
                <type>VbV</type>
                <context_Blob>595</context_Blob>
                <lanCode>0</lanCode>
            </ns2:card>
            <ns2:transaction>
                <purchaseAmount>10000</purchaseAmount>
                <purchaseExponent>2</purchaseExponent>
                <purchaseCurrency>036</purchaseCurrency>
                <purchaseDate>2023-06-27T11:26:38.000Z</purchaseDate>
                <merchantId>123456789012345</merchantId>
                <merchantName>Test Merchant</merchantName>
                <merchantCountry>840</merchantCountry>
                <acqBin>41234567890</acqBin>
                <theeDSProtocolVersion>2.1.0</theeDSProtocolVersion>
                <cardExpiry>2508</cardExpiry>
                <issuerName>remote</issuerName>
                <acsTransId>36e6c66a-3dc6-4a59-a722-96c718824981</acsTransId>
                <threeDSTransId>e6e9bf9b-af70-4f20-ad3a-52ce69de1c15</threeDSTransId>
                <dsTransId>2d5e6910-4d86-4d84-b6a3-9125b80b9ba1</dsTransId>
                <threeDSRequestorID>123456789.visa</threeDSRequestorID>
                <threeDSRequestorName>3dsclient.local.visa</threeDSRequestorName>
                <threeDSServerRefNumber>3DS_LOA_SER_GPPL_020100_00075</threeDSServerRefNumber>
                <threeDSServerOperatorID>1jpeeLAWgGFgS1Ri9tX9</threeDSServerOperatorID>
                <threeDSRequestorURL>http://gpayments.com</threeDSRequestorURL>
                <threeDSServerURL>https://amir-test3.testlab.3dsecure.cloud:9605/api/v2/ds/result/request</threeDSServerURL>
                <deviceChannel>02</deviceChannel>
                <dsReferenceNumber>3DS_LOA_ACS_GPPL_020200_00442</dsReferenceNumber>
                <payTokenInd>0</payTokenInd>
                <mcc>2020</mcc>
                <messageCategory>01</messageCategory>
                <transType>01</transType>
                <acctType>03</acctType>
                <threeDSRequestorAuthenticationInd>01</threeDSRequestorAuthenticationInd>
            </ns2:transaction>
            <SMS>Your OTP is {0}</SMS>
            <authType>2</authType>
        </ns2:initAuthReq>
    </S:Body>
</S:Envelope>

initAuthResp:

<?xml version='1.0' encoding='UTF-8'?>
<S:Envelope xmlns:S="http://www.w3.org/2003/05/soap-envelope">
    <S:Body>
        <ns2:initAuthResp xmlns:ns2="http://www.gpayments.com/caas/">
            <authData>
                <data name="mobileNo" authType="2">
                    <value>+421XXXXX1234</value>
                </data>
            </authData>
            <code>0</code>
        </ns2:initAuthResp>
    </S:Body>
</S:Envelope>

VerifyAuthReq:

<?xml version='1.0' encoding='UTF-8'?>
<S:Envelope xmlns:S="http://www.w3.org/2003/05/soap-envelope">
    <S:Body>
        <ns2:verifyAuthReq xmlns:ns2="http://www.gpayments.com/caas/">
            <ns2:card>
                <id>*************</id>
                <number>*************</number>
                <type>VbV</type>
                <context_Blob>595</context_Blob>
                <lanCode>0</lanCode>
            </ns2:card>
            <token authType="1">100203045</token>
            <ns2:transaction>
                <purchaseAmount>10000</purchaseAmount>
                <purchaseExponent>2</purchaseExponent>
                <purchaseCurrency>036</purchaseCurrency>
                <purchaseDate>2023-06-27T11:26:38.000Z</purchaseDate>
                <merchantId>123456789012345</merchantId>
                <merchantName>Test Merchant</merchantName>
                <merchantCountry>840</merchantCountry>
                <acqBin>41234567890</acqBin>
                <theeDSProtocolVersion>2.1.0</theeDSProtocolVersion>
                <cardExpiry>2508</cardExpiry>
                <issuerName>remote</issuerName>
                <acsTransId>36e6c66a-3dc6-4a59-a722-96c718824981</acsTransId>
                <threeDSTransId>e6e9bf9b-af70-4f20-ad3a-52ce69de1c15</threeDSTransId>
                <dsTransId>2d5e6910-4d86-4d84-b6a3-9125b80b9ba1</dsTransId>
                <threeDSRequestorID>123456789.visa</threeDSRequestorID>
                <threeDSRequestorName>3dsclient.local.visa</threeDSRequestorName>
                <threeDSServerRefNumber>3DS_LOA_SER_GPPL_020100_00075</threeDSServerRefNumber>
                <threeDSServerOperatorID>1jpeeLAWgGFgS1Ri9tX9</threeDSServerOperatorID>
                <threeDSRequestorURL>http://gpayments.com</threeDSRequestorURL>
                <threeDSServerURL>https://amir-test3.testlab.3dsecure.cloud:9605/api/v2/ds/result/request</threeDSServerURL>
                <deviceChannel>02</deviceChannel>
                <dsReferenceNumber>3DS_LOA_ACS_GPPL_020200_00442</dsReferenceNumber>
                <payTokenInd>0</payTokenInd>
                <mcc>2020</mcc>
                <messageCategory>01</messageCategory>
                <transType>01</transType>
                <acctType>03</acctType>
                <threeDSRequestorAuthenticationInd>01</threeDSRequestorAuthenticationInd>
            </ns2:transaction>
        </ns2:verifyAuthReq>
    </S:Body>
</S:Envelope>

verifyAuthResp

<?xml version='1.0' encoding='UTF-8'?>
<S:Envelope xmlns:S="http://www.w3.org/2003/05/soap-envelope">
    <S:Body>
        <ns2:verifyAuthResp xmlns:ns2="http://www.gpayments.com/caas/">
            <code>0</code>
        </ns2:verifyAuthResp>
    </S:Body>
</S:Envelope>