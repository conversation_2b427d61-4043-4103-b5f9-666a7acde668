<?xml version="1.0" encoding="UTF-8"?>
<definitions
        xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
        xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/"
        xmlns:xsd="http://www.w3.org/2001/XMLSchema"
        xmlns:tns="http://www.gpayments.com/caas/"
        name="VerifyService"
        targetNamespace="http://www.gpayments.com/caas/"
        xmlns="http://schemas.xmlsoap.org/wsdl/"
>

    <!-- === Types === -->
    <types>
        <xsd:schema targetNamespace="http://www.gpayments.com/caas/">
            <xsd:element name="verifyRegReq">
                <xsd:complexType>
                    <xsd:sequence>
                        <xsd:element name="card">
                            <xsd:complexType>
                                <xsd:sequence>
                                    <xsd:element name="number" type="xsd:string"/>
                                </xsd:sequence>
                            </xsd:complexType>
                        </xsd:element>
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>

            <xsd:element name="verifyRegResp">
                <xsd:complexType>
                    <xsd:sequence>
                        <xsd:element name="cardInfo" minOccurs="0">
                            <xsd:complexType>
                                <xsd:sequence>
                                    <xsd:element name="card_ID" type="xsd:string"/>
                                    <xsd:element name="context_Blob" type="xsd:string"/>
                                    <xsd:element name="regStatus" type="xsd:int"/>
                                    <xsd:element name="authRequired" type="xsd:int"/>
                                    <xsd:element name="authTypeSup" type="xsd:int" maxOccurs="unbounded"/>
                                    <xsd:element name="lanCode" type="xsd:string"/>
                                    <xsd:element name="twoFA" type="xsd:boolean"/>
                                </xsd:sequence>
                            </xsd:complexType>
                        </xsd:element>
                        <xsd:element name="code" type="xsd:int"/>
                        <xsd:element name="errorMessage" type="xsd:string"/>
                        <xsd:element name="errorDetail" type="xsd:string"/>
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>

            <xsd:element name="preAuthReq" type="xsd:string"/>
            <xsd:element name="preAuthResp">
                <xsd:complexType>
                    <xsd:sequence>
                        <xsd:element name="code" type="xsd:int"/>
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>

            <xsd:element name="initAuthReq" type="xsd:string"/>
            <xsd:element name="initAuthResp">
                <xsd:complexType>
                    <xsd:sequence>
                        <xsd:element name="authData">
                            <xsd:complexType>
                                <xsd:sequence>
                                    <xsd:element name="data">
                                        <xsd:complexType>
                                            <xsd:sequence>
                                                <xsd:element name="name" type="xsd:string"/>
                                                <xsd:element name="authType" type="xsd:string"/>
                                                <xsd:element name="value" type="xsd:string"/>
                                            </xsd:sequence>
                                        </xsd:complexType>
                                    </xsd:element>
                                </xsd:sequence>
                            </xsd:complexType>
                        </xsd:element>
                        <xsd:element name="code" type="xsd:int"/>
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>

            <xsd:element name="verifyAuthReq">
                <xsd:complexType>
                    <xsd:sequence>
                        <xsd:element name="token" type="xsd:string"/>
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>

            <xsd:element name="verifyAuthResp">
                <xsd:complexType>
                    <xsd:sequence>
                        <xsd:element name="code" type="xsd:int"/>
                        <xsd:element name="errorMessage" type="xsd:string"/>
                        <xsd:element name="errorDetail" type="xsd:string"/>
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>
        </xsd:schema>
    </types>

    <!-- === Messages === -->
    <message name="VerifyRegRequest">
        <part name="verifyRegReq" element="tns:verifyRegReq"/>
    </message>
    <message name="VerifyRegResponse">
        <part name="verifyRegResp" element="tns:verifyRegResp"/>
    </message>

    <message name="PreAuthRequest">
        <part name="preAuthReq" element="tns:preAuthReq"/>
    </message>
    <message name="PreAuthResponse">
        <part name="preAuthResp" element="tns:preAuthResp"/>
    </message>

    <message name="InitAuthRequest">
        <part name="initAuthReq" element="tns:initAuthReq"/>
    </message>
    <message name="InitAuthResponse">
        <part name="initAuthResp" element="tns:initAuthResp"/>
    </message>

    <message name="VerifyAuthRequest">
        <part name="verifyAuthReq" element="tns:verifyAuthReq"/>
    </message>
    <message name="VerifyAuthResponse">
        <part name="verifyAuthResp" element="tns:verifyAuthResp"/>
    </message>

    <!-- === Port Type === -->
    <portType name="VerifyPortType">
        <operation name="verifyReg">
            <input message="tns:VerifyRegRequest"/>
            <output message="tns:VerifyRegResponse"/>
        </operation>
        <operation name="preAuth">
            <input message="tns:PreAuthRequest"/>
            <output message="tns:PreAuthResponse"/>
        </operation>
        <operation name="initAuth">
            <input message="tns:InitAuthRequest"/>
            <output message="tns:InitAuthResponse"/>
        </operation>
        <operation name="verifyAuth">
            <input message="tns:VerifyAuthRequest"/>
            <output message="tns:VerifyAuthResponse"/>
        </operation>
    </portType>

    <!-- === Binding (SOAP 1.2) === -->
    <binding name="VerifyBinding" type="tns:VerifyPortType">
        <soap12:binding style="document" transport="http://www.w3.org/2003/05/soap/bindings/HTTP/"/>

        <operation name="verifyReg">
            <soap12:operation soapAction="http://www.gpayments.com/caas/verifyReg"/>
            <input><soap12:body use="literal"/></input>
            <output><soap12:body use="literal"/></output>
        </operation>
        <operation name="preAuth">
            <soap12:operation soapAction="http://www.gpayments.com/caas/preAuth"/>
            <input><soap12:body use="literal"/></input>
            <output><soap12:body use="literal"/></output>
        </operation>
        <operation name="initAuth">
            <soap12:operation soapAction="http://www.gpayments.com/caas/initAuth"/>
            <input><soap12:body use="literal"/></input>
            <output><soap12:body use="literal"/></output>
        </operation>
        <operation name="verifyAuth">
            <soap12:operation soapAction="http://www.gpayments.com/caas/verifyAuth"/>
            <input><soap12:body use="literal"/></input>
            <output><soap12:body use="literal"/></output>
        </operation>
    </binding>

    <!-- === Service === -->
    <service name="VerifyService">
        <documentation>Ryvyl SOAP Verification Service</documentation>
        <port name="VerifyPort" binding="tns:VerifyBinding">
            <soap12:address location="http://localhost:3000/wsdl"/>
        </port>
    </service>
</definitions>
