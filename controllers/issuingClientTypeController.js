const IssuingClientType = require("../models/IssuingClientType");
const {createTask} = require("../config/EventHandler");
const User = require('../models/user');

// Create a new issuing client type
exports.createIssuingClientType = async (req, res) => {
    try {
        const { type, code,created_by } = req.body;

        if (!type || !code) {
            return res.status(400).json({ message: "Type and Code are required" });
        }
        const recordCount = await IssuingClientType.countDocuments();
        const x = recordCount + 1
        // Calculate the new version number
        const newVersion = x + `.0`;
        const newIssuingClientType = new IssuingClientType({
            type,
            code,created_by,version:newVersion
        });

        const p = await newIssuingClientType.save();
        const user = await User.findById(created_by);

        const taskData = {
            refId: p._id,
            type: 'Issuing Client Type',
            title: user.name + ' requested a new Issuing client Type "' +type+'"',
            date: new Date(),
            user: created_by,
            ipAddress: '************',
        };
        await createTask(taskData)
        res.status(201).json({ message: "Issuing Client Type created successfully", issuingClientType: newIssuingClientType });
    } catch (error) {
        res.status(500).json({ message: "Server error", error });
    }
};

// Fetch all issuing client types
exports.getIssuingClientTypes = async (req, res) => {
    try {
        const issuingClientTypes = await IssuingClientType.find({ deleted_at: null })
            .populate("created_by").sort({created_at: -1});
        res.status(200).json(issuingClientTypes);
    } catch (error) {
        res.status(500).json({ message: "Server error", error });
    }
};
