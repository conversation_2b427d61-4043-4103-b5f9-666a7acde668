const ZoneMethodConfig = require("../models/ZoneMethodConfig")
const Zone = require("../models/Zone")

// Controller for zone-method configuration operations
const zoneMethodConfigController = {
    // Get all configurations
    getAllConfigs: async (req, res, next) => {
        try {
            const configs = await ZoneMethodConfig.find({}).populate("zoneId", "name number countries deliveryMethods")
            res.json(configs)
        } catch (error) {
            next(error)
        }
    },

    // Get configurations for a specific zone
    getConfigsByZone: async (req, res, next) => {
        try {
            const { zoneId } = req.params
            const configs = await ZoneMethodConfig.find({ zoneId }).populate(
                "zoneId",
                "name number countries deliveryMethods",
            )
            res.json(configs)
        } catch (error) {
            next(error)
        }
    },

    // Get configurations for a specific country
    getConfigsByCountry: async (req, res, next) => {
        try {
            const { country } = req.params
            const configs = await ZoneMethodConfig.find({ country }).populate(
                "zoneId",
                "name number countries deliveryMethods",
            )
            res.json(configs)
        } catch (error) {
            next(error)
        }
    },

    // Get a specific configuration
    getConfigById: async (req, res, next) => {
        try {
            const config = await ZoneMethodConfig.findById(req.params.id).populate(
                "zoneId",
                "name number countries deliveryMethods",
            )

            if (!config) {
                return res.status(404).json({ error: "Configuration not found" })
            }

            res.json(config)
        } catch (error) {
            next(error)
        }
    },

    // Create a new configuration
    createConfig: async (req, res, next) => {
        try {
            const { zoneId, country, methodId } = req.body

            // Verify that zone exists
            const zone = await Zone.findById(zoneId)
            if (!zone) {
                return res.status(404).json({ error: "Zone not found" })
            }

            // Verify that country is valid for the zone
            if (!zone.countries.includes(country)) {
                return res.status(400).json({ error: "Country is not valid for the selected zone" })
            }

            // Verify that method is valid for the zone
            if (!zone.deliveryMethods.includes(methodId)) {
                return res.status(400).json({ error: "Delivery method is not valid for the selected zone" })
            }



            const newConfig = new ZoneMethodConfig(req.body)
            const savedConfig = await newConfig.save()

            const populatedConfig = await ZoneMethodConfig.findById(savedConfig._id).populate(
                "zoneId",
                "name number countries deliveryMethods",
            )

            res.status(201).json(populatedConfig)
        } catch (error) {
            if (error.name === "ValidationError") {
                return res.status(400).json({ error: error.message })
            }
            next(error)
        }
    },

    // Update a configuration
    updateConfig: async (req, res, next) => {
        try {
            const { zoneId, country, methodId } = req.body

            if (zoneId && country && methodId) {
                // Verify that zone exists
                const zone = await Zone.findById(zoneId)
                if (!zone) {
                    return res.status(404).json({ error: "Zone not found" })
                }

                // Verify that country is valid for the zone
                if (!zone.countries.includes(country)) {
                    return res.status(400).json({ error: "Country is not valid for the selected zone" })
                }

                // Verify that method is valid for the zone
                if (!zone.deliveryMethods.includes(methodId)) {
                    return res.status(400).json({ error: "Delivery method is not valid for the selected zone" })
                }

                // Check if configuration already exists (excluding the current one)
                const existingConfig = await ZoneMethodConfig.findOne({
                    _id: { $ne: req.params.id },
                    zoneId,
                    country,
                    methodId,
                })

                if (existingConfig) {
                    return res.status(400).json({ error: "Configuration for this zone, country, and method already exists" })
                }
            }

            const updatedConfig = await ZoneMethodConfig.findByIdAndUpdate(
                req.params.id,
                { $set: req.body },
                { new: true, runValidators: true },
            ).populate("zoneId", "name number countries deliveryMethods")

            if (!updatedConfig) {
                return res.status(404).json({ error: "Configuration not found" })
            }

            res.json(updatedConfig)
        } catch (error) {
            if (error.name === "ValidationError") {
                return res.status(400).json({ error: error.message })
            }
            next(error)
        }
    },

    // Delete a configuration
    deleteConfig: async (req, res, next) => {
        try {
            const deletedConfig = await ZoneMethodConfig.findByIdAndDelete(req.params.id)

            if (!deletedConfig) {
                return res.status(404).json({ error: "Configuration not found" })
            }

            res.json({ message: "Configuration deleted successfully" })
        } catch (error) {
            next(error)
        }
    },
}

module.exports = zoneMethodConfigController
