const CardScheme = require("../models/CardScheme");
const path = require("path");
const fs = require("fs");
const {createTask} = require("../config/EventHandler");
const User = require('../models/user');

// Create a new card scheme
exports.createCardScheme = async (req, res) => {

    try {
        const {scheme_name, scheme_code, created_by} = req.body;
        // Count the total number of records in the collection
        const recordCount = await CardScheme.countDocuments();
        const x = recordCount + 1
        // Calculate the new version number
        const newVersion = x + `.0`;

        const newScheme = new CardScheme({
            scheme_name,
            scheme_code,
            version: newVersion,
            created_by
        });


        const user = await User.findById(created_by);
        const ip =
            req.headers["x-forwarded-for"]?.split(",").shift() ||
            req.socket?.remoteAddress;
        const p =    await newScheme.save();
        const taskData = {
            refId: p._id,
            type: 'Card Scheme',
            title: user.name + ' requested a new card scheme "' + scheme_name+'"',
            date: new Date(),
            user: created_by,
            ipAddress: ip,
        };

        await createTask(taskData)


        res.status(201).json({message: "Scheme created successfully", scheme: newScheme});
    } catch (error) {
        res.status(500).json({message: "Server error", error});
    }
};


exports.deleteCardScheme = async (req, res) => {
    try {
        const {id} = req.params
        const schemes = await CardScheme.findByIdAndUpdate(
            id,
            {deleted_at: new Date()}, // Set deleted_at field
            {new: true}
        )


        if (!schemes) {
            res.status(200).json({message: 'Deleted Successfully'});
        }

    } catch (error) {
        res.status(500).json({message: "Server error", error});
    }

}
// Fetch all card schemes
exports.getCardSchemes = async (req, res) => {
    try {

        const schemes = await CardScheme.find({deleted_at: null})
            .populate('created_by').sort({created_at: -1});
        res.status(200).json(schemes);
    } catch (error) {
        res.status(500).json({message: "Server error", error});
    }
};
