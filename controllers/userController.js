// controllers/userController.js
const User = require('../models/user');
const Role = require('../models/Role');
const fs = require('fs');
const path = require('path');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const {createTransport} = require("nodemailer");


const transporter = createTransport({
    service: 'gmail', // Use your email service provider, e.g., Gmail, Outlook, etc.
    auth: {
        user: process.env.EMAIL_USER, // Use environment variables
        pass: process.env.EMAIL_PASS
    }
});

// Function to register a new user
const postmark = require("postmark");
const client = new postmark.ServerClient("************************************");

const dashboardTemplateMap = {
    local_api: "party-welcome-email",
    programmeManager: "pm-welcome-email",
    cardholder: "cardholder-welcome-email",
    corporate :  "b2b-welcome-email"
};

const registerUser = async (name, email, roleIds, status, dashboard, recordId,company=null) => {
    const existingUser = await User.findOne({ email });
    if (existingUser) {
        throw new Error('User already exists with this email');
    }

    const password = password_generator(8);
    const roles = roleIds;

    const newUser = new User({ name, email, password, roles, status, recordId, dashboard,company });
    await newUser.save();

    try {
        let templateAlias = dashboardTemplateMap[dashboard];

        if (templateAlias) {
            const templateModel = {
                name,
                email,
                password,
                ...(dashboard === 'cardholder' && { roles: "Cardholder" })
            };

            await client.sendEmailWithTemplate({
                From: "<EMAIL>",
                To: email,
                TemplateAlias: templateAlias,
                TemplateModel: templateModel
            });

        } else {
            // For other dashboards (e.g., admin/staff)
            const roleDocs = await getRolesByIds(roleIds);
            const roleNames = roleDocs.map(r => r.name);
            const permissionsList = roleDocs.map(r => r.permissions);
            const mergedUniquePermissions = [...new Set([].concat(...permissionsList))];
            const permissionHtml = mergedUniquePermissions.map(p => "&rarr; &nbsp; " + p).join('<br>');

            await client.sendEmailWithTemplate({
                From: "<EMAIL>",
                To: email,
                TemplateAlias: "staff-welcome-email",
                TemplateModel: {
                    name,
                    email,
                    password,
                    roles: roleNames.join(', '),
                    permissions: permissionHtml
                }
            });
        }

        return { message: "Successfully registered", user: newUser };

    } catch (error) {
        throw new Error('Error while sending email: ' + error.message);
    }
};
const getRolesByIds = async (roleIds) => {
    try {
        // Ensure roleIds is an array and valid
        if (!Array.isArray(roleIds) || roleIds.length === 0) {
            throw new Error('Invalid array of role IDs');
        }

        // Fetch roles by IDs
        return await Role.find({_id: {$in: roleIds}}); // Return the found roles
    } catch (error) {
        console.error('Error fetching roles by IDs:', error);
        throw new Error('Could not fetch roles');
    }
};

function password_generator(len) {
    var length = (len) ? (len) : (10);
    var string = "abcdefghijklmnopqrstuvwxyz"; //to upper
    var numeric = '0123456789';
    var punctuation = '!@$%';
    var password = "";
    var character = "";
    var crunch = true;
    while (password.length < length) {
        entity1 = Math.ceil(string.length * Math.random() * Math.random());
        entity2 = Math.ceil(numeric.length * Math.random() * Math.random());
        entity3 = Math.ceil(punctuation.length * Math.random() * Math.random());
        hold = string.charAt(entity1);
        hold = (password.length % 2 === 0) ? (hold.toUpperCase()) : (hold);
        character += hold;
        character += numeric.charAt(entity2);
        character += punctuation.charAt(entity3);
        password = character;
    }
    password = password.split('').sort(function () {
        return 0.5 - Math.random()
    }).join('');
    return password.substr(0, len);
}

// Function to get all roles
const getRoles = async () => {
    try {
        // Fetch all roles from the database
        return await Role.find(); // Return the roles array
    } catch (error) {
        console.error('Error fetching roles:', error);
        throw new Error('Could not fetch roles');
    }
};

const getRoleById = async (roleId) => {
    try {
        const role = await Role.findById(roleId); // Fetch the role by ID
        if (!role) {
            throw new Error('Role not found');
        }
        return role; // Return the found role
    } catch (error) {
        console.error('Error fetching role by ID:', error);
        throw new Error('Could not fetch role');
    }
};

// Function to login a user
const loginUser = async (email, password) => {
    const user = await User.findOne({email});
    if (!user) {
        throw new Error('User not found');
    }

    // Compare the password with the hashed password
    const isMatch = await bcrypt.compare(password, user.password);
    if (!isMatch) {
        throw new Error('Invalid credentials');
    }

    // Generate a JWT token
    const token = jwt.sign({id: user._id, email: user.email}, process.env.JWT_SECRET, {expiresIn: '1h'});
    return {token, user:{id: user._id, email: user.email ,dashboard: user.dashboard}}; // Return the token and user data (omit sensitive info as needed)
};
//
// // Assign a role to a user
// const assignRoleToUser = async (userId, roleId) => {
//     const user = await User.findById(userId);
//     if (user) {
//         user.roles.push(roleId);
//         await user.save();
//         console.log(`Role ${roleId} assigned to user ${userId}`);
//     } else {
//         console.log('User not found');
//     }
// };

// Check user permission
const checkUserPermission = async (userId, permission) => {
    const user = await User.findById(userId).populate('roles');
    if (!user) return false;

    for (const role of user.roles) {
        if (role.permissions.includes(permission)) {
            return true; // User has the permission
        }
    }
    return false; // User does not have the permission
};






module.exports = {
    registerUser,
    // assignRoleToUser,
    checkUserPermission,
    loginUser
};
