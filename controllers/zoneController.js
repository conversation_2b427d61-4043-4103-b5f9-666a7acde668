const Zone = require("../models/Zone")

// Controller for zone operations
const zoneController = {
    // Get all zones
    getAllZones: async (req, res, next) => {
        try {
            const zones = await Zone.find({})
            res.json(zones)
        } catch (error) {
            next(error)
        }
    },

    // Get a single zone by ID
    getZoneById: async (req, res, next) => {
        try {
            const zone = await Zone.findById(req.params.id)
            if (!zone) {
                return res.status(404).json({ error: "Zone not found" })
            }
            res.json(zone)
        } catch (error) {
            next(error)
        }
    },

    // Create a new zone
    createZone: async (req, res, next) => {
        try {
            const newZone = new Zone(req.body)
            const savedZone = await newZone.save()
            res.status(201).json(savedZone)
        } catch (error) {
            if (error.name === "ValidationError") {
                return res.status(400).json({ error: error.message })
            }
            next(error)
        }
    },

    // Update a zone
    updateZone: async (req, res, next) => {
        try {
            const updatedZone = await Zone.findByIdAndUpdate(
                req.params.id,
                { $set: req.body },
                { new: true, runValidators: true },
            )

            if (!updatedZone) {
                return res.status(404).json({ error: "Zone not found" })
            }

            res.json(updatedZone)
        } catch (error) {
            if (error.name === "ValidationError") {
                return res.status(400).json({ error: error.message })
            }
            next(error)
        }
    },

    // Delete a zone
    deleteZone: async (req, res, next) => {
        try {
            const deletedZone = await Zone.findByIdAndDelete(req.params.id)

            if (!deletedZone) {
                return res.status(404).json({ error: "Zone not found" })
            }

            res.json({ message: "Zone deleted successfully" })
        } catch (error) {
            next(error)
        }
    },
}

module.exports = zoneController
