const ProductVersion = require("../models/productVersions")
const User = require("../models/user")
const {version} = require("mongoose");
const {createTask} = require("../config/EventHandler");

// Get all product versions
exports.getProductVersions = async (req, res) => {
    try {
        const productVersions = await ProductVersion.find({deleted_at: null})
            .populate("created_by").populate("company").sort({created_at: -1});
        res.json(productVersions)
    } catch (error) {
        res.status(500).json({message: "Error fetching product versions", error})
    }
}

// Create a new product version
exports.createProductVersion = async (req, res) => {
    try {
        const {version_name, version_number, version_variant, created_by,product_reference,version_code,company} = req.body
        const ip =
            req.headers["x-forwarded-for"]?.split(",").shift() ||
            req.socket?.remoteAddress;
        // Create new product version document
        const newProductVersion = new ProductVersion({
            version_name,
            version_number,
            version_code:version_code,
            company:company===''?null:company,
            version_variant, created_by,product_reference
        })
        const p = await newProductVersion.save()
        const user = await User.findById(created_by);

        const taskData = {
            refId: p._id,
            type: 'Product Version',
            title: user.name + ' requested a new Product Version "' + version_name+'"',
            date: new Date(),
            user: created_by,
            ipAddress: ip,
        };
        await createTask(taskData)
        res.json({
            message: "Product version created successfully",
            productVersion: newProductVersion,
        })
    } catch (error) {
        res.status(500).json({message: "Error creating product version", error})
    }
}

exports.attachProductCode = async (req, res) => {
    try {
        const { version, version_code } = req.body;

        // Find the product version by ID
        const productVersion = await ProductVersion.findById(version);

        if (!productVersion) {
            return res.status(404).json({ message: "Product version not found" });
        }


        productVersion.version_code = version_code;

        // Save the updated product version
        await productVersion.save();

        res.json({
            message: "Product version updated successfully",
            productVersion,
        });
    } catch (error) {
        res.status(500).json({ message: "Error updating product version", error });
    }
};