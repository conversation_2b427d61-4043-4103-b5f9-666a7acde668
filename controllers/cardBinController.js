const CardBin = require("../models/CardBin");

// Create a new card bin
exports.createCardBin = async (req, res) => {
    try {
        const { bin, code,created_by } = req.body;

        if (!bin || !code) {
            return res.status(400).json({ message: "Both bin and code are required" });
        }

        const newCardBin = new CardBin({
            bin,
            code,
            created_by
        });

        await newCardBin.save();

        res.status(201).json({ message: "Card Bin created successfully", cardBin: newCardBin });
    } catch (error) {
        res.status(500).json({ message: "Server error", error });
    }
};

// Fetch all card bins
exports.getCardBins = async (req, res) => {
    try {
        const cardBins = await CardBin.find({ deleted_at: null })
            .populate("created_by")
        res.status(200).json(cardBins);
    } catch (error) {
        res.status(500).json({ message: "Server error", error });
    }
};
