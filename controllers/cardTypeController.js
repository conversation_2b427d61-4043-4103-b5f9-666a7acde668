const CardType = require("../models/CardType");
const User = require("../models/user");
const {createTask} = require("../config/EventHandler");

// Create a new card type
exports.createCardType = async (req, res) => {
    try {
        const {
            type,
            code,
            created_by,
            binCode,
            binCodeSuffix,
            binCodePrefix,
            currency,
            binCategory,
            binVariant,
            bin_end,
            bin_start
        } = req.body;

        if (!type || !code) {
            return res.status(400).json({message: "Type and Code are required"});
        }
        const recordCount = await CardType.countDocuments();
        const x = recordCount + 1
        // Calculate the new version number
        const newVersion = x + `.0`;
        const newCardType = new CardType({
            type,
            code, created_by, binCode,
            version: newVersion,
            binCodeSuffix: binCodeSuffix,
            binCodePrefix: binCodePrefix,
            currency: currency,
            binCategory: binCategory,
            binVariant: binVariant,
            bin_end: bin_end,
            bin_start: bin_start
        });

        const p =  await newCardType.save();
        const user = await User.findById(created_by);

        const taskData = {
            refId: p._id,
            type: 'BIN Type',
            title: user.name + ' requested a new BIN Type "' + type +' ' + code +'"',
            date: new Date(),
            user: created_by,
            ipAddress: '************',
        };
        await createTask(taskData)

        res.status(201).json({message: "Card Type created successfully", cardType: newCardType});
    } catch (error) {
        res.status(500).json({message: "Server error", error});
    }
};

// Fetch all card types
exports.getCardTypes = async (req, res) => {
    try {
        const cardTypes = await CardType.find({deleted_at: null})
            .populate("created_by").populate("currency").sort({created_at: -1});
        res.status(200).json(cardTypes);
    } catch (error) {
        res.status(500).json({message: "Server error", error});
    }
};
