const CustomerType = require("../models/CustomerType");

// Create a new customer type
exports.createCustomerType = async (req, res) => {
    try {
        const { type, code ,created_by} = req.body;

        if (!type || !code) {
            return res.status(400).json({ message: "Type and Code are required" });
        }

        const newCustomerType = new CustomerType({
            type,
            code,created_by
        });

        await newCustomerType.save();

        res.status(201).json({ message: "Customer Type created successfully", customerType: newCustomerType });
    } catch (error) {
        res.status(500).json({ message: "Server error", error });
    }
};

// Fetch all customer types
exports.getCustomerTypes = async (req, res) => {
    try {
        const customerTypes = await CustomerType.find({ deleted_at: null })
            .populate("created_by")
        res.status(200).json(customerTypes);
    } catch (error) {
        res.status(500).json({ message: "Server error", error });
    }
};
