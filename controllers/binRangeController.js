
const BinRange = require("../models/BinRange")
const User = require("../models/user")
const { ApiError, errorHandler } = require('../config/ApiError');
const mongoose = require("mongoose")
const {createTask} = require("../config/EventHandler");
const { isValidObjectId } = mongoose

/**
 * Create a new bin range
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
 const createBinRange = async (req, res, next) => {
    try {
        const { binType, binCode, binCodePrefix, binCodeSuffix, currency, bin_start, bin_end, created_by } = req.body

        // // Check for overlapping bin ranges
        // const overlappingRanges = await BinRange.findOverlappingRanges(bin_start, bin_end)
        // if (overlappingRanges.length > 0) {
        //     return next(new ApiError(400, "The bin range overlaps with existing ranges"))
        // }

        // Create new bin range
        const binRange = new BinRange({
            binType,
            binCode,
            binCodePrefix,
            binCodeSuffix,
            currency,
            bin_start,
            bin_end,
            created_by: created_by || req.user._id,
            status: "pending", // New entries start as pending
        })

       var p=  await binRange.save()
        const user = await User.findById(req.body.created_by);

        const taskData = {
            refId: p._id,
            type: 'Bin Range',
            title: user.name + ' requested a new Bin Range "' + p.binCode + '"',
            date: new Date(),
            user: user._id,
            ipAddress: '************',
        };
        await createTask(taskData)
        res.status(201).json({
            success: true,
            data: binRange,
        })
    } catch (error) {
        next(error)
    }
}

/**
 * Get all bin ranges
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
 const getAllBinRanges = async (req, res, next) => {
    try {
        const binRanges = await BinRange.find()
            .populate("binType", "type")
            .populate("currency", "currency_code")
            .populate("created_by", "name")
            .sort({ created_at: -1 })

        res.status(200).json(binRanges)
    } catch (error) {
        next(error)
    }
}

/**
 * Get a single bin range by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
 const getBinRangeById = async (req, res, next) => {
    try {
        const { id } = req.params

        if (!mongoose.Types.ObjectId.isValid(id)) {
            return next(new ApiError(400, "Invalid bin range ID"))
        }

        const binRange = await BinRange.findById(id)
            .populate("binType", "type")
            .populate("currency", "currency_code")
            .populate("created_by", "name")

        if (!binRange) {
            return next(new ApiError(404, "Bin range not found"))
        }

        res.status(200).json({
            success: true,
            data: binRange,
        })
    } catch (error) {
        next(error)
    }
}

/**
 * Update a bin range
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
 const updateBinRange = async (req, res, next) => {
    try {
        const { id } = req.params
        const updateData = req.body

        if (!mongoose.Types.ObjectId.isValid(id)) {
            return next(new ApiError(400, "Invalid bin range ID"))
        }

        // Check if bin range is being updated
        if (updateData.bin_start && updateData.bin_end) {
            const overlappingRanges = await BinRange.findOverlappingRanges(updateData.bin_start, updateData.bin_end, id)

            if (overlappingRanges.length > 0) {
                return next(new ApiError(400, "The updated bin range overlaps with existing ranges"))
            }
        }

        // Set status to 'modify' when updating an existing record
        if (updateData.status === "active") {
            updateData.status = "modify"
        }

        // Add updated_by and updated_at
        updateData.updated_by = req.user._id
        updateData.updated_at = new Date()

        const binRange = await BinRange.findByIdAndUpdate(id, updateData, { new: true, runValidators: true })
            .populate("binType", "type")
            .populate("currency", "currency_code")
            .populate("created_by", "name")

        if (!binRange) {
            return next(new ApiError(404, "Bin range not found"))
        }

        res.status(200).json({
            success: true,
            data: binRange,
        })
    } catch (error) {
        next(error)
    }
}

/**
 * Delete a bin range (soft delete by changing status)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
 const deleteBinRange = async (req, res, next) => {
    try {
        const { id } = req.params

        if (!mongoose.Types.ObjectId.isValid(id)) {
            return next(new ApiError(400, "Invalid bin range ID"))
        }

        // Instead of actually deleting, we change the status to 'pending' for approval
        const binRange = await BinRange.findByIdAndDelete(
            id

        )

        if (!binRange) {
            return next(new ApiError(404, "Bin range not found"))
        }

        res.status(200).json({
            success: true,
            message: "Bin range deleted successfully",
        })
    } catch (error) {
        next(error)
    }
}




module.exports = {
     createBinRange,
     getAllBinRanges,
     getBinRangeById,
     updateBinRange,
     deleteBinRange,

};
