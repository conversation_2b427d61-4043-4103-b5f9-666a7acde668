const ProductCurrency = require("../models/ProductCurrency")
const {createTask} = require("../config/EventHandler");
const User = require('../models/user');

// Get all product currencies
exports.getProductCurrencies = async (req, res) => {
    try {
        const productCurrencies = await ProductCurrency.find({ deleted_at: null }).populate('created_by').sort({created_at: -1});
        res.json(productCurrencies)
    } catch (error) {
        res.status(500).json({message: "Error fetching product currencies", error})
    }
}

// Create a new product currency
exports.createProductCurrency = async (req, res) => {
    try {
        const {created_by, currency_code, currency_number} = req.body

        // Create new product currency document
        const recordCount = await ProductCurrency.countDocuments();
        const x = recordCount + 1
        // Calculate the new version number
        const newVersion = x + `.0`;
        const newProductCurrency = new ProductCurrency({
            currency_code: currency_code,
            version: newVersion,
            currency_number: currency_number,created_by:created_by
        })
        const p = await newProductCurrency.save()

        const user = await User.findById(created_by);

        const taskData = {
            refId: p._id,
            type: 'Currency',
            title: user.name + ' requested a new Currency "' + currency_code + '"',
            date: new Date(),
            user: created_by,
            ipAddress: '************',
        };
        await createTask(taskData)
        res.json({
            message: "Product currency created successfully", productCurrency: newProductCurrency,
        })
    } catch (error) {
        res.status(500).json({message: "Error creating product currency", error})
    }

}
exports.getProductCurrency = async (id) => {
    try {
        return await ProductCurrency.findById(id);
    } catch (error) {
        throw error;
    }
};

exports.updateProductCurrency = async (id, updateData) => {
    try {
        return await ProductCurrency.findByIdAndUpdate(id, updateData, {new: true});
    } catch (error) {
        throw error;
    }
};