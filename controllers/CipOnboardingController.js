const Onboarding = require('../models/cipOnboarding');

exports.saveOnboardingData = async (req, res) => {
    try { 
        const onboardingData = new Onboarding(req.body);
        await onboardingData.save();
        res.status(201).json({success: true, message: 'Onboarding data saved successfully', data: onboardingData});
    } catch (error) {
        console.error('Error saving onboarding data:', error);
        res.status(500).json({success: false, message: 'An error occurred while saving onboarding data'});
    }
};

exports.getCipOnboardingById = async (req, res) => {
    try {
        const x = await Onboarding.findOne({personal: req.params.id}).populate('personal');


        if (!x) {
            return res.status(404).json({
                message: 'Onboarding application not found',
            });
        }

        res.status(200).json({
            message: 'Onboarding application retrieved successfully',
            data: x,
        });
    } catch (error) {
        console.error('Error retrieving onboarding:', error);
        res.status(500).json({
            message: 'Failed to retrieve onboarding application',
            error: error instanceof Error ? error.message : 'Unknown error',
        });
    }
};


