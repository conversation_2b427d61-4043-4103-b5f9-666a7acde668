// const BinType = require("../models/BinType")
// const mongoose = require("mongoose")
// const { isValidObjectId } = mongoose
//
// // Get all bin types
// exports.getAllBinTypes = async (req, res) => {
//     try {
//         const { type, status, programmeType, binVariant, binCategory } = req.query
//
//         // Build filter object
//         const filter = { deleted_at: null }
//
//         if (type) filter.type = type
//         if (status) filter.status = status
//         if (programmeType && isValidObjectId(programmeType)) filter.programmeType = programmeType
//         if (binVariant && isValidObjectId(binVariant)) filter.binVariant = binVariant
//         if (binCategory && isValidObjectId(binCategory)) filter.binCategory = binCategory
//
//         const binTypes = await BinType.find(filter)
//             .populate("programmeType")
//             .populate("binVariant")
//             .populate("binCategory")
//             .populate("created_by", "name email")
//
//         res.status(200).json({
//             success: true,
//             count: binTypes.length,
//             data: binTypes,
//         })
//     } catch (error) {
//         res.status(500).json({
//             success: false,
//             message: "Server Error",
//             error: error.message,
//         })
//     }
// }
//
// // Get a single bin type by ID
// exports.getBinTypeById = async (req, res) => {
//     try {
//         const binType = await BinType.findOne({
//             _id: req.params.id,
//             deleted_at: null,
//         })
//             .populate("programmeType")
//             .populate("binVariant")
//             .populate("binCategory")
//             .populate("created_by", "name email")
//
//         if (!binType) {
//             return res.status(404).json({
//                 success: false,
//                 message: "Bin type not found",
//             })
//         }
//
//         res.status(200).json({
//             success: true,
//             data: binType,
//         })
//     } catch (error) {
//         res.status(500).json({
//             success: false,
//             message: "Server Error",
//             error: error.message,
//         })
//     }
// }
//
// // Create a new bin type
// exports.createBinType = async (req, res) => {
//     try {
//         const { type, programmeType, binVariant, binCategory, status, version, reason, created_by } = req.body
//
//         // Validate required fields
//         if (!type || !programmeType || !binVariant || !binCategory || !created_by) {
//             return res.status(400).json({
//                 success: false,
//                 message: "Please provide all required fields",
//             })
//         }
//
//         // Validate ObjectIds
//         if (
//             !isValidObjectId(programmeType) ||
//             !isValidObjectId(binVariant) ||
//             !isValidObjectId(binCategory) ||
//             !isValidObjectId(created_by)
//         ) {
//             return res.status(400).json({
//                 success: false,
//                 message: "Invalid ID format for reference fields",
//             })
//         }
//
//         const newBinType = new BinType({
//             type,
//             programmeType,
//             binVariant,
//             binCategory,
//             status: status || "pending",
//             version,
//             reason,
//             created_by,
//         })
//
//         const savedBinType = await newBinType.save()
//
//         res.status(201).json({
//             success: true,
//             data: savedBinType,
//         })
//     } catch (error) {
//         res.status(500).json({
//             success: false,
//             message: "Server Error",
//             error: error.message,
//         })
//     }
// }
//
// // Update a bin type
// exports.updateBinType = async (req, res) => {
//     try {
//         const { type, programmeType, binVariant, binCategory, status, version, reason } = req.body
//
//         // Find bin type first to check if it exists and isn't deleted
//         const binType = await BinType.findOne({
//             _id: req.params.id,
//             deleted_at: null,
//         })
//
//         if (!binType) {
//             return res.status(404).json({
//                 success: false,
//                 message: "Bin type not found",
//             })
//         }
//
//         // Update fields
//         if (type) binType.type = type
//         if (programmeType && isValidObjectId(programmeType)) binType.programmeType = programmeType
//         if (binVariant && isValidObjectId(binVariant)) binType.binVariant = binVariant
//         if (binCategory && isValidObjectId(binCategory)) binType.binCategory = binCategory
//         if (status) binType.status = status
//         if (version) binType.version = version
//         if (reason) binType.reason = reason
//
//         // Save updated bin type
//         const updatedBinType = await binType.save()
//
//         res.status(200).json({
//             success: true,
//             data: updatedBinType,
//         })
//     } catch (error) {
//         res.status(500).json({
//             success: false,
//             message: "Server Error",
//             error: error.message,
//         })
//     }
// }
//
// // Update bin type status
// exports.updateBinTypeStatus = async (req, res) => {
//     try {
//         const { status, reason } = req.body
//
//         if (!status) {
//             return res.status(400).json({
//                 success: false,
//                 message: "Status is required",
//             })
//         }
//
//         // Validate status value
//         const validStatuses = ["active", "inactive", "pending", "modify", "decline"]
//         if (!validStatuses.includes(status)) {
//             return res.status(400).json({
//                 success: false,
//                 message: `Status must be one of: ${validStatuses.join(", ")}`,
//             })
//         }
//
//         const binType = await BinType.findOne({
//             _id: req.params.id,
//             deleted_at: null,
//         })
//
//         if (!binType) {
//             return res.status(404).json({
//                 success: false,
//                 message: "Bin type not found",
//             })
//         }
//
//         binType.status = status
//         if (reason) binType.reason = reason
//
//         const updatedBinType = await binType.save()
//
//         res.status(200).json({
//             success: true,
//             data: updatedBinType,
//         })
//     } catch (error) {
//         res.status(500).json({
//             success: false,
//             message: "Server Error",
//             error: error.message,
//         })
//     }
// }
//
// // Delete a bin type (soft delete)
// exports.deleteBinType = async (req, res) => {
//     try {
//         const binType = await BinType.findOne({
//             _id: req.params.id,
//             deleted_at: null,
//         })
//
//         if (!binType) {
//             return res.status(404).json({
//                 success: false,
//                 message: "Bin type not found",
//             })
//         }
//
//         // Soft delete by setting deleted_at
//         binType.deleted_at = new Date()
//         await binType.save()
//
//         res.status(200).json({
//             success: true,
//             message: "Bin type deleted successfully",
//         })
//     } catch (error) {
//         res.status(500).json({
//             success: false,
//             message: "Server Error",
//             error: error.message,
//         })
//     }
// }
//
// // Get bin types by programme type
// exports.getBinTypesByProgrammeType = async (req, res) => {
//     try {
//         if (!isValidObjectId(req.params.programmeTypeId)) {
//             return res.status(400).json({
//                 success: false,
//                 message: "Invalid programme type ID format",
//             })
//         }
//
//         const binTypes = await BinType.find({
//             programmeType: req.params.programmeTypeId,
//             deleted_at: null,
//         })
//             .populate("programmeType")
//             .populate("binVariant")
//             .populate("binCategory")
//             .populate("created_by", "name email")
//
//         res.status(200).json({
//             success: true,
//             count: binTypes.length,
//             data: binTypes,
//         })
//     } catch (error) {
//         res.status(500).json({
//             success: false,
//             message: "Server Error",
//             error: error.message,
//         })
//     }
// }
//
