const CardProgrammeType = require("../models/CardProgrammeType");
const User = require("../models/user");
const {createTask} = require("../config/EventHandler");

// Create a new card programme type
exports.createCardProgrammeType = async (req, res) => {
    try {
        const {programme_type,   created_by, bin_type,bin_currency,bin_usage} = req.body;


        if (!programme_type || !bin_type) {
            return res.status(400).json({message: "Programme Type and BIN Type are required"});
        }
        const recordCount = await CardProgrammeType.countDocuments();
        const x = recordCount + 1
        // Calculate the new version number
        const newVersion = x + `.0`;
        const newProgrammeType = new CardProgrammeType({
            programme_type,
            version: newVersion,
            created_by,
            bin_type: bin_type,bin_currency,bin_usage
        });

        const p = await newProgrammeType.save();

        const user = await User.findById(created_by);

        const taskData = {
            refId: p._id,
            type: 'Card Programme Type',
            title: user.name + ' requested a new Card Programme Type "' +programme_type+ '"',
            date: new Date(),
            user: created_by,
            ipAddress: '************',
        };

        await createTask(taskData)
        res.status(201).json({message: "Card Programme Type created successfully", programmeType: newProgrammeType});
    } catch (error) {
        res.status(500).json({message: "Server error", error});
    }
};

// Fetch all card programme types
exports.getCardProgrammeTypes = async (req, res) => {
    try {
        const programmeTypes = await CardProgrammeType.find({deleted_at: null})
            .populate("created_by").populate("bin_type").sort({created_at: -1});
        res.status(200).json(programmeTypes);
    } catch (error) {
        res.status(500).json({message: "Server error", error});
    }
};
