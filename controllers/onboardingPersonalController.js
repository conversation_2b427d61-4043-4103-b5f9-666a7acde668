const IndividualOnboarding = require("../models/IndividualOnboarding")
const Account = require("../models/Account")
const AccountCard = require("../models/AccountCard")
const { sendPostRequest } = require("../config/ApiInstense")
const mongoose = require("mongoose")
const { registerUser } = require("./userController")
const User = require("../models/user")
const VALIDATION_RULES = require("../utils/validation-constants")
const {
    isValidEmail,
    isValidPhone,
    isValidDate,
    isValidApplicationDate,
    isValidObjectId,
    isNotEmpty,
    isValidLength,
    isValidClientCode,
    isValidRiskLevel,
    isValidRiskStatus,
    isValidApplicationStatus,
    isValidIdDocumentType,
    isValidCountryCode,
    isValidTaxCountry,
} = require("../utils/validation-utils")
const { updateIdDocumentImages } = require("../config/id-document-service")
const { countries } = require("../data")
const { fetchBalance } = require("../config/LegecyService")
const B2BIndividualAccounts = require("../models/B2BIndividualAccounts")
const B2BAccount = require("../models/B2BAccount")

const createIndividualOnboarding = async (req, res) => {
    try {
        let {
            company,
            productVersions,
            personalInfo,
            address,
            idDocument,
            taxInfo,
            currency,
            applicationId,
            riskLevel,
            riskStatus,
            applicationStatus,
            applicationDate,
            mothersMaidenName,
            legalId,
            citizenship,
            birthCountry,
            clientCode,
            b2bClient,
        } = req.body

        // Validate required fields
        const validationErrors = validateOnboardingData({
            clientCode,
            personalInfo,
            mothersMaidenName,
            legalId,
            citizenship,
            birthCountry,
            address,
            idDocument,
            taxInfo,
            applicationId,
            riskLevel,
            riskStatus,
            applicationStatus,
            applicationDate,
        })

        // Return validation errors if any
        if (validationErrors.length > 0) {
            return res.status(400).json({
                message: "Validation failed",
                errors: validationErrors,
            })
        }

        // Set default values if company is null
        let origin
        if (company == null) {
            company = "6785126247b8a6a67fbf7cad"
            productVersions = [new mongoose.Types.ObjectId("67caebe302cc703097f4b7df")]
            origin = "API"
        } else {
            // Validate company ID format if provided
            if (!isValidObjectId(company)) {
                return res.status(400).json({
                    message: "Invalid company ID format",
                })
            }
            origin = "Onboarding Form"
        }

        // Validate product versions if provided
        if (productVersions && Array.isArray(productVersions)) {
            for (const version of productVersions) {
                if (!isValidObjectId(version.toString())) {
                    return res.status(400).json({
                        message: "Invalid product version ID format",
                    })
                }
            }
        }

        // Check for existing client with the same clientCode
        const existingOnboarding = await IndividualOnboarding.findOne({ clientID: clientCode })
        if (existingOnboarding) {
            return res.status(400).json({
                message: "Client already exists with this client code",
            })
        }

        // Check for existing user with the same email
        const existingUser = await User.findOne({ email: personalInfo.email })
        if (existingUser) {
            return res.status(400).json({
                message: "Please use a unique email address!",
            })
        }

        console.log("personal_info userType:", personalInfo.userType)

        // Handle B2B users - only save data, no API operations
        if (personalInfo.userType === "b2b") {
            try {
                const savedOnboarding = await registerB2bCardholder({
                    company,
                    productVersions,
                    personalInfo,
                    address,
                    idDocument,
                    taxInfo,
                    currency,
                    applicationId,
                    riskLevel,
                    riskStatus,
                    applicationStatus,
                    applicationDate,
                    mothersMaidenName,
                    legalId,
                    citizenship,
                    birthCountry,
                    clientCode,
                    origin,
                    b2bClient,
                })

                return res.status(201).json({
                    message: "B2B Client created successfully",
                    data: savedOnboarding,
                })
            } catch (error) {
                console.error("B2B onboarding error:", error)
                return res.status(400).json({
                    message: "Failed to create B2B client",
                    error: error.message,
                })
            }
        } else {
            // Handle regular users - perform API operations
            return await handleRegularUserOnboarding(
                {
                    clientCode,
                    personalInfo,
                    address,
                    productVersions,
                    company,
                    mothersMaidenName,
                    birthCountry,
                    legalId,
                    origin,
                    citizenship,
                    idDocument,
                    taxInfo,
                    currency,
                    applicationId,
                    riskLevel,
                    riskStatus,
                    applicationStatus,
                    applicationDate,
                },
                res,
            )
        }
    } catch (error) {
        // Handle validation errors or database errors
        console.error("Onboarding submission error:", error)

        if (error instanceof Error) {
            res.status(400).json({
                message: "Failed to submit onboarding application",
                error: error.message,
            })
        } else {
            res.status(500).json({
                message: "Internal server error",
                error: "An unexpected error occurred",
            })
        }
    }
}

// Extracted validation function for better organization
const validateOnboardingData = (data) => {
    const {
        clientCode,
        personalInfo,
        mothersMaidenName,
        legalId,
        citizenship,
        birthCountry,
        address,
        idDocument,
        taxInfo,
        applicationId,
        riskLevel,
        riskStatus,
        applicationStatus,
        applicationDate,
    } = data

    const validationErrors = []

    // Validate client code (mandatory)
    if (!isNotEmpty(clientCode)) {
        validationErrors.push("Client code is required")
    } else if (!isValidClientCode(clientCode)) {
        validationErrors.push("Invalid client code format. Must be in format RYVL-xxxxxxxx with max length 15")
    }

    // Validate personalInfo (mandatory)
    if (!personalInfo) {
        validationErrors.push("Personal information is required")
    } else {
        // First name (mandatory)
        if (!isNotEmpty(personalInfo.firstName)) {
            validationErrors.push("First name is required")
        } else if (!isValidLength(personalInfo.firstName, VALIDATION_RULES.MAX_LENGTHS.firstName)) {
            validationErrors.push(`First name must not exceed ${VALIDATION_RULES.MAX_LENGTHS.firstName} characters`)
        }

        // Second name (optional)
        if (personalInfo.secondName && !isValidLength(personalInfo.secondName, VALIDATION_RULES.MAX_LENGTHS.secondName)) {
            validationErrors.push(`Second name must not exceed ${VALIDATION_RULES.MAX_LENGTHS.secondName} characters`)
        }

        // Last name (mandatory)
        if (!isNotEmpty(personalInfo.lastName)) {
            validationErrors.push("Last name is required")
        } else if (!isValidLength(personalInfo.lastName, VALIDATION_RULES.MAX_LENGTHS.lastName)) {
            validationErrors.push(`Last name must not exceed ${VALIDATION_RULES.MAX_LENGTHS.lastName} characters`)
        }

        // Email (mandatory)
        if (!isNotEmpty(personalInfo.email)) {
            validationErrors.push("Email is required")
        } else if (!isValidEmail(personalInfo.email)) {
            validationErrors.push("Invalid email format")
        } else if (!isValidLength(personalInfo.email, VALIDATION_RULES.MAX_LENGTHS.email)) {
            validationErrors.push(`Email must not exceed ${VALIDATION_RULES.MAX_LENGTHS.email} characters`)
        }

        // Birth date (mandatory)
        if (!isNotEmpty(personalInfo.birthDate)) {
            validationErrors.push("Date of birth is required")
        } else if (!isValidDate(personalInfo.birthDate)) {
            validationErrors.push("Invalid date of birth format. Must be in format DD MMM YYYY")
        }
    }

    // Validate mothers maiden name (optional)
    if (mothersMaidenName && !isValidLength(mothersMaidenName, VALIDATION_RULES.MAX_LENGTHS.mothersMaidenName)) {
        validationErrors.push(
            `Mother's maiden name must not exceed ${VALIDATION_RULES.MAX_LENGTHS.mothersMaidenName} characters`,
        )
    }

    // Validate legal ID (optional)
    if (legalId && !isValidLength(legalId, VALIDATION_RULES.MAX_LENGTHS.legalId)) {
        validationErrors.push(`Legal ID must not exceed ${VALIDATION_RULES.MAX_LENGTHS.legalId} characters`)
    }

    const citizenshipCode = getCountryNumber(citizenship)
    // Validate citizenship (mandatory)
    if (!isNotEmpty(citizenshipCode)) {
        validationErrors.push("Citizenship is required")
    } else if (!isValidLength(citizenshipCode, VALIDATION_RULES.MAX_LENGTHS.citizenship)) {
        validationErrors.push(`Citizenship must not exceed ${VALIDATION_RULES.MAX_LENGTHS.citizenship} characters`)
    } else if (!isValidCountryCode(citizenshipCode)) {
        validationErrors.push("Invalid citizenship country code")
    }

    const birthCountryCode = getCountryNumber(personalInfo?.birthCountry)
    // Validate birth country (mandatory)
    if (!isNotEmpty(birthCountryCode)) {
        validationErrors.push("Birth country is required")
    } else if (!isValidLength(birthCountryCode, VALIDATION_RULES.MAX_LENGTHS.birthCountry)) {
        validationErrors.push(`Birth country must not exceed ${VALIDATION_RULES.MAX_LENGTHS.birthCountry} characters`)
    } else if (!isValidCountryCode(birthCountryCode)) {
        validationErrors.push("Invalid birth country code")
    }

    // Validate address (mandatory)
    if (!address) {
        validationErrors.push("Address information is required")
    } else {
        // Street (mandatory)
        if (!isNotEmpty(address.street)) {
            validationErrors.push("Street is required")
        } else if (!isValidLength(address.street, VALIDATION_RULES.MAX_LENGTHS.street)) {
            validationErrors.push(`Street must not exceed ${VALIDATION_RULES.MAX_LENGTHS.street} characters`)
        }

        if (address.buildingNumber && !isValidLength(address.buildingNumber, VALIDATION_RULES.MAX_LENGTHS.buildingNumber)) {
            validationErrors.push(`Building number must not exceed ${VALIDATION_RULES.MAX_LENGTHS.buildingNumber} characters`)
        }

        // Apartment number (optional)
        if (
            address.apartmentNumber &&
            !isValidLength(address.apartmentNumber, VALIDATION_RULES.MAX_LENGTHS.apartmentNumber)
        ) {
            validationErrors.push(
                `Apartment number must not exceed ${VALIDATION_RULES.MAX_LENGTHS.apartmentNumber} characters`,
            )
        }

        // City (mandatory)
        if (!isNotEmpty(address.city)) {
            validationErrors.push("City is required")
        } else if (!isValidLength(address.city, VALIDATION_RULES.MAX_LENGTHS.city)) {
            validationErrors.push(`City must not exceed ${VALIDATION_RULES.MAX_LENGTHS.city} characters`)
        }

        // State/Province (optional)
        if (address.stateProvince && !isValidLength(address.stateProvince, VALIDATION_RULES.MAX_LENGTHS.stateProvince)) {
            validationErrors.push(`State/Province must not exceed ${VALIDATION_RULES.MAX_LENGTHS.stateProvince} characters`)
        }

        // Zip code (mandatory)
        if (!isNotEmpty(address.zipCode)) {
            validationErrors.push("Zip code is required")
        } else if (!isValidLength(address.zipCode, VALIDATION_RULES.MAX_LENGTHS.zipCode)) {
            validationErrors.push(`Zip code must not exceed ${VALIDATION_RULES.MAX_LENGTHS.zipCode} characters`)
        }

        const countryCode = getCountryNumber(address.country)
        // Country (mandatory)
        if (!isNotEmpty(countryCode)) {
            validationErrors.push("Country is required")
        } else if (!isValidLength(countryCode, VALIDATION_RULES.MAX_LENGTHS.country)) {
            validationErrors.push(`Country must not exceed ${VALIDATION_RULES.MAX_LENGTHS.country} characters`)
        } else if (!isValidCountryCode(countryCode)) {
            validationErrors.push("Invalid country code")
        }

        // Validate combined address length
        const combinedAddress = `${address.street || ""} ${address.buildingNumber || ""} ${address.apartmentNumber || ""} ${address.city || ""} ${address.stateProvince || ""} ${address.zipCode || ""} ${countryCode || ""}`
        if (combinedAddress.length > VALIDATION_RULES.MAX_LENGTHS.address) {
            validationErrors.push(`Combined address must not exceed ${VALIDATION_RULES.MAX_LENGTHS.address} characters`)
        }
    }

    // Validate ID document (mandatory)
    if (!idDocument) {
        validationErrors.push("ID document information is required")
    } else {
        // Customer ID type (mandatory)
        if (!isNotEmpty(idDocument.customerIdType)) {
            validationErrors.push("ID document type is required")
        } else if (!isValidIdDocumentType(idDocument.customerIdType)) {
            validationErrors.push(
                "Invalid ID document type. Allowed values: ID, PASSPORT, RESIDENCE_CARD, DRIVING_LICENSE, MINOR_WITHOUT_ID, OTHER",
            )
        }

        // ID number (mandatory)
        if (!isNotEmpty(idDocument.number)) {
            validationErrors.push("ID document number is required")
        } else if (!isValidLength(idDocument.number, VALIDATION_RULES.MAX_LENGTHS.idDocumentNumber)) {
            validationErrors.push(
                `ID document number must not exceed ${VALIDATION_RULES.MAX_LENGTHS.idDocumentNumber} characters`,
            )
        }

        // Issue date (mandatory)
        if (!isNotEmpty(idDocument.issueDate)) {
            validationErrors.push("ID issue date is required")
        } else if (!isValidDate(idDocument.issueDate)) {
            validationErrors.push("Invalid ID issue date format. Must be in format DD MMM YYYY")
        }

        // Expiry date (mandatory)
        if (!isNotEmpty(idDocument.expiryDate)) {
            validationErrors.push("ID expiry date is required")
        } else if (!isValidDate(idDocument.expiryDate)) {
            validationErrors.push("Invalid ID expiry date format. Must be in format DD MMM YYYY")
        }

        const issuingCountryCode = getCountryNumber(idDocument.issuingCountry)
        // Issuing country (mandatory)
        if (!isNotEmpty(issuingCountryCode)) {
            validationErrors.push("ID issuing country is required")
        } else if (!isValidLength(issuingCountryCode, VALIDATION_RULES.MAX_LENGTHS.idDocumentIssuingCountry)) {
            validationErrors.push(
                `ID issuing country must not exceed ${VALIDATION_RULES.MAX_LENGTHS.idDocumentIssuingCountry} characters`,
            )
        } else if (!isValidCountryCode(issuingCountryCode)) {
            validationErrors.push("Invalid ID issuing country code")
        }

        // ID authority (mandatory)
        if (!isNotEmpty(idDocument.idAuthority)) {
            validationErrors.push("ID authority is required")
        } else if (!isValidLength(idDocument.idAuthority, VALIDATION_RULES.MAX_LENGTHS.idDocumentAuthority)) {
            validationErrors.push(
                `ID authority must not exceed ${VALIDATION_RULES.MAX_LENGTHS.idDocumentAuthority} characters`,
            )
        }
    }

    // Validate tax info (optional)
    if (taxInfo) {
        // Tax ID number (optional)
        if (taxInfo.taxIdNumber && !isValidLength(taxInfo.taxIdNumber, VALIDATION_RULES.MAX_LENGTHS.taxIdNumber)) {
            validationErrors.push(`Tax ID number must not exceed ${VALIDATION_RULES.MAX_LENGTHS.taxIdNumber} characters`)
        }
    }

    if (isNotEmpty(applicationId) && !isValidLength(applicationId, VALIDATION_RULES.MAX_LENGTHS.applicationId)) {
        validationErrors.push(`Application ID must not exceed ${VALIDATION_RULES.MAX_LENGTHS.applicationId} characters`)
    }

    if (isNotEmpty(riskLevel) && !isValidRiskLevel(riskLevel)) {
        validationErrors.push("Invalid risk level. Must be a number between 0 and 999")
    }

    if (isNotEmpty(riskStatus) && !isValidRiskStatus(riskStatus)) {
        validationErrors.push("Invalid risk status. Allowed values: LOW, MEDIUM, HIGH, VERY HIGH")
    } else if (isNotEmpty(riskStatus) && !isValidLength(riskStatus, VALIDATION_RULES.MAX_LENGTHS.riskStatus)) {
        validationErrors.push(`Risk status must not exceed ${VALIDATION_RULES.MAX_LENGTHS.riskStatus} characters`)
    }

    // Validate application status (mandatory)
    if (isNotEmpty(applicationStatus) && !isValidApplicationStatus(applicationStatus)) {
        validationErrors.push("Invalid application status. Allowed values: APPROVED, REJECTED, PENDING")
    } else if (
        isNotEmpty(applicationStatus) &&
        !isValidLength(applicationStatus, VALIDATION_RULES.MAX_LENGTHS.applicationStatus)
    ) {
        validationErrors.push(
            `Application status must not exceed ${VALIDATION_RULES.MAX_LENGTHS.applicationStatus} characters`,
        )
    }

    if (isNotEmpty(applicationDate) && !isValidApplicationDate(applicationDate)) {
        validationErrors.push("Invalid application date format. Must be in format DD-MMM-YYYY hh:mm:ss")
    }

    return validationErrors
}

// Extracted regular user onboarding logic
const handleRegularUserOnboarding = async (data, res) => {
    const {
        clientCode,
        personalInfo,
        address,
        productVersions,
        company,
        mothersMaidenName,
        birthCountry,
        legalId,
        origin,
        citizenship,
        idDocument,
        taxInfo,
        currency,
        applicationId,
        riskLevel,
        riskStatus,
        applicationStatus,
        applicationDate,
    } = data

    // Prepare client data for API request
    const clientData = {
        clientCode: clientCode,
        firstName: personalInfo.firstName,
        lastName: personalInfo.lastName,
        phoneNumber: personalInfo.phoneNumber,
        address: {
            street: address.street,
            buildingNumber: address.buildingNumber,
            apartmentNumber: address.apartmentNumber,
            city: address.city,
            country: getCountryNumber(address.country),
            zipCode: address.zipCode,
        },
    }

    const url = "https://apifintech.sandbox.itcardpaymentservice.pl:30080/v1/clients"

    try {
        const result = await sendPostRequest(url, clientData)

        if (result.status === "APPROVED") {
            // Create new onboarding application
            const newOnboarding = new IndividualOnboarding({
                productVersion: productVersions,
                company: company,
                personalInfo: {
                    firstName: personalInfo.firstName,
                    middleName: personalInfo.secondName,
                    lastName: personalInfo.lastName,
                    dateOfBirth: new Date(personalInfo.birthDate),
                    email: personalInfo.email,
                    phone: personalInfo.phoneNumber,
                    mothersMaidenName,
                    authPhoneNumber: personalInfo.authPhoneNumber,
                    birthCountry: getCountryNumber(personalInfo.birthCountry),
                },
                legalId,
                origin: origin,
                citizenship: getCountryNumber(citizenship),
                address: {
                    ...address,
                    country: getCountryNumber(address.country),
                },
                idDocument: {
                    number: idDocument.number,
                    customerIdType: idDocument.customerIdType,
                    issueDate: idDocument.issueDate,
                    expiryDate: idDocument.expiryDate,
                    issuingCountry: getCountryNumber(idDocument.issuingCountry),
                    idAuthority: idDocument.idAuthority,
                },
                taxInfo,
                client: true,
                dashboardStatus: "ACTIVE",
                clientID: clientCode,
                cardCurrency: currency,
                applicationId: applicationId || null,
                riskLevel: riskLevel || null,
                riskStatus: riskStatus || null,
                applicationStatus: applicationStatus || null,
                applicationDate: applicationDate ? new Date(applicationDate) : null,
            })

            // Save to database
            const savedOnboarding = await newOnboarding.save()

            // Register user if origin is API
            if (origin.toUpperCase() === "API") {
                await registerUser(
                    personalInfo.firstName + " " + personalInfo.lastName,
                    personalInfo.email,
                    [],
                    "active",
                    "cardholder",
                    savedOnboarding._id,
                )
            }

            res.status(201).json({
                message: "Client created successfully",
                data: savedOnboarding,
            })
        } else {
            // Handle non-approved status from external API
            res.status(400).json({
                message: "Client creation failed at payment service",
                status: result.status,
            })
        }
    } catch (error) {
        console.error("Request failed:", error.message)
        res.status(400).json({
            message: "Failed to submit onboarding application",
            error: error.message,
        })
    }
}

// Fixed registerB2bCardholder function with better error handling
const registerB2bCardholder = async (data) => {
    const {
        company,
        productVersions,
        personalInfo,
        address,
        idDocument,
        taxInfo,
        currency,
        applicationId,
        riskLevel,
        riskStatus,
        applicationStatus,
        applicationDate,
        mothersMaidenName,
        legalId,
        citizenship,
        birthCountry,
        clientCode,
        origin,
        b2bClient,
    } = data

    if (!b2bClient) {
        throw new Error("B2B client ID is required")
    }

    const b2b = await B2BAccount.findById(b2bClient)
    if (!b2b) {
        throw new Error("B2B account not found")
    }

    console.log("B2B client code:", b2b.clientCode)

    const debitPayload = {
        currencyCode: "EUR",
        accNo: "**********************",
        owners: [
            {
                clientCode: b2b.clientCode,
                relationship: "OWN",
            },
        ],
    }

    const debitUrl = "https://apifintech.sandbox.itcardpaymentservice.pl:30080/v1/accounts/debitAccount"
    const personResult = await sendPostRequest(debitUrl, debitPayload)

    console.log("Debit Account API response:", personResult)

    if (!personResult.accNo) {
        throw new Error("Failed to create debit account")
    }

    // Create new onboarding application
    const newOnboarding = new IndividualOnboarding({
        productVersion: productVersions,
        company: company,
        personalInfo: {
            firstName: personalInfo.firstName,
            middleName: personalInfo.secondName,
            lastName: personalInfo.lastName,
            dateOfBirth: new Date(personalInfo.birthDate),
            email: personalInfo.email,
            phone: personalInfo.phoneNumber,
            mothersMaidenName,
            authPhoneNumber: personalInfo.authPhoneNumber,
            birthCountry: getCountryNumber(personalInfo.birthCountry),
        },
        legalId,
        origin: origin,
        citizenship: getCountryNumber(citizenship),
        address: {
            ...address,
            country: getCountryNumber(address.country),
        },
        idDocument: {
            number: idDocument.number,
            customerIdType: idDocument.customerIdType,
            issueDate: idDocument.issueDate,
            expiryDate: idDocument.expiryDate,
            issuingCountry: getCountryNumber(idDocument.issuingCountry),
            idAuthority: idDocument.idAuthority,
        },
        taxInfo,
        client: true,
        dashboardStatus: "ACTIVE",
        clientID: "RYVL-"+generateRandomNumbers() ,
        cardCurrency: currency,
        applicationId: applicationId || null,
        riskLevel: riskLevel || null,
        riskStatus: riskStatus || null,
        applicationStatus: applicationStatus || null,
        applicationDate: applicationDate ? new Date(applicationDate) : null,
        userType: "b2b",
        b2bClient: b2bClient,
    })

    console.log("New B2B onboarding application:", newOnboarding)
    const savedOnboarding = await newOnboarding.save()

    // Create B2B individual account
    const b2bIndivAccount = new B2BIndividualAccounts({
        parent: savedOnboarding._id,
        account: personResult.accNo,
        status: personResult.status,
        currencyCode: personResult.currencyCode,
        currencyName: personResult.currencyName,
        owners: (personResult.owners || []).map((owner) => ({
            clientCode: owner.clientCode,
            relationship: owner.relationship,
            mainOwner: owner.mainOwner,
        })),
    })

    await b2bIndivAccount.save()

    return savedOnboarding
}

/**
 * Upload ID document images for a client
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const uploadIdDocumentImages = async (req, res) => {
    try {
        const { clientCode } = req.params

        if (!clientCode) {
            return res.status(400).json({
                message: "Client code is required",
            })
        }

        // Check if files are provided in the request
        let idFront, idBack

        // Handle different upload methods
        if (req.files) {
            // For multer or express-fileupload
            idFront = req.files.idFront || req.files["idFront"]
            idBack = req.files.idBack || req.files["idBack"]
        } else if (req.body) {
            // For base64 encoded images in request body
            idFront = req.body.idFront
            idBack = req.body.idBack
        }

        if (!idFront) {
            return res.status(400).json({
                message: "Front ID image is required",
            })
        }

        // Update ID document images
        const updatedOnboarding = await updateIdDocumentImages(clientCode, idFront, idBack)

        res.status(200).json({
            message: "ID document images uploaded successfully",
            data: {
                clientCode,
                idDocument: {
                    frontImagePath: updatedOnboarding.idDocument.frontImagePath,
                    backImagePath: updatedOnboarding.idDocument.backImagePath,
                },
            },
        })
    } catch (error) {
        console.error("ID document upload error:", error)

        res.status(error.message.includes("not found") ? 404 : 400).json({
            message: "Failed to upload ID document images",
            error: error.message,
        })
    }
}

// Get all individual onboarding applications
const getAllIndividualOnboardings = async (req, res) => {
    try {
        const onboardings = await IndividualOnboarding.find().populate("company","company_name").populate("cardCurrency").sort({ createdAt: -1 })

        res.status(200).json({
            message: "Onboarding applications retrieved successfully",
            data: onboardings,
        })
    } catch (error) {
        console.error("Error retrieving onboardings:", error)
        res.status(500).json({
            message: "Failed to retrieve onboarding applications",
            error: error instanceof Error ? error.message : "Unknown error",
        })
    }
}

// Get a specific individual onboarding application by ID
const getIndividualOnboardingById = async (req, res) => {
    try {
        if (!req.params.id) {
            return res.status(400).json({
                message: "Onboarding ID is required",
            })
        }

        const onboarding = await IndividualOnboarding.findById(req.params.id).populate("cardCurrency").populate("company")

        if (!onboarding) {
            return res.status(404).json({
                message: "Onboarding application not found",
            })
        }

        const account = await Account.find({ onboarding: req.params.id })
        const onboardingId = new mongoose.Types.ObjectId(req.params.id)

        const cards = await AccountCard.find({ onboarding: onboardingId }).sort({ createdAt: -1 })

        let balance = 0

        // Only try to fetch balance if there's at least one account
        if (account.length > 0) {
            try {
                const result = await fetchBalance(account[0].accountNumber)
                balance = result?.availableBalance ?? 0
            } catch (balanceError) {
                console.warn("Failed to fetch balance, defaulting to 0:", balanceError.message)
                balance = 0
            }
        }

        res.status(200).json({
            message: "Onboarding application retrieved successfully",
            data: onboarding,
            account,
            cards,
            balance,
        })
    } catch (error) {
        console.error("Error retrieving onboarding:", error)
        res.status(500).json({
            message: "Failed to retrieve onboarding application",
            error: error instanceof Error ? error.message : "Unknown error",
        })
    }
}

// Helper functions
async function generateUniqueSerialNumber(lastSerial, model) {
    // Prefix and year
    const prefix = "RYVL-"
    const year = new Date().getFullYear().toString().slice(-2) // Last 2 digits of the year

    // Start the counter from lastSerial + 1
    let counter = lastSerial + 1

    let isUnique = false
    let generatedSerial

    while (!isUnique) {
        // Pad the counter with leading zeros to ensure it is always 7 digits
        const counterString = counter.toString().padStart(7, "0")

        // Combine the parts
        generatedSerial = `${prefix}${year}${counterString}`

        // Check if the serial exists in the database
        const existingRecord = await model.findOne({ clientID: generatedSerial })

        if (!existingRecord) {
            isUnique = true // The serial is unique
        } else {
            counter++ // Increment the counter and try again
        }
    }

    return generatedSerial
}

function generateRandomNumbers() {
    // 1: Create a `Set` object
    const uniqueNumbers = new Set()
    while (uniqueNumbers.size < 5) {
        // 2: Generate each random number
        uniqueNumbers.add(Math.floor(Math.random() * (10 - 5 + 1)) + 5)
    }
    // 3: Immediately insert them numbers into the Set...
    return Number(Array.from(uniqueNumbers).join(""))
}

function getCountryNumber(code) {
    if (!code) return null
    return countries.find((c) => c.code.toUpperCase() === code.toUpperCase())?.isoNumeric
}

module.exports = {
    getAllIndividualOnboardings,
    getIndividualOnboardingById,
    createIndividualOnboarding,
    uploadIdDocumentImages,
}
