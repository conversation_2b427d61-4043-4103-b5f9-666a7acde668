const DeliveryMethod = require("../models/DeliveryMethod")

// Controller for delivery method operations
const deliveryMethodController = {
    // Get all delivery methods
    getAllDeliveryMethods: async (req, res, next) => {
        try {
            const deliveryMethods = await DeliveryMethod.find({})
            res.json(deliveryMethods)
        } catch (error) {
            next(error)
        }
    },

    // Get a single delivery method by ID
    getDeliveryMethodById: async (req, res, next) => {
        try {
            const deliveryMethod = await DeliveryMethod.findById(req.params.id)
            if (!deliveryMethod) {
                return res.status(404).json({ error: "Delivery method not found" })
            }
            res.json(deliveryMethod)
        } catch (error) {
            next(error)
        }
    },

    // Create a new delivery method
    createDeliveryMethod: async (req, res, next) => {
        try {
            const newDeliveryMethod = new DeliveryMethod(req.body)
            const savedDeliveryMethod = await newDeliveryMethod.save()
            res.status(201).json(savedDeliveryMethod)
        } catch (error) {
            if (error.name === "ValidationError") {
                return res.status(400).json({ error: error.message })
            }
            next(error)
        }
    },

    // Update a delivery method
    updateDeliveryMethod: async (req, res, next) => {
        try {
            const updatedDeliveryMethod = await DeliveryMethod.findByIdAndUpdate(
                req.params.id,
                { $set: req.body },
                { new: true, runValidators: true },
            )

            if (!updatedDeliveryMethod) {
                return res.status(404).json({ error: "Delivery method not found" })
            }

            res.json(updatedDeliveryMethod)
        } catch (error) {
            if (error.name === "ValidationError") {
                return res.status(400).json({ error: error.message })
            }
            next(error)
        }
    },

    // Delete a delivery method
    deleteDeliveryMethod: async (req, res, next) => {
        try {
            const deletedDeliveryMethod = await DeliveryMethod.findByIdAndDelete(req.params.id)

            if (!deletedDeliveryMethod) {
                return res.status(404).json({ error: "Delivery method not found" })
            }

            res.json({ message: "Delivery method deleted successfully" })
        } catch (error) {
            next(error)
        }
    },
}

module.exports = deliveryMethodController
