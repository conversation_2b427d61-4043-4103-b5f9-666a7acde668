const Account = require("../models/ClientAccount");
const Card = require("../models/AccountCard");
const { sendPostRequest, sendGetRequest } = require("../config/ApiInstense");

// Create Account
exports.createAccount = async (req, res) => {
    const { clientId, userId, currencyCode } = req.body;

    const clientData = JSON.stringify({
        productCode: "RYV1",
        currencyCode,
        accNo: "**********************",
        owners: [{ clientCode: clientId, relationship: "OWN" }],
    });

    console.log("Request Payload:", clientData);

    const url = "https://apifintech.sandbox.itcardpaymentservice.pl:30080/v1/accounts/debitAccount";

    try {
        const result = await sendPostRequest(url, clientData);
        const accountData = {
            onboarding: userId,
            accNo: result.accNo,
            status: result.status,
            currencyCode: result.currencyCode,
            currencyName: result.currencyName,
            owners: result.owners.map(owner => ({
                clientCode: owner.clientCode,
                relationship: owner.relationship,
                mainOwner: owner.mainOwner
            }))
        };

        const newAccount = new Account(accountData);
        await newAccount.save();

        console.log("Account saved:", newAccount);
        res.status(200).json({ success: true, message: "Account created successfully", account: newAccount });
    } catch (error) {
        console.error("Error:", error);
        res.status(500).json({ success: false, message: "Error creating account", error: error.message });
    }
};

// Create Virtual Card
exports.createVirtualCard = async (req, res) => {
    await createCard(req, res, "CARDV1", "none");
};

// Create Physical Card
exports.createPhysicalCard = async (req, res) => {
    await createCard(req, res, "CARD1", "LETTER");
};

// Common Function for Creating Cards
async function createCard(req, res, visualType, deliveryType) {
    const { clientId, currencyCode, accNo, userId } = req.body;

    const clientData = JSON.stringify({
        holder: clientId,
        productCode: "RYV1",
        visual: visualType,
        delivery: { deliveryType, deliveryBranch: "DHL", oneTimeDeliveryBranch: "DHL" },
        pinDelivery: { deliveryType },
        account: {
            currencyCode,
            productCode: "RYV1",
            owners: [{ clientCode: clientId, relationship: "OWN" }],
            accNo
        },
        "3dsAuthMethods": ["MOBILE_APP"]
    });

    console.log("Constructed Payload:", clientData);

    const url = "https://apifintech.sandbox.itcardpaymentservice.pl:30080/v1/cards/debitCard";

    try {
        const apiResponse = await sendPostRequest(url);
        const cardData = {
            cardHash: apiResponse.cardHash,
            cardKey: apiResponse.cardKey,
            expDate: apiResponse.expDate,
            status: apiResponse.status,
            statusCode: apiResponse.statusCode,
            kind: apiResponse.kind,
            productCode: apiResponse.productCode,
            productDesc: apiResponse.productDesc,
            main: apiResponse.main,
            holder: apiResponse.holder,
            accNo: apiResponse.accNo,
            embossName1: apiResponse.embossName1,
            cardMask: apiResponse.cardMask,
            onboarding: userId,
        };

        const newCard = new Card(cardData);
        await newCard.save();

        console.log("Card saved:", newCard);
        res.status(200).json({ success: true, message: "Card created successfully", card: newCard });
    } catch (error) {
        console.error("Error:", error);
        res.status(500).json({ success: false, message: "Error creating card", error: error.message });
    }
}

// Get Card Details
exports.getCardDetails = async (req, res) => {
    const { id } = req.params;
    console.log("Fetching card details for ID:", id);

    const url = `https://apifintech.sandbox.itcardpaymentservice.pl:30080/v1/cards/${id}`;

    try {
        const apiResponse = await sendGetRequest(url);
        res.status(200).json({ success: true, card: apiResponse });
    } catch (error) {
        console.error("Error:", error);
        res.status(500).json({ success: false, message: "Error fetching card details", error: error.message });
    }
};
