const ProductVersionName = require("../models/ProductVersionName")




exports.getProductVersionNames = async (req, res) => {
    try {
        const productVersions = await ProductVersionName.find({ deleted_at: null })
            .populate("created_by")
        res.status(200).json(productVersions);
    } catch (error) {
        res.status(500).json({ message: "Server error", error });
    }
};


// Create a new product version name
exports.createProductVersionName = async (req, res) => {
    try {
        const { version_name, code,created_by } = req.body
        const productVersion = await ProductVersionName.create({ version_name, code,created_by })
        res.json({ productVersion })
    } catch (error) {
        res.status(500).json({ message: "Error creating product version name" })
    }
}
