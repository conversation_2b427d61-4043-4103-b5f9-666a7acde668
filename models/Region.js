const mongoose = require("mongoose")

// Define the schema for the Region model
const RegionSchema = new mongoose.Schema({
    // Region name (optional in your form but might be useful in the database)
    name: {
        type: String,
        trim: true,
    },

    // Region number (required in your form)
    number: {
        type: String,
        required: [true, "Region number is required"],
        trim: true,
        unique: true,
    },

    // Geographic region category
    geographicRegion: {
        type: String,
        required: [true, "Geographic region is required"],
        enum: ["Europe", "North America/Africa", "South/Central America/Asia", "Australia/Oceania"],
    },

    // Array of countries in this region
    countries: {
        type: [String],
        required: [true, "At least one country must be selected"],
        validate: {
            validator: (countries) => countries.length > 0,
            message: "Please select at least one country",
        },
    },

    // Delivery methods available for this region
    deliveryMethods: {
        type: [String],
        default: ["Polish Post"],
        required: [true, "At least one delivery method is required"],
    },

    // Timestamps for when the region was created and last updated
    createdAt: {
        type: Date,
        default: Date.now,
    },

    updatedAt: {
        type: Date,
        default: Date.now,
    },
})

// Pre-save hook to update the updatedAt timestamp
RegionSchema.pre("save", function (next) {
    this.updatedAt = Date.now()
    next()
})

// Create and export the Region model
const Region = mongoose.model("Region", RegionSchema)

module.exports = Region
