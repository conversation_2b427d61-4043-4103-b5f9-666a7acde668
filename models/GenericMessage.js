const mongoose = require("mongoose");

const genericMessageSchema = new mongoose.Schema(
    {
        data: {
            type: mongoose.Schema.Types.Mixed,
            required: true,
        },
        receivedAt: {
            type: Date,
            default: Date.now,
        },
    },
    { strict: false } // allow any structure
);

module.exports = mongoose.model("GenericMessage", genericMessageSchema);
