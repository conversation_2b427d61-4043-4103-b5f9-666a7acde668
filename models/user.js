const mongoose = require('mongoose');
const {Schema} = require("mongoose");

const userSchema = new mongoose.Schema({
    name: {
        required: true,
        type: String
    },
    email: {
        required: true,
        unique: true, // Ensure email uniqueness
        type: String
    },
    password: {
        required: true,
        type: String
    },


    roles: [{
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Role' ,default:{}

    }],
    dashboard: {
        type: String,
        default: ''
    },

    recordId: {
        type: Schema.Types.ObjectId, ref: 'IndividualOnboarding',
       required: false
    },

    twoFactorSecret: {
        type: String,
    },
    twoFactorEnabled: {
        type: Boolean,
        default: false,
    },

    status: {
        required: true,
        type: String
    },
    createdAt: {
        type: Date,
        default: Date.now
    },
    company:{
        type: Schema.Types.ObjectId, ref: 'Company',
        required: false
    },

    lastLoginAt: {
        type: Date,
    },

    lastLoginIP: {
        type: String,
    },
    loginAttempts: { type: Number, default: 0 },
    lockUntil: Date,
    isLocked: {
        type: Boolean,
        default: false,
        get: function() {
            return !!(this.lockUntil && this.lockUntil > Date.now());
        }
    }
});

// Middleware to hash password (you can use bcryptjs or another hashing library)
userSchema.pre('save', async function(next) {
    if (this.isModified('password')) {
        const bcrypt = require('bcryptjs');
        this.password = await bcrypt.hash(this.password, 10); // 10 is the salt rounds
    }
    next();
});


module.exports = mongoose.models.User || mongoose.model('User', userSchema)

