const mongoose = require("mongoose");
const {Schema} = require("mongoose");
const OwnerSchema = new mongoose.Schema({
    clientCode: {
        type: String, required: false,
    }, relationship: {
        type: String, required: false, enum: ['OWN'], // Define expected values for relationship
    }, mainOwner: {
        type: Boolean, required: false,
    },
});
const AccountSchema = new mongoose.Schema({

    owners: {
        type: [OwnerSchema], required: false,
    },
    onboarding: {type: Schema.Types.ObjectId, ref: 'IndividualOnboarding', required: false},
    company: {type: Schema.Types.ObjectId, ref: 'Company', required: false},
    b2bCompany: {type: Schema.Types.ObjectId, ref: 'Company', required: false},
    isCompany: {type: Boolean, required: false, default: false},
    isB2b: {type: Boolean, required: false, default: false},
    accountNumber: { type: String, required: false },
    accountHolder: { type: String, required: false },
    currencyCode: {type: String, required: false, default: "EUR"},
    currency: {type: String, required: false, default: "978"},
    relationship: {type: String},
    bankName: {type: String},
    status: {
        type: String
    },
    clientCode: { type: String, required: false },  // External application ID
    extAppId: { type: String, required: false },  // External application ID
    productCode: { type: String, required: false }, // Product code
    productDesc: { type: String, required: false }, // Product description
    bankNumber: { type: String, required: false }, // Bank number
    openDate: { type: Date, required: false }, // Account opening date
    balance: { type: Number, min: 0, default: 0 }, // Balance (optional)
    closeCause: {
        type: String,
    }
}, { timestamps: false });

module.exports = mongoose.models.DebiteAccount || mongoose.model("DebiteAccount", AccountSchema);
