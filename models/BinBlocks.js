const mongoose = require("mongoose");

// Define the schema for BinBlocks
const BinBlockSchema = new mongoose.Schema({
    cardRange: {
        type: String, required: true, trim: true
    },

    binStart: {
        type: String, required: true, trim: true
    },
    binEnd: {
        type: String, required: true, trim: true
    }, company: {
        type: mongoose.Schema.Types.ObjectId, ref: "Company", // Assuming you have a Company model
        required: true
    }, programme: {
        type: mongoose.Schema.Types.ObjectId, ref: "CardProgram", // Assuming you have a Programme model
        required: true},
    product_version: {
        type: mongoose.Schema.Types.ObjectId, ref: "ProductVersion", // Assuming you have a Programme model
        required: true
    }, createdAt: {
        type: Date, default: Date.now
    }
});

// Create and export the model
const BinBlock = mongoose.model("BinBlock", BinBlockSchema);

module.exports = BinBlock;
