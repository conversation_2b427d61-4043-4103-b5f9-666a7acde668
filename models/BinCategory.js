const mongoose = require("mongoose");
const {Schema} = require("mongoose");

// Define the CardBin schema
const binCategory = new mongoose.Schema({
    category: {
        type: String, required: true,
    },
    version: {
        type: String, default: null
    },
    bin_prefix: {
        type: String,
        required: [true, "Bin code prefix is required"],
        trim: true,
    },
    currency: {
        type: Schema.Types.ObjectId,
        ref: "ProductCurrency",
        required: [true, "Currency is required"],
    },
    status: {
        type: String, enum: ['active', 'inactive', 'pending'], // Define possible values for status
        default: 'pending' // Set a default status if needed
    }, created_by: {type: Schema.Types.ObjectId, ref: 'User', required: true}, // Add created_by field
    created_at: {type: Date, default: Date.now}, // Set created_at with default value
    deleted_at: {type: Date, default: null} // Set deleted_at, default null
});

// Create and export the model
module.exports = mongoose.model("binCategory", binCategory);
