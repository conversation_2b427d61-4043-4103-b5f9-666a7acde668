const mongoose = require("mongoose");
const {Schema} = require("mongoose");

// Define the CardType schema
const BinTypeSchema = new mongoose.Schema({
    type: {
        type: String,
        required: true,
    },
    programmeType: {type: Schema.Types.ObjectId, ref: 'ProgrammeType', required: true},
    binVariant: {type: Schema.Types.ObjectId, ref: 'binVariant', required: true},
    binCategory: {type: Schema.Types.ObjectId, ref: 'binCategory', required: true},

    status: {
        type: String, enum: ['active', 'inactive', 'pending', 'modify', "decline"], default: 'pending'
    },
    version: {
        type: String, default: null
    },
    reason: {
        type: String, default: null
    },
    company: {type: Schema.Types.ObjectId, ref: 'Company'},

    created_by: {type: Schema.Types.ObjectId, ref: 'User', required: true}, // Add created_by field
    created_at: {type: Date, default: Date.now}, // Set created_at with default value
    deleted_at: {type: Date, default: null} // Set deleted_at, default null
});

// Create and export the model
module.exports = mongoose.models.BinType || mongoose.model('BinType', BinTypeSchema)
