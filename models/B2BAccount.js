const mongoose = require("mongoose")
const { Schema } = mongoose
const PermissionLogsSchema = new mongoose.Schema({
    action: { type: String, required: true },  // e.g., "Permission Update", "User Created"
    details: { type: String },                 // extra info about the change
    username: { type: String },                // person's full name
    email: { type: String },                   // person's email
    userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User' }, // store ObjectId
}, { timestamps: true });

const PermissionAuditSchema = new mongoose.Schema({
    key: { type: String, required: true },
    enabled: { type: Boolean, required: true }
}, { _id: false });

// Define the address schema as a sub-document
const addressSchema = new Schema({
    type: {
        type: String,

        default: "registration_address",
        trim: true,
    },
    street: {
        type: String,
        required: [true, "Street is required"],
        trim: true,
    },
    buildingNumber: {
        type: String,
        required: [true, "Building number is required"],
        trim: true,
    },
    apartmentNumber: {
        type: String,
        trim: true,
    },
    city: {
        type: String,
        required: [true, "City is required"],
        trim: true,
    },
    zipCode: {
        type: String,
        required: [true, "ZIP code is required"],
        trim: true,
        validate: {
            validator: (v) => {
                // Basic ZIP code validation (can be enhanced for specific country formats)
                return /^[0-9a-zA-Z\s-]{4,10}$/.test(v)
            },
            message: (props) => `${props.value} is not a valid ZIP/postal code!`,
        },
    },
    country: {
        type: String,
        required: [true, "Country is required"],
        trim: true,
    },
})

// Define the main B2B account schema
const b2bAccountSchema = new Schema(
    {
        // ===== EXISTING FIELDS =====
        companyName: {
            type: String,
            required: [true, "Company name is required"],
            trim: true,
        },
        clientCode: {
            type: String,
            required: [true, "Client code is required"],
            trim: true,
            unique: true,
        },
        phoneNumber: {
            type: String,
            required: [true, "Phone number is required"],
            trim: true,
        },
        authPhoneNumber: {
            type: String,
            required: [true, "Authorization phone number is required"],
            trim: true,
        },
        email: {
            type: String,
            required: [true, "Email is required"],
            trim: true,
            lowercase: true,
            validate: {
                validator: (v) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(v),
                message: (props) => `${props.value} is not a valid email address!`,
            },
        },
        addresses: {
            type: [addressSchema],
            required: true,
        },
        nip: {
            type: String,
            required: [true, "NIP (Tax ID) is required"],
            trim: true,
            validate: {
                validator: (v) => /^\d{10}$/.test(v),
                message: (props) => `${props.value} is not a valid NIP! NIP must be 10 digits.`,
            },
        },
        regon: {
            type: String,
            required: [true, "REGON is required"],
            trim: true,
            validate: {
                validator: (v) => /^(\d{9}|\d{14})$/.test(v),
                message: (props) => `${props.value} is not a valid REGON! REGON must be 9 or 14 digits.`,
            },
        },
        embossedName: {
            type: String,
            required: [true, "Embossed name is required"],
            trim: true,
        },
        company: {
            type: Boolean,
            default: true,
        },
        customer: {
            type: Boolean,
            default: false,
        },
        parentCompany: {
            type: mongoose.Schema.Types.ObjectId,
            ref: "Company",
            required: function () {
                return this.company === true
            },
        },
        status: {
            type: String,
            enum: ["active", "inactive", "pending", "approved", "rejected", "suspended"],
            default: "pending",
        },
        products: [{ type: mongoose.Schema.Types.ObjectId, ref: "ProductVersion" }],

        // ===== NEW FIELDS FROM FORM =====
        permissions: { type: [String], default: [] },
        permissionAudit: { type: [PermissionAuditSchema], default: [] },
        permissionsLog: { type: [PermissionLogsSchema], default: [] },
        // Extended Company Information
        companyIndustry: {
            type: String,
            trim: true,
            maxlength: [100, "Company industry cannot exceed 100 characters"],
        },
        companyNumber: {
            type: String,
            trim: true,
            maxlength: [50, "Company registration number cannot exceed 50 characters"],
        },
        registrationDate: {
            type: Date,
            validate: {
                validator: (v) => !v || v <= new Date(),
                message: "Registration date cannot be in the future",
            },
        },
        contactName: {
            type: String,
            trim: true,
            maxlength: [100, "Contact name cannot exceed 100 characters"],
        },
        contactRole: {
            type: String,
            trim: true,
            maxlength: [100, "Contact role cannot exceed 100 characters"],
        },
        countryOfIncorporation: {
            type: String,
            trim: true,
            maxlength: [100, "Country of incorporation cannot exceed 100 characters"],
        },
        companyWebsite: {
            type: String,
            trim: true,
            validate: {
                validator: (v) => {
                    if (!v) return true // Allow empty values
                    try {
                        new URL(v)
                        return true
                    } catch (e) {
                        return false
                    }
                },
                message: "Please enter a valid website URL",
            },
        },

        // Card Programme Purpose
        typeOfBusiness: {
            type: String,
            trim: true,
            maxlength: [100, "Type of business cannot exceed 100 characters"],
        },
        cardUsage: {
            type: String,
            trim: true,
            maxlength: [500, "Card usage description cannot exceed 500 characters"],
        },
        cardholderGroups: {
            type: String,
            trim: true,
            maxlength: [200, "Cardholder groups cannot exceed 200 characters"],
        },
        fundLoading: {
            type: Number,
            min: [0, "Fund loading cannot be negative"],
            validate: {
                validator: (v) => v === null || v === undefined || (typeof v === "number" && v >= 0),
                message: "Fund loading must be a positive number",
            },
        },

        // Merchant Business Case Details
        businessSector: {
            type: String,
            trim: true,
            maxlength: [100, "Business sector cannot exceed 100 characters"],
        },
        regions: {
            type: String,
            trim: true,
            maxlength: [200, "Regions cannot exceed 200 characters"],
        },
        countries: {
            type: String,
            trim: true,
            maxlength: [200, "Countries cannot exceed 200 characters"],
        },
        businessPurpose: {
            type: String,
            trim: true,
            maxlength: [1000, "Business purpose cannot exceed 1000 characters"],
        },
        cardUserGroups: {
            type: String,
            trim: true,
            maxlength: [200, "Card user groups cannot exceed 200 characters"],
        },
        numberOfCards: {
            type: Number,
            min: [0, "Number of cards cannot be negative"],
            validate: {
                validator: (v) => v === null || v === undefined || (Number.isInteger(v) && v >= 0),
                message: "Number of cards must be a positive integer",
            },
        },
        monthlyLoadingValue: {
            type: Number,
            min: [0, "Monthly loading value cannot be negative"],
            validate: {
                validator: (v) => v === null || v === undefined || (typeof v === "number" && v >= 0),
                message: "Monthly loading value must be a positive number",
            },
        },

        // Administrator Information
        adminName: {
            type: String,
            trim: true,
            maxlength: [100, "Administrator name cannot exceed 100 characters"],
        },
        adminRole: {
            type: String,
            trim: true,
            maxlength: [100, "Administrator role cannot exceed 100 characters"],
        },
        adminEmail: {
            type: String,
            trim: true,
            lowercase: true,
            validate: {
                validator: (v) => {
                    if (!v) return true // Allow empty values
                    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(v)
                },
                message: "Please enter a valid administrator email address",
            },
        },
        adminPhone: {
            type: String,
            trim: true,
            maxlength: [20, "Administrator phone cannot exceed 20 characters"],
        },

        // Additional metadata
        applicationSource: {
            type: String,
            enum: ["web_form", "api", "admin_panel", "import"],
            default: "web_form",
        },

        // Approval workflow
        approvalNotes: {
            type: String,
            trim: true,
            maxlength: [1000, "Approval notes cannot exceed 1000 characters"],
        },
        approvedBy: {
            type: mongoose.Schema.Types.ObjectId,
            ref: "User",
        },
        approvedAt: {
            type: Date,
        },
        rejectedBy: {
            type: mongoose.Schema.Types.ObjectId,
            ref: "User",
        },
        rejectedAt: {
            type: Date,
        },
        rejectionReason: {
            type: String,
            trim: true,
            maxlength: [500, "Rejection reason cannot exceed 500 characters"],
        },

        // External API integration
        externalClientId: {
            type: String,
            trim: true,
        },
        externalApiResponse: {
            type: Schema.Types.Mixed, // Store the full API response
        },
        lastSyncAt: {
            type: Date,
        },
    },
    {
        timestamps: true, // Automatically manage createdAt and updatedAt fields
    },
)

// ===== INDEXES =====
// Existing indexes
b2bAccountSchema.index({ companyName: 1 })
b2bAccountSchema.index({ email: 1 })
b2bAccountSchema.index({ nip: 1 })
b2bAccountSchema.index({ parentCompany: 1 })

// New indexes for better query performance
b2bAccountSchema.index({ status: 1 })
b2bAccountSchema.index({ clientCode: 1 })
b2bAccountSchema.index({ companyNumber: 1 })
b2bAccountSchema.index({ adminEmail: 1 })
b2bAccountSchema.index({ createdAt: -1 })
b2bAccountSchema.index({ approvedAt: -1 })
b2bAccountSchema.index({ businessSector: 1 })

// Compound indexes for common queries
b2bAccountSchema.index({ status: 1, createdAt: -1 })
b2bAccountSchema.index({ parentCompany: 1, status: 1 })

// ===== MIDDLEWARE =====
// Pre-save middleware to update timestamps and handle status changes
b2bAccountSchema.pre("save", function (next) {
    // Handle approval/rejection timestamps
    if (this.isModified("status")) {
        if (this.status === "approved" && !this.approvedAt) {
            this.approvedAt = new Date()
        } else if (this.status === "rejected" && !this.rejectedAt) {
            this.rejectedAt = new Date()
        }
    }

    // Update lastSyncAt when external data changes
    if (this.isModified("externalApiResponse") || this.isModified("externalClientId")) {
        this.lastSyncAt = new Date()
    }

    next()
})

// ===== INSTANCE METHODS =====
// Method to get the registration address
b2bAccountSchema.methods.getRegistrationAddress = function () {
    return this.addresses.find((addr) => addr.type === "registration_address")
}

// Method to get the delivery address
b2bAccountSchema.methods.getDeliveryAddress = function () {
    return this.addresses.find((addr) => addr.type === "delivery_address")
}

// Method to check if account is fully completed
b2bAccountSchema.methods.isComplete = function () {
    const requiredFields = ["companyName", "email", "phoneNumber", "nip", "regon", "embossedName", "addresses"]

    return requiredFields.every((field) => {
        const value = this[field]
        if (Array.isArray(value)) {
            return value.length > 0
        }
        return value && value.toString().trim().length > 0
    })
}

// // Method to get account summary for API responses
// b2bAccountSchema.methods.getSummary = function () {
//     return {
//         id: this._id,
//         companyName: this.companyName,
//         email: this.email,
//         clientCode: this.clientCode,
//         status: this.status,
//         companyIndustry: this.companyIndustry,
//         businessSector: this.businessSector,
//         createdAt: this.createdAt,
//         updatedAt: this.updatedAt,
//         isComplete: this.isComplete(),
//     }
// }

// ===== STATIC METHODS =====
// Static method to find accounts by status
b2bAccountSchema.statics.findByStatus = function (status) {
    return this.find({ status }).sort({ createdAt: -1 })
}

// Static method to find accounts pending approval
b2bAccountSchema.statics.findPendingApproval = function () {
    return this.find({ status: "pending" }).sort({ createdAt: 1 })
}

// Static method to get accounts statistics
b2bAccountSchema.statics.getStatistics = function () {
    return this.aggregate([
        {
            $group: {
                _id: "$status",
                count: { $sum: 1 },
            },
        },
        {
            $group: {
                _id: null,
                total: { $sum: "$count" },
                statusBreakdown: {
                    $push: {
                        status: "$_id",
                        count: "$count",
                    },
                },
            },
        },
    ])
}

// ===== VIRTUAL FIELDS =====
// Virtual for full company display name
b2bAccountSchema.virtual("displayName").get(function () {
    return `${this.companyName} (${this.clientCode})`
})

// Virtual for admin contact info
b2bAccountSchema.virtual("adminContact").get(function () {
    if (!this.adminName) return null
    return {
        name: this.adminName,
        role: this.adminRole,
        email: this.adminEmail,
        phone: this.adminPhone,
    }
})

// Ensure virtual fields are serialized
b2bAccountSchema.set("toJSON", { virtuals: true })
b2bAccountSchema.set("toObject", { virtuals: true })

// Create and export the model
const B2BAccount = mongoose.model("B2BAccount", b2bAccountSchema)

module.exports = B2BAccount
