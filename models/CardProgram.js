const mongoose = require('mongoose');
const Schema = mongoose.Schema;

// CardProgram Schema Definition
const cardProgramSchema = new Schema({
    company: {type: Schema.Types.ObjectId, ref: 'Company' }, // Referring to Company model
    cardScheme: {type: Schema.Types.ObjectId, ref: 'CardScheme'}, // Referring to CardScheme model
    binRange: {type: String},
    programmeType: {type: Schema.Types.ObjectId, ref: 'ProgrammeType'}, // Referring to CardProgrammeType model
    programManagerType: {type: Schema.Types.ObjectId, ref: 'programmeManagerType'}, // Referring to CardProgrammeType model
    binType: {type: Schema.Types.ObjectId, ref: 'BinType',},
    binRangeId: {type: Schema.Types.ObjectId, ref: 'BinRange',},
    productVersionName: [{
        type: mongoose.Schema.Types.ObjectId,
        ref: 'ProductVersion',
        default: null
    }],

    status: {
        type: String,
        enum: ['active', 'inactive', 'pending'], // Define possible values for status
        default: 'pending' // Set a default status if needed
    },
    created_by: {type: Schema.Types.ObjectId, ref: 'User',}, // Add created_by field
    created_at: {type: Date, default: Date.now}, // Set created_at with default value
    deleted_at: {type: Date, default: null} // Set deleted_at, default null
}, {timestamps: true});
// cardProgramSchema.index({company: 1}, {unique: true});

module.exports = mongoose.models.CardProgram || mongoose.model('CardProgram', cardProgramSchema)
