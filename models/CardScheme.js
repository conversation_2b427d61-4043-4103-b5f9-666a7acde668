const mongoose = require("mongoose");
const {Schema} = require("mongoose");

// Define the CardScheme schema
const cardSchemeSchema = new mongoose.Schema({
    scheme_name: {
        type: String,
        required: true,
    },
    scheme_code: {
        type: String,
    },
    status: {
        type: String,
        enum: ['active', 'inactive', 'pending','modify',"decline"], // Define possible values for status
        default: 'pending' // Set a default status if needed
    },
    version: {
        type: String, default: null
    },

    reason: {
        type: String, default: null
    },
    created_by: {type: Schema.Types.ObjectId, ref: 'User', required: true}, // Add created_by field
    created_at: {type: Date, default: Date.now}, // Set created_at with default value
    deleted_at: {type: Date, default: null} // Set deleted_at, default null
});

// Create and export the model
module.exports = mongoose.model("CardScheme", cardSchemeSchema);
