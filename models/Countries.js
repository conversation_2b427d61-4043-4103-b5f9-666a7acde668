// models/Country.js

const mongoose = require("mongoose")

const CountrySchema = new mongoose.Schema({
    country_name: { type: String, required: true },
    country_code: { type: String, required: true },
    currency_code: { type: String, required: true },
    is_active: { type: Boolean, default: true },
    status: {
        type: String,
        enum: ['active', 'inactive', 'pending','modify',"decline"], // Define possible values for status
        default: 'pending' // Set a default status if needed
    },
    version: {
        type: String, default: null
    },

    reason: {
        type: String, default: null
    },
    created_by: { type: String, required: true }, // Add created_by field
    created_at: { type: Date, default: Date.now }, // Set created_at with default value
    deleted_at: { type: Date, default: null } // Set deleted_at, default null
})

module.exports = mongoose.model("Country", CountrySchema)
