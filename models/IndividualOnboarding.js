const mongoose = require("mongoose");
const {Schema} = require("mongoose");

const PermissionLogsSchema = new mongoose.Schema({
    action: { type: String, required: true },  // e.g., "Permission Update", "User Created"
    details: { type: String },                 // extra info about the change
    username: { type: String },                // person's full name
    email: { type: String },                   // person's email
    userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User' }, // store ObjectId
}, { timestamps: true });

const PermissionAuditSchema = new mongoose.Schema({
    key: { type: String, required: true },
    enabled: { type: Boolean, required: true }
}, { _id: false });


// Define the schema for individual onboarding
const IndividualOnboardingSchema = new mongoose.Schema({
    // Product Selection
    origin: {
        type: String,
        required: false,
        default: "Onboarding Form",
        enum: ['API', 'Onboarding Form']
    },
    company: {type: Schema.Types.ObjectId, ref: 'Company' },
    productVersion: [{
        type: mongoose.Schema.Types.ObjectId,
        ref: 'ProductVersion',
        default: null,required: false,
    }],

    // Personal Information
    personalInfo: {
        firstName: {
            type: String,
            required: true,
            trim: true
        },
        middleName: {
            type: String,
            trim: true
        },
        lastName: {
            type: String,
            required: true,
            trim: true
        }, mothersMaidenName: {
            type: String,
            required: false,
            trim: true
        },
        email: {
            type: String,
            required: true,
            trim: true
        },
        phone: {
            type: String,
            required: true,
            trim: true
        },
        authPhoneNumber: {
            type: String,
            required: false,
            trim: true
        },
        dateOfBirth: {
            type: Date,
            required: true
        },birthCountry: {
            type: String,
            required: false,
            trim: true
        }
    },

    // Address Details
    address: {
        street: {
            type: String,
            required: false,
            trim: true
        },
        buildingNumber: {
            type: String,
            trim: true
        },
        apartmentNumber: {
            type: String,
            trim: true
        },
        city: {
            type: String,
            required: false,
            trim: true
        },
        stateProvince: {
            type: String,
            trim: true
        },
        zipCode: {
            type: String,
            trim: true
        },
        country: {
            type: String,
            required: false,
            trim: true
        }
    },

    operationStatus: {
        default: "Active",
        type: String
    },

    // ID Document Information
    idDocument: {
        customerIdType: {
            type: String,
            required: true
        },
        number: {
            type: String,
            required: true,
            trim: true
        },
        issueDate: {
            type: Date,
            required: false
        },
        expiryDate: {
            type: Date,
            required: false
        },
        issuingCountry:{
            type: String,
            trim: true
        },

        frontImagePath: {
            type: String,
            trim: true
        }, backImagePath: {
            type: String,
            trim: true
        }, idAuthority: {
            type: String,
            trim: true
        },
    },

    // Tax Information
    taxInfo: {
        country: {
            type: String,
            required: true,
            trim: true
        },
        taxIdNumber: {
            type: String,
            required: true,
            trim: true
        }
    },

    clientID: {
        type: String,
        required: true,
        trim: true
    },

    cardTypes: [String],

    cardCurrency: [{
        type: mongoose.Schema.Types.ObjectId,
        ref: 'ProductCurrency',
    }],

    client: {
        type: Boolean,
        default: false
    },
    permissions: { type: [String], default: [] },
    permissionAudit: { type: [PermissionAuditSchema], default: [] },
    permissionsLog: { type: [PermissionLogsSchema], default: [] },
    // Newly added fields
    legalId: {
        type: String,
        required: false,
        trim: true
    },
    citizenship: {
        type: String,
        required: false,
        trim: true
    },

    applicationId: {
        type: String,
        required: false,
        trim: true
    },

    riskLevel: {
        type: Number,
        required: false,
        min: 0,
        max: 999
    },

    riskStatus: {
        type: String,
        required: false,
        trim: true,
        maxlength: 9
    },

    applicationID: {
        type: String,
        required: false,
        trim: true,
        maxlength: 40
    },

    applicationStatus: {
        type: String,
        required: false,
        trim: true,
        maxlength: 10
    },

    applicationDate: {
        type: Date,
        required: false
    },
    dashboardStatus: {
        type: String,
        required: false
    },
    userType: {
        type: String,
        required: false
    },
    b2bClient: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'B2BAccount',
    },

    // Timestamps for tracking
    createdAt: {
        type: Date,
        default: Date.now
    }

}, {timestamps: true});

// Create and export the model
const IndividualOnboarding = mongoose.model('IndividualOnboarding', IndividualOnboardingSchema);

module.exports = IndividualOnboarding;
