const mongoose = require('mongoose');
const {Schema} = require("mongoose");

const OwnerSchema = new mongoose.Schema({
    clientCode: {
        type: String, required: true,
    }, relationship: {
        type: String, required: true, enum: ['OWN'], // Define expected values for relationship
    }, mainOwner: {
        type: Boolean, required: true,
    },
});

const AccountSchema = new mongoose.Schema({
    accNo: {
        type: String, required: true
    }, status: {
        type: String, required: true, enum: ['ACTIVE', 'INACTIVE', 'CLOSED'], // Define status options
    }, currencyCode: {
        type: String, required: true,
    }, currencyName: {
        type: String, required: true,
    }, owners: {
        type: [OwnerSchema], required: true,
    }, onboarding: {type: Schema.Types.ObjectId, ref: 'IndividualOnboarding', required: true},
}, {
    timestamps: true, // Adds `createdAt` and `updatedAt` timestamps
});

const Account = mongoose.model('Account', AccountSchema);

module.exports = Account;
