const mongoose = require('mongoose');
const {Schema} = require("mongoose");



const OnboardingSchema = new mongoose.Schema({
    selectedPurposes: [String],
    occupation: String,
    annualIncome: Number,
    incomingTransfers: String,
    outgoingTransfers: String,
    incomingVolume: String,
    personal: {type: Schema.Types.ObjectId, ref: 'IndividualOnboarding'},
    outgoingVolume: String,

    incomingDestinations: [String],
    outgoingDestinations: [String],
    createdAt: { type: Date, default: Date.now },
});


module.exports = mongoose.model('CipOnboarding', OnboardingSchema);
