const mongoose = require('mongoose');

// Define the schema for weight and rate pairs
const weightRateSchema = new mongoose.Schema({
    weight: {
        type: Number,
        required: true,
        min: 0
    },
    rate: {
        type: Number,
        required: true,
        min: 0
    }
});

// Define the main Polish Post schema
const polishPostSchema = new mongoose.Schema({
    zoneId: {
        type: String,
        required: true,
        enum: ['europe', 'north-america-africa', 'south-central-america-asia', 'australia-oceania']
    },
    zoneDescription: {
        type: String,
        required: false
    },
    deliveryTime: {
        type: String,
        required: true
    },
    methodId: {
        type: String,
        required: true,
        default: 'standard'
    },
    trackingDays: {
        type: Number,
        required: true,
        min: 1,
        max: 90,
        default: 14
    },
    weightRates: {
        type: [weightRateSchema],
        required: true,
        validate: {
            validator: function (weightRates) {
                return weightRates && weightRates.length > 0;
            },
            message: 'At least one weight/rate pair is required'
        }
    },
    isActive: {
        type: Boolean,
        default: true
    },
    createdAt: {
        type: Date,
        default: Date.now
    },
    updatedAt: {
        type: Date,
        default: Date.now
    }
}, {timestamps: {updatedAt: 'updatedAt'}});

// Add a pre-save hook to update zoneDescription based on zoneId
polishPostSchema.pre('save', function (next) {
    const zoneDescriptions = {
        'europe': 'Europe',
        'north-america-africa': 'North America/Africa',
        'south-central-america-asia': 'South/Central America/Asia',
        'australia-oceania': 'Australia/Oceania'
    };

    this.zoneDescription = zoneDescriptions[this.zoneId] || this.zoneId;
    next();
});

// Create and export the model
const PolishPost = mongoose.model('PolishPost', polishPostSchema);

module.exports = PolishPost;