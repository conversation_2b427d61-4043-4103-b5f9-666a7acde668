const mongoose = require("mongoose");
const {Schema} = require("mongoose");

// Define the CardType schema
const cardTypeSchema = new mongoose.Schema({
    type: {
        type: String,
        required: true,
    },
    code: {
        type: String,
        required: true,
    },
    binCode: {
        type: String,
        required: true,
    },


    binVariant: {type: Schema.Types.ObjectId, ref: 'binVariant', required: true},
    binCategory: {type: Schema.Types.ObjectId, ref: 'binCategory', required: true},

    status: {
        type: String, enum: ['active', 'inactive', 'pending', 'modify', "decline"], default: 'pending'
    },
    version: {
        type: String, default: null
    },  reason: {
        type: String, default: null
    },
    binCodePrefix: {
        type: String
    },
    binCodeSuffix: {
        type: String
    }, bin_start: {
        type: String
    }, bin_end: {
        type: String
    },
    currency: {type: Schema.Types.ObjectId, ref: 'ProductCurrency', required: true},
    created_by: {  type: Schema.Types.ObjectId, ref: 'User' , required: true }, // Add created_by field
    created_at: { type: Date, default: Date.now }, // Set created_at with default value
    deleted_at: { type: Date, default: null } // Set deleted_at, default null
});

// Create and export the model
 module.exports = mongoose.models.CardType || mongoose.model('CardType', cardTypeSchema)
