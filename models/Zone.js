const mongoose = require("mongoose")

// Define the Zone schema
const ZoneSchema = new mongoose.Schema(
    {
        name: {
            type: String,
            required: [true, "Please provide a zone name"],
            trim: true,
        },
        number: {
            type: String,
            required: [true, "Please provide a zone number"],
            trim: true,
        },
        countries: {
            type: [String],
            default: [],
        },
        deliveryMethods: {
            type: [String],
            default: [],
        },
    },
    { timestamps: true }
)

// Create and export the Zone model
module.exports = mongoose.model("Zone", ZoneSchema)
