// Define the Contact schema
const {Schema} = require("mongoose");
const mongoose = require("mongoose");
const contactScheme = new mongoose.Schema(
    {
        name: {
            type: String,
            required: [true, "Contact name is required"],
            trim: true,
            minlength: [2, "Name must be at least 2 characters"],
        },
        role: {
            type: String,
            required: [true, "Contact role is required"],
            trim: true,
         },
        email: {
            type: String,
            required: [true, "Email address is required"],
            trim: true,
            lowercase: true,
            },
        phone: {
            type: String,
            required: [true, "Phone number is required"],
            trim: true,
            minlength: [10, "Phone number must be at least 10 characters"],
        },
        contactType: {
            type: String,
            required: [true, "Contact type is required"],
            enum: {
                values: ["primary", "technical", "financial", "customer", "other"],
                message: "{VALUE} is not a valid contact type",
            },
            default: "primary",
        },
        notes: {
            type: String,
            trim: true,
        },
        company: {
            type: Schema.Types.ObjectId,
            ref: "Company",
            required: [true, "Company ID is required"],
        },
    },
    {
        timestamps: true, // Automatically add createdAt and updatedAt fields
        toJSON: {virtuals: true}, // Include virtuals when document is converted to JSON
        toObject: {virtuals: true}, // Include virtuals when document is converted to object
    },
)

module.exports = mongoose.models.CompanyContact || mongoose.model("CompanyContact", contactScheme);
