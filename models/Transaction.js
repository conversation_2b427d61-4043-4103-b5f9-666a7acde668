
const mongoose = require("mongoose");
const {Schema} = require("mongoose");
// Transaction Schema
const TransactionSchema = new mongoose.Schema({
    accountNumber: { type: String, required: true },
    amount: { type: Number, required: true },
    transactionType: { type: String, enum: ["credit", "debit"], required: true },
    date: { type: Date, default: Date.now },
    description: { type: String }
}, { timestamps: true });


module.exports = mongoose.models.Transaction || mongoose.model('Transaction', TransactionSchema)
