# 🚀 Deployment Documentation Prompt

You are an **expert Dev<PERSON>ps engineer**.
Your mission is to create a **complete deployment guide** for a Node.js backend project.
The guide must cover **multiple deployment strategies** (<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>er Compose, PM2/systemd, Kubernetes).
It should also generate the **required configuration files** (`Docker<PERSON>le`, `docker-compose.yml`, `portainer-stack.yml`, `systemd.service`, `k8s manifests`, etc.)

---

## 📦 Project Context (to be filled by user)

- **Project Name:** `{{PROJECT_NAME}}`
- **Repo URL / Path:** `{{REPO_URL}}`
- **Runtime:** `Node {{NODE_VERSION}}`
- **Package Manager:** `npm | pnpm | yarn`
- **Build Command:** `{{BUILD_CMD}}`
- **Start Command:** `{{START_CMD}}`
- **Internal Port:** `{{PORT}}`
- **Healthcheck Endpoint:** `{{HEALTH_ENDPOINT}}`
- **Required Env Vars:** `{{ENV_VARS}}`
- **Secrets:** `{{SECRETS_LIST}}`
- **Databases / Queues:** `{{DB_AND_QUEUES}}`
- **Persistent Volumes:** `{{VOLUMES}}`
- **Image Name:** `{{REGISTRY}}/{{IMAGE_NAME}}:{{TAG}}`
- **Domain / TLS Provider:** `{{DOMAIN}} / {{TLS_PROVIDER}}`

---

## 📂 Expected Output

```
/deployment/
├── DEPLOYMENT.md         # Step-by-step deployment guide
├── ENVIRONMENT.md        # Env vars & secrets documentation
├── Dockerfile            # Multi-stage Docker build
├── docker-compose.yml    # Compose file (with DB, cache, etc.)
├── portainer-stack.yml   # Stack for Portainer
├── k8s/
│   ├── deployment.yaml
│   ├── service.yaml
│   ├── ingress.yaml
│   ├── configmap.yaml
│   └── secret.example.yaml
├── systemd/
│   └── {{PROJECT_NAME}}.service
└── nginx/
    └── reverse-proxy.conf
```

---

## 📑 Instructions

1. **DEPLOYMENT.md**

   - Pre-deployment checklist (Node, lockfile, `.dockerignore`, envs, secrets, volumes).
   - Local build & test.
   - Docker build & push steps.
   - Running with raw Docker.
   - Deploy with Docker Compose (scaling, logs, volumes).
   - Deploy with Portainer (stack config, update, rollback).
   - Deploy with PM2/systemd (non-containerized option).
   - Deploy with Kubernetes (apply manifests, scaling, probes).
   - TLS setup (Nginx/Traefik, Let’s Encrypt).
   - Database migrations & backups.
   - Monitoring & logging.
   - Rollback strategies & troubleshooting.

2. **ENVIRONMENT.md**

   - Table of all env vars (required, default, description).
   - Secrets handling (Docker secrets, Portainer, K8s Secret).
   - Validation example (Joi/Zod snippet).

3. **Dockerfile**

   - Multi-stage (build + run).
   - Non-root user.
   - Healthcheck with curl.
   - Production install (`npm ci --only=production`).

4. **docker-compose.yml**

   - App container + database + cache + reverse proxy (if needed).
   - Volumes for persistent data.
   - Restart policies & scaling examples.

5. **portainer-stack.yml**

   - Ready to paste in Portainer stack UI.
   - Env vars & secrets placeholders.

6. **Kubernetes Manifests**

   - Deployment, Service, Ingress.
   - ConfigMap for env vars.
   - Secret example (base64 values).
   - Resource limits, probes.

7. **systemd/{{PROJECT\_NAME}}.service**

   - Systemd service file for bare-metal deployment.

8. **nginx/reverse-proxy.conf**

   - TLS termination, reverse proxy, redirects.

---

## 📢 Output Requirements

- Generate **Markdown docs + config files**.
- Include **step-by-step explanations** for each deployment path.
- Make it **copy-paste ready**.
- Always explain **where to insert secrets/env vars**.
- Add **best practices** (security, scaling, logging, monitoring).
- If something doesn’t apply → state **“Not Applicable”** with explanation.
