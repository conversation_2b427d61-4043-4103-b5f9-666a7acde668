const express = require('express');
const { createIndividualOnboarding,uploadIdDocumentImages } = require("../controllers/onboardingPersonalController");
const router = express.Router();
const IndividualOnboarding = require('../models/IndividualOnboarding');
const Transaction = require('../models/Transaction');
const Account = require('../models/Account');
const {sendPostRequest, sendPutRequest} = require("../config/ApiInstense");
const Card = require("../models/AccountCard");
const {validationResult, body} = require('express-validator');
const validationRules = require('../config/ValidationRules');
const AccountCard = require("../models/AccountCard");
const sendCardCreationWebhook = require("../config/webhook"); // Import webhook handler

// ✅ Create a new client (No KYC verification during enrollment)
router.post('/clients',   createIndividualOnboarding);

router.post("/clients/:clientCode/id-documents", uploadIdDocumentImages)
// ✅ Get card-associated client operation status
router.get('/clients/:clientCode/operationStatus', async (req, res) => {
    try {
        const { clientCode } = req.params;

        // Fetch client by clientID
        const onboardingData = await IndividualOnboarding.findOne({ clientID: clientCode })
            .populate('cardCurrency');

        if (!onboardingData) {
            return res.status(404).json({ error: 'Client not found' });
        }

        res.json({ status: onboardingData.operationStatus });
    } catch (error) {
        console.error('Error retrieving operation status:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});
// ✅ Update customer personal information (Only for individual customers)
router.patch('/clients/:clientCode/personal', async (req, res) => {
    try {
        const { clientCode } = req.params;
        let updateData = req.body; // Partial data submitted by the user

        // Find the existing record
        const existingCustomer = await IndividualOnboarding.findOne({ clientID: clientCode });

        if (!existingCustomer) {
            return res.status(404).json({ error: 'Client not found' });
        }

        // Map incoming API field names to DB field names
        const mappedUpdateData = {
            firstName: updateData.firstName || existingCustomer.personalInfo.firstName,
            middleName: updateData.secondName || existingCustomer.personalInfo.middleName, // Mapping `secondName` → `middleName`
            lastName: updateData.lastName || existingCustomer.personalInfo.lastName,
            mothersMaidenName: updateData.mothersMaidenName || existingCustomer.personalInfo.mothersMaidenName,
            dateOfBirth: updateData.birthDate || existingCustomer.personalInfo.dateOfBirth, // Mapping `birthDate` → `dateOfBirth`
            email: updateData.email || existingCustomer.personalInfo.email,
            phone: updateData.phoneNumber || existingCustomer.personalInfo.phone, // Mapping `phoneNumber` → `phone`
            authPhoneNumber: updateData.authPhoneNumber || existingCustomer.personalInfo.authPhoneNumber,
            birthCountry: updateData.birthCountry || existingCustomer.personalInfo.birthCountry
        };

        // Merge existing `personalInfo` with the mapped new data
        existingCustomer.personalInfo = {
            ...existingCustomer.personalInfo.toObject(),
            ...mappedUpdateData
        };
        await existingCustomer.save();

        res.json({
            message: 'Customer personal information updated successfully',
            updatedCustomer: existingCustomer
        });
    } catch (error) {
        console.error('Error updating customer info:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});
router.patch('/clients/:clientCode/kyc', async (req, res) => {
    try {
        const { clientCode } = req.params;
        const updateData = req.body; // Partial data submitted by the user

        // Find the existing record
        let existingCustomer = await IndividualOnboarding.findOne({ clientID: clientCode });

        if (!existingCustomer) {
            return res.status(404).json({ error: 'Client not found' });
        }

        // Update only the provided fields
        Object.keys(updateData).forEach((key) => {
            existingCustomer[key] = updateData[key];
        });

        // Save the updated record
        await existingCustomer.save();

        res.json({
            message: 'KYC information updated successfully',
            updatedCustomer: existingCustomer
        });
    } catch (error) {
        console.error('Error updating KYC info:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});



// ✅ Update customer identity document (Only for individual customers)
router.put('/clients/:clientCode/id', async (req, res) => {
    try {
        const { clientCode } = req.params;
        const updateData = req.body; // Partial update data

        // Find the client by clientID
        let existingClient = await IndividualOnboarding.findOne({ clientID: clientCode });

        if (!existingClient) {
            return res.status(404).json({ error: 'Client not found' });
        }

        // Update fields directly
        Object.keys(updateData).forEach((key) => {
            existingClient[key] = updateData[key];
        });

        // Save the updated document
        await existingClient.save();

        res.json({ message: 'Customer ID updated successfully', updatedClient: existingClient });
    } catch (error) {
        console.error('Error updating customer ID:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// ✅ Update client contact information
router.patch('/clients/:clientCode/contact', async (req, res) => {
    try {
        const { clientCode } = req.params;
        const updateData = req.body; // `{ phone, email }`

        const existingClient = await IndividualOnboarding.findOne({ clientID: clientCode });

        if (!existingClient) {
            return res.status(404).json({ error: 'Client not found' });
        }

        existingClient.personalInfo = {
            ...existingClient.personalInfo.toObject(),
            ...updateData
        };


//         const dataToSend = `{
//   "phoneNumber": "${updateData.phone}",
//   "email": "${updateData.email}"
// }`
        const request = {
            phoneNumber: updateData.phone,
            email: updateData.email
        }


        const url = `https://apifintech.sandbox.itcardpaymentservice.pl:30080/v1/clients/${clientCode}/contact`;

        const apiResponse = await sendPutRequest(url, JSON.stringify(request));


        if (apiResponse.status !== "APPROVED") {
            return res.status(500).json({
                success: false,
                message: "Error while changing contact information"
            });
        } else {
            await existingClient.save();
            res.json({message: 'Client contact information updated successfully', updatedClient: existingClient});

        }


    } catch (error) {
        console.error('Error updating contact info:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// ✅ Update client address information
router.put('/clients/:clientCode/address', async (req, res) => {


    try {
        const { clientCode } = req.params;
        const updateData = req.body; // `{ address }`

        const existingClient = await IndividualOnboarding.findOne({ clientID: clientCode });

        if (!existingClient) {
            return res.status(404).json({ error: 'Client not found' });
        }

        existingClient.address = {
            ...existingClient.address.toObject(),
            ...updateData
        };
        const request = {
            address: {
                street: updateData.street,
                buildingNumber: updateData.buildingNumber,
                apartmentNumber: updateData.apartmentNumber || "",
                stateProvince:updateData.stateProvince,
                city: updateData.city,
                country: updateData.country,
                zipCode: updateData.zipCode
            },

        };
         const url = `https://apifintech.sandbox.itcardpaymentservice.pl:30080/v1/clients/${clientCode}/address`;
        const apiResponse = await sendPutRequest(url, JSON.stringify(request));
        console.log(apiResponse);

        if (apiResponse.status !== "APPROVED") {
            return res.status(500).json({
                success: false,
                message: "Error while changing address"
            });
        }else{
            await existingClient.save();
            res.json({ message: 'Client address information updated successfully', updatedClient: existingClient});
        }
     } catch (error) {
        console.error('Error updating address:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// ✅ Update customer personal information (Only for individual customers)
router.patch('/clients/:clientCode/personal', async (req, res) => {
    try {
        const { clientCode } = req.params;
        const updateData = req.body; // Data from the form

        const existingClient = await IndividualOnboarding.findOne({ clientID: clientCode });

        if (!existingClient) {
            return res.status(404).json({ error: 'Client not found' });
        }

        existingClient.personalInfo = {
            ...existingClient.personalInfo.toObject(),
            ...updateData
        };

        await existingClient.save();

        res.json({ message: 'Customer personal information updated successfully', updatedClient: existingClient });
    } catch (error) {
        console.error('Error updating customer info:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// ✅ Update customer operation status
router.patch('/clients/:clientCode/operationStatus', async (req, res) => {
    try {
        const { clientCode } = req.params;
        const { operationStatus } = req.body;

        const updatedClient = await IndividualOnboarding.findOneAndUpdate(
            { clientID: clientCode },
            { $set: { operationStatus } },
            { new: true, runValidators: true }
        );

        if (!updatedClient) {
            return res.status(404).json({ error: 'Client not found' });
        }

        res.json({ message: 'Operation status updated successfully', updatedClient });
    } catch (error) {
        console.error('Error updating operation status:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});



// 1️⃣ Add Existing Debit Account
router.post("/debit-account", async (req, res) => {
    try {
        const {
            accountNumber,
            accountHolder,
            currencyName,
            status,
            extAppId,
            productCode,
            productDesc,
            bankNumber,
            openDate,
            balance,
            clientCode,
            currencyCode,
            relationship,
            bankName
        } = req.body;
var currency = currencyCode;
        // // Check if the account already exists
        const existingAccount = await Account.findOne({ accountNumber });
        if (existingAccount) {
            return res.status(400).json({ error: "Account already exists" });
        }

        const existingClient = await IndividualOnboarding.findOne({clientID: clientCode});
        if (!existingClient) {
            return res.status(400).json({error: "Please Create a client first" });
        }
        // Request payload
        const clientData = `{
        "productCode": "RYV1",
        "currencyCode":"${currency}" ,
        "accNo": "${accountNumber}",
        "owners": [
            {
                "clientCode": "${clientCode}",
                "relationship": "OWN"
            }
        ]
    }`;


        // API URL
        const url = "https://apifintech.sandbox.itcardpaymentservice.pl:30080/v1/accounts/debitAccount";

        const result = await sendPostRequest(url, clientData);
        console.dir(result)
        // Create new account
        if (result.accNo) {
            const newAccount = new Account({
                onboarding: existingClient._id,
                clientCode: clientCode,
                accountNumber,
                accountHolder,
                relationship,
                bankName,
                currencyName,
                currency,
                extAppId,
                productCode,
                productDesc,
                bankNumber,
                status: result.status,
                owners: result.owners.map(owner => ({
                    clientCode: owner.clientCode,
                    relationship: owner.relationship,
                    mainOwner: owner.mainOwner
                })),
                openDate: new Date(openDate), // Ensures proper date format
                balance: balance || 0, // Default balance if not provided
            });
            await newAccount.save();

            var card = await createVirtualCard(clientCode, currency, accountNumber, existingClient._id,productCode)


            res.status(201).json({message: "Debit account created successfully", account: newAccount, card: card});
        } else {
            res.status(400).json({error: "Error creating debit account"});
        }

    } catch (error) {
        console.error("Error creating debit account:", error);
        res.status(500).json({ error: "Internal server error" });
    }
});


// 2️⃣ Get Account Transactions (For a Specific Time Frame)
router.get("/transactions/:accountNumber", async (req, res) => {
    try {
        const { startDate, endDate } = req.query;

        const transactions = await Transaction.find({
            accountNumber: req.params.accountNumber,
            date: { $gte: new Date(startDate), $lte: new Date(endDate) }
        });

        res.json(transactions);
    } catch (error) {
        console.error("Error fetching transactions:", error);
        res.status(500).json({ error: "Internal server error" });
    }
});

// 3️⃣ Get Transaction Details
router.get("/transaction/:transactionId", async (req, res) => {
    try {
        const transaction = await Transaction.findById(req.params.transactionId);

        if (!transaction) {
            return res.status(404).json({ error: "Transaction not found" });
        }

        res.json(transaction);
    } catch (error) {
        console.error("Error fetching transaction details:", error);
        res.status(500).json({ error: "Internal server error" });
    }
});

// 4️⃣ Close Account at Organization Request
router.put("/close-account/organization/:accountNumber", async (req, res) => {
    try {
        const { closeCause } = req.body;

        const updatedAccount = await Account.findOneAndUpdate(
            { accountNumber: req.params.accountNumber },
            { status: "closed", closeCause },
            { new: true }
        );

        if (!updatedAccount) {
            return res.status(404).json({ error: "Account not found" });
        }

        res.json({ message: "Account closed by organization", updatedAccount });
    } catch (error) {
        console.error("Error closing account:", error);
        res.status(500).json({ error: "Internal server error" });
    }
});

// 5️⃣ Close Account at Client Request
router.put("/close-account/client/:accountNumber", async (req, res) => {
    try {
        const updatedAccount = await Account.findOneAndUpdate(
            { accountNumber: req.params.accountNumber },
            { status: "pending_closure" },
            { new: true }
        );

        if (!updatedAccount) {
            return res.status(404).json({ error: "Account not found" });
        }

        res.json({ message: "Account closure request submitted", updatedAccount });
    } catch (error) {
        console.error("Error processing account closure request:", error);
        res.status(500).json({ error: "Internal server error" });
    }
});

// 6️⃣ Immediate Termination of Notice Period
router.put("/terminate-notice/:accountNumber", async (req, res) => {
    try {
        const updatedAccount = await Account.findOneAndUpdate(
            { accountNumber: req.params.accountNumber },
            { status: "closed_immediately" },
            { new: true }
        );

        if (!updatedAccount) {
            return res.status(404).json({ error: "Account not found" });
        }

        res.json({ message: "Notice period terminated immediately", updatedAccount });
    } catch (error) {
        console.error("Error terminating notice period:", error);
        res.status(500).json({ error: "Internal server error" });
    }
});


async function createVirtualCard(clientId,
                                 currencyCode,
                                 accNo, userId,productCode) {

    // const onboardingData = await IndividualOnboarding.findOne({clientID: clientId})
    //     .populate("productVersion")
    // console.log()
    // var code =onboardingData.productVersion[0].version_code ;
    // Request payload
    const code = productCode;
    const clientData = `{
        "holder": "${clientId}",
        "productCode": "${code}",

        "visual": "${code}",
         "delivery": {
          "deliveryType": "LETTER",
          "deliveryBranch": "DHL",
          "oneTimeDeliveryBranch": "DHL"
          },
        "pinDelivery": {
            "deliveryType": "LETTER"
        },
        "account": {
            "currencyCode": "${currencyCode}",
            "productCode":  "${code}",
            "owners": [
                {
                    "clientCode": "${clientId}",
                    "relationship": "OWN"
                }
            ],
            "accNo": "${accNo}"
        },
        "3dsAuthMethods": ["MOBILE_APP"]
    }`;



    // API URL
    const url = "https://apifintech.sandbox.itcardpaymentservice.pl:30080/v1/cards/debitCard";

    try {
        const apiResponse = await sendPostRequest(url, clientData);

        // Prepare data for saving to MongoDB
        const cardData = {
            cardHash: apiResponse.cardHash,
            cardKey: apiResponse.cardKey,
            expDate: apiResponse.expDate,
            status: apiResponse.status,
            statusCode: apiResponse.statusCode,
            kind: apiResponse.kind,
            productCode: apiResponse.productCode,
            productDesc: apiResponse.productDesc,
            main: apiResponse.main,
            holder: apiResponse.holder,
            accNo: apiResponse.accNo,
            embossName1: apiResponse.embossName1,
            cardMask: apiResponse.cardMask,
            onboarding: userId,
        };

        // Save card details to MongoDB
        const newCard = new Card(cardData);
        await newCard.save();
        // 🔔 Send Webhook Notification
        await sendCardCreationWebhook(apiResponse);

        console.log("Account saved to database:", newCard);
        return apiResponse;
    } catch (err) {
        console.error("Error creating virtual card:", err);
    }
}

router.get("/account/:accountNumber/cards", async (req, res) => {
    try {
        const { accountNumber } = req.params;
        const cards = await Card.find({accNo: accountNumber}).sort({createdAt: -1});
        res.status(200).json({ cards});
    }catch (err) {
        res.status(500).json({ error: "Internal server error" });
    }
});



module.exports = router;
