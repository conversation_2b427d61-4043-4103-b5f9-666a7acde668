const VALIDATION_RULES = {
    MAX_LENGTHS: {
        companyName: 255,
        clientCode: 50,
        phoneNumber: 20,
        email: 255,
        nip: 20,
        regon: 20,
        embossedName: 30,
        companyIndustry: 100,
        companyNumber: 50,
        contactName: 100,
        contactRole: 50,
        countryOfIncorporation: 3, // ISO code
        companyWebsite: 255,
        typeOfBusiness: 50,
        cardUsage: 50,
        businessSector: 50,
        businessPurpose: 255,
        adminName: 100,
        adminRole: 50,
        adminEmail: 255,
        adminPhone: 20,

        street: 100,
        buildingNumber: 10,
        apartmentNumber: 10,
        city: 50,
        zipCode: 20,
        country: 3,
    },
}

const isNotEmptyString = (value) =>
    typeof value === 'string' && value.trim().length > 0

const isValidLength = (value, max) =>
    typeof value === 'string' && value.length <= max

const isValidEmail = (value) =>
    typeof value === 'string' &&
    /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)

const isValidPhoneNumber = (value) =>
    typeof value === 'string' && /^[0-9+\-() ]{7,20}$/.test(value)

const isValidUrl = (value) =>
    typeof value === 'string' && /^https?:\/\/.+\..+/.test(value)

const isValidDate = (value) => {
    const d = new Date(value)
    return !isNaN(d.getTime())
}

const validateB2BRegisterData = (data) => {
    const errors = []

    const {
        company_name,
        clientCode,
        company_phone,
        authPhoneNumber,
        company_email,
        nip,
        regon,
        embossedName,

        company_industry,
        company_number,
        registration_date,
        contact_name,
        contact_role,
        country_of_incorporation,
        company_website,

        type_of_business,
        card_usage,
        business_sector,
        business_purpose,

        admin_name,
        admin_role,
        admin_email,
        admin_phone,

        registered_address,
        operational_address,
    } = data

    // ✔ Company name
    if (!isNotEmptyString(company_name)) {
        errors.push('Company name is required')
    } else if (!isValidLength(company_name, VALIDATION_RULES.MAX_LENGTHS.companyName)) {
        errors.push(`Company name must not exceed ${VALIDATION_RULES.MAX_LENGTHS.companyName} characters`)
    }

    // ✔ Client code
    if (!isNotEmptyString(clientCode)) {
        errors.push('Client code is required')
    } else if (!isValidLength(clientCode, VALIDATION_RULES.MAX_LENGTHS.clientCode)) {
        errors.push(`Client code must not exceed ${VALIDATION_RULES.MAX_LENGTHS.clientCode} characters`)
    }

    // ✔ Phone number
    if (!isNotEmptyString(company_phone)) {
        errors.push('Company phone is required')
    } else if (!isValidPhoneNumber(company_phone)) {
        errors.push('Invalid company phone format')
    }

    if (authPhoneNumber && (!isNotEmptyString(authPhoneNumber) || !isValidPhoneNumber(authPhoneNumber))) {
        errors.push('Invalid auth phone number format')
    }

    // ✔ Email
    if (!isNotEmptyString(company_email)) {
        errors.push('Company email is required')
    } else if (!isValidEmail(company_email)) {
        errors.push('Invalid company email format')
    } else if (!isValidLength(company_email, VALIDATION_RULES.MAX_LENGTHS.email)) {
        errors.push(`Company email must not exceed ${VALIDATION_RULES.MAX_LENGTHS.email} characters`)
    }

    // ✔ NIP & REGON
    if (nip !== undefined && nip !== null) {
        if (typeof nip !== 'string') errors.push('NIP must be a string')
        else if (!isValidLength(nip, VALIDATION_RULES.MAX_LENGTHS.nip)) {
            errors.push(`NIP must not exceed ${VALIDATION_RULES.MAX_LENGTHS.nip} characters`)
        }
    }

    if (regon !== undefined && regon !== null) {
        if (typeof regon !== 'string') errors.push('REGON must be a string')
        else if (!isValidLength(regon, VALIDATION_RULES.MAX_LENGTHS.regon)) {
            errors.push(`REGON must not exceed ${VALIDATION_RULES.MAX_LENGTHS.regon} characters`)
        }
    }

    if (embossedName && (!isNotEmptyString(embossedName) || !isValidLength(embossedName, VALIDATION_RULES.MAX_LENGTHS.embossedName))) {
        errors.push(`Embossed name must not exceed ${VALIDATION_RULES.MAX_LENGTHS.embossedName} characters`)
    }

    if (company_industry && (!isNotEmptyString(company_industry) || !isValidLength(company_industry, VALIDATION_RULES.MAX_LENGTHS.companyIndustry))) {
        errors.push(`Company industry must not exceed ${VALIDATION_RULES.MAX_LENGTHS.companyIndustry} characters`)
    }

    if (company_number && (!isNotEmptyString(company_number) || !isValidLength(company_number, VALIDATION_RULES.MAX_LENGTHS.companyNumber))) {
        errors.push(`Company number must not exceed ${VALIDATION_RULES.MAX_LENGTHS.companyNumber} characters`)
    }

    if (registration_date && !isValidDate(registration_date)) {
        errors.push('Registration date must be a valid date')
    }

    if (contact_name && (!isNotEmptyString(contact_name) || !isValidLength(contact_name, VALIDATION_RULES.MAX_LENGTHS.contactName))) {
        errors.push(`Contact name must not exceed ${VALIDATION_RULES.MAX_LENGTHS.contactName} characters`)
    }

    if (contact_role && (!isNotEmptyString(contact_role) || !isValidLength(contact_role, VALIDATION_RULES.MAX_LENGTHS.contactRole))) {
        errors.push(`Contact role must not exceed ${VALIDATION_RULES.MAX_LENGTHS.contactRole} characters`)
    }

    if (country_of_incorporation && (!isNotEmptyString(country_of_incorporation) || !isValidLength(country_of_incorporation, VALIDATION_RULES.MAX_LENGTHS.countryOfIncorporation))) {
        errors.push(`Country of incorporation must not exceed ${VALIDATION_RULES.MAX_LENGTHS.countryOfIncorporation} characters`)
    }

    if (company_website && (!isValidUrl(company_website) || !isValidLength(company_website, VALIDATION_RULES.MAX_LENGTHS.companyWebsite))) {
        errors.push(`Invalid or too long company website`)
    }

    if (type_of_business && (!isNotEmptyString(type_of_business) || !isValidLength(type_of_business, VALIDATION_RULES.MAX_LENGTHS.typeOfBusiness))) {
        errors.push(`Type of business must not exceed ${VALIDATION_RULES.MAX_LENGTHS.typeOfBusiness} characters`)
    }

    if (card_usage && (!isNotEmptyString(card_usage) || !isValidLength(card_usage, VALIDATION_RULES.MAX_LENGTHS.cardUsage))) {
        errors.push(`Card usage must not exceed ${VALIDATION_RULES.MAX_LENGTHS.cardUsage} characters`)
    }

    if (business_sector && (!isNotEmptyString(business_sector) || !isValidLength(business_sector, VALIDATION_RULES.MAX_LENGTHS.businessSector))) {
        errors.push(`Business sector must not exceed ${VALIDATION_RULES.MAX_LENGTHS.businessSector} characters`)
    }

    if (business_purpose && (!isNotEmptyString(business_purpose) || !isValidLength(business_purpose, VALIDATION_RULES.MAX_LENGTHS.businessPurpose))) {
        errors.push(`Business purpose must not exceed ${VALIDATION_RULES.MAX_LENGTHS.businessPurpose} characters`)
    }

    if (!isNotEmptyString(admin_name)) {
        errors.push('Admin name is required')
    } else if (!isValidLength(admin_name, VALIDATION_RULES.MAX_LENGTHS.adminName)) {
        errors.push(`Admin name must not exceed ${VALIDATION_RULES.MAX_LENGTHS.adminName} characters`)
    }

    if (admin_role && (!isNotEmptyString(admin_role) || !isValidLength(admin_role, VALIDATION_RULES.MAX_LENGTHS.adminRole))) {
        errors.push(`Admin role must not exceed ${VALIDATION_RULES.MAX_LENGTHS.adminRole} characters`)
    }

    if (!isNotEmptyString(admin_email)) {
        errors.push('Admin email is required')
    } else if (!isValidEmail(admin_email)) {
        errors.push('Invalid admin email')
    } else if (!isValidLength(admin_email, VALIDATION_RULES.MAX_LENGTHS.adminEmail)) {
        errors.push(`Admin email must not exceed ${VALIDATION_RULES.MAX_LENGTHS.adminEmail} characters`)
    }

    if (!isNotEmptyString(admin_phone)) {
        errors.push('Admin phone is required')
    } else if (!isValidPhoneNumber(admin_phone)) {
        errors.push('Invalid admin phone')
    }

    // ✔ Addresses (objects)
    const checkAddress = (address, prefix) => {
        if (!address) {
            errors.push(`${prefix} address is required`)
            return
        }
        if (!isNotEmptyString(address.street)) errors.push(`${prefix} street is required`)
        else if (!isValidLength(address.street, VALIDATION_RULES.MAX_LENGTHS.street)) errors.push(`${prefix} street too long`)

        if (!isNotEmptyString(address.building_number)) errors.push(`${prefix} building number is required`)
        else if (!isValidLength(address.building_number, VALIDATION_RULES.MAX_LENGTHS.buildingNumber)) errors.push(`${prefix} building number too long`)

        if (address.apartment_number && !isValidLength(address.apartment_number, VALIDATION_RULES.MAX_LENGTHS.apartmentNumber))
            errors.push(`${prefix} apartment number too long`)

        if (!isNotEmptyString(address.city)) errors.push(`${prefix} city is required`)
        else if (!isValidLength(address.city, VALIDATION_RULES.MAX_LENGTHS.city)) errors.push(`${prefix} city too long`)

        if (!isNotEmptyString(address.postal_code)) errors.push(`${prefix} postal code is required`)
        else if (!isValidLength(address.postal_code, VALIDATION_RULES.MAX_LENGTHS.zipCode)) errors.push(`${prefix} postal code too long`)

        if (!isNotEmptyString(address.country)) errors.push(`${prefix} country is required`)
        else if (!isValidLength(address.country, VALIDATION_RULES.MAX_LENGTHS.country)) errors.push(`${prefix} country too long`)
    }

    checkAddress(registered_address, 'Registered')
    if (operational_address) checkAddress(operational_address, 'Operational')

    return errors
}
