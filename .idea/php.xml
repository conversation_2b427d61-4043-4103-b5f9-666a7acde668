<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="MessDetectorOptionsConfiguration">
    <option name="transferred" value="true" />
  </component>
  <component name="PHPCSFixerOptionsConfiguration">
    <option name="transferred" value="true" />
  </component>
  <component name="PHPCodeSnifferOptionsConfiguration">
    <option name="highlightLevel" value="WARNING" />
    <option name="transferred" value="true" />
  </component>
  <component name="PhpCodeSniffer">
    <phpcs_settings>
      <phpcs_by_interpreter asDefaultInterpreter="true" interpreter_id="e8dc6228-fb39-45f0-a4f6-580605e0b9c2" timeout="30000" />
    </phpcs_settings>
  </component>
  <component name="PhpStan">
    <PhpStan_settings>
      <phpstan_by_interpreter asDefaultInterpreter="true" interpreter_id="e8dc6228-fb39-45f0-a4f6-580605e0b9c2" timeout="60000" />
    </PhpStan_settings>
  </component>
  <component name="PhpStanOptionsConfiguration">
    <option name="transferred" value="true" />
  </component>
  <component name="Psalm">
    <Psalm_settings>
      <psalm_fixer_by_interpreter asDefaultInterpreter="true" interpreter_id="e8dc6228-fb39-45f0-a4f6-580605e0b9c2" timeout="60000" />
    </Psalm_settings>
  </component>
  <component name="PsalmOptionsConfiguration">
    <option name="transferred" value="true" />
  </component>
</project>