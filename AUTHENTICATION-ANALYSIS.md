# Ryvyl Backend Authentication Analysis

## 📋 Overview

This document provides a comprehensive analysis of all authentication mechanisms, route protections, card payment service authentication, and 3D Secure (3DS) logic in the Ryvyl Backend.

## 🔐 Authentication Methods

### 1. **JWT Bearer Token Authentication**

#### **Implementation**: `middleware/authMiddleware.js`
```javascript
const authenticateToken = (req, res, next) => {
    const token = req.headers['authorization']?.split(' ')[1]; // Bearer <token>
    if (!token) {
        return res.status(401).json({ error: "Unauthorized: No token provided" });
    }
    jwt.verify(token, process.env.JWT_SECRET, (err, user) => {
        if (err) {
            return res.status(401).json({ error: "Invalid or expired token" });
        }
        req.user = user; // Attach user info to request object
        next();
    });
};
```

#### **Token Generation**: `routes/authRoutes.js`
```javascript
const token = jwt.sign(
    {
        id: user._id,
        email: user.email,
        dashboard: user.dashboard,
    },
    process.env.JWT_SECRET,
    { expiresIn: "1d" }
);
```

#### **Token Storage**:
- **HTTP-Only Cookie**: Set with security flags
- **Bearer Header**: `Authorization: Bearer <token>`

### 2. **Client Certificate Authentication**

#### **Implementation**: `index.js`
```javascript
// SSL Options with client certificate requirement
const options = {
    key: fs.readFileSync("./ssl/server.key"),
    cert: fs.readFileSync("./ssl/server.crt"),
    ca: fs.readFileSync("./ssl/ca.crt"),
    requestCert: true,
    rejectUnauthorized: false
};

// Client certificate middleware
const requireClientCertificate = (req, res, next) => {
    const cert = req.socket.getPeerCertificate();
    if (req.client.authorized) {
        console.log("✅ Authorized Client Certificate:", cert.subject);
        return next();
    } else {
        console.warn("❌ Unauthorized Access Attempt:", cert.subject || "Unknown");
        return res.status(403).json({ error: "Client certificate required." });
    }
};
```

#### **Certificate Files**:
- **Server Certificate**: `./ssl/server.crt`
- **Server Key**: `./ssl/server.key`
- **CA Certificate**: `./ssl/ca.crt`

### 3. **Two-Factor Authentication (2FA)**

#### **Implementation**: `routes/authRoutes.js`
```javascript
// 2FA verification during login
router.post("/verify-login-2fa", async (req, res) => {
    const { email, verificationCode, tempToken } = req.body;
    
    // Verify temporary token
    const decoded = jwt.verify(tempToken, process.env.JWT_SECRET);
    
    // Verify TOTP code
    const verified = speakeasy.totp.verify({
        secret: user.twoFactorSecret,
        encoding: 'base32',
        token: verificationCode,
        window: 2
    });
    
    if (verified) {
        // Generate full access token
        const token = jwt.sign({...}, process.env.JWT_SECRET, { expiresIn: "1d" });
    }
});
```

### 4. **Session-Based Authentication (Legacy)**

#### **Implementation**: `controllers/authController.js`
```javascript
exports.loginUser = (req, res) => {
    const { username, password } = req.body;
    User.authenticate(username, password, (err, user) => {
        if (err || !user) {
            return res.status(401).json({ error: 'Invalid credentials' });
        }
        // Create session
        req.session.userId = user._id;
        res.json({ message: 'Login successful' });
    });
};
```

## 🛡️ Route Authentication Requirements

### **Authenticated Routes** (Require JWT Token)

#### **User Management**
- `GET /api/users` - User listing
- `POST /api/users` - User creation
- `PUT /api/users/:id` - User updates
- `DELETE /api/users/:id` - User deletion

#### **Company Management**
- `GET /api/company` - Company listing
- `POST /api/company` - Company creation
- `PUT /api/company/:id` - Company updates
- `DELETE /api/company/:id` - Company deletion

#### **Card Operations**
- `GET /api/cards` - Card listing
- `POST /api/cards` - Card creation
- `PUT /api/cards/:id` - Card updates
- `DELETE /api/cards/:id` - Card deletion

#### **Card Program Management**
- `GET /api/cip` - Card program listing
- `POST /api/cip` - Card program creation
- `PUT /api/cip/:id` - Card program updates

#### **Financial Operations**
- `GET /api/client` - Client operations
- `POST /api/client/card` - Card creation
- `GET /api/client/card/:id` - Card details

#### **Administrative**
- `GET /api/activity` - User activity logs
- `GET /api/logs` - System logs
- `GET /api/webhook-logs` - Webhook logs

#### **Configuration**
- `GET /api/cardScheme` - Card scheme management
- `GET /api/cardBin` - Card BIN management
- `GET /api/countries` - Country configuration
- `GET /api/zones` - Zone configuration
- `GET /api/regions` - Region configuration

#### **Integration Layer**
- `GET /api/il/*` - Integration layer APIs
- `GET /api/il/b2b/*` - B2B integration APIs
- `GET /api/local/*` - Local API operations

#### **Data Access**
- `GET /api/data/*` - Sensitive data operations
- `GET /api/pin` - PIN operations
- `GET /api/fee` - Fee management

### **🚨 Unauthenticated Routes** (Public Access)

#### **Authentication Endpoints**
- `POST /api/auth/login` - User login
- `POST /api/auth/register` - User registration
- `POST /api/auth/verify-login-2fa` - 2FA verification
- `POST /api/auth/logout` - User logout

#### **Password Management**
- `POST /api/password/forgot` - Password reset request
- `POST /api/password/reset` - Password reset confirmation

#### **Onboarding (Critical Security Issue)**
- `POST /api/onboarding/personal` - Personal onboarding
- `POST /api/clients/:clientCode/id-documents` - **🚨 File upload without auth**

#### **Public Company Data**
- `GET /api/companies` - Public company information

#### **Webhooks**
- `POST /webhook/*` - Webhook endpoints

#### **Delivery Configuration**
- `GET /api/delivery-methods` - Delivery method listing
- `GET /api/zone-method-configs` - Zone method configurations

#### **Client Certificate Protected**
- `GET /s` - Secure endpoint requiring client certificate

## 💳 Card Payment Service Authentication

### **ITCardPaymentService Integration**

#### **Base URL**: `https://apifintech.sandbox.itcardpaymentservice.pl:30080`

#### **Client Certificate Authentication**
```javascript
// config/ApiInstense.js
function createHttpsAgent() {
    const certPath = path.join(__dirname, '..', 'config', 'RYVL_API.crt');
    const keyPath = path.join(__dirname, '..', 'config', 'RYVL_API.key');
    return new https.Agent({
        cert: fs.readFileSync(certPath),
        key: fs.readFileSync(keyPath),
    });
}
```

#### **Certificate Files**:
- **Client Certificate**: `config/RYVL_API.crt`
- **Client Private Key**: `config/RYVL_API.key`

#### **Authentication Flow for Sensitive Data**
```javascript
// routes/SensetiveDataRoutes.js
// Step 1: Generate ECDH key pair
const ecdh = crypto.createECDH("prime256v1");
ecdh.generateKeys();
const publicKeyBase64 = ecdh.getPublicKey().toString("base64");

// Step 2: Authenticate & Get JWT Token
const authUrl = `https://apifintech.sandbox.itcardpaymentservice.pl:30080/v1/cards/${id}/auth`;
const authResponse = await sendPostRequest(authUrl, { 
    resource: "CARD_NUMBER", 
    securityKey: publicKeyBase64 
});

// Step 3: Use access token for sensitive operations
const headers = { Authorization: `Bearer ${authResponse.accessToken}` };
```

#### **Supported Resources**:
- **CARD_NUMBER**: Card number retrieval
- **CVV2**: CVV code retrieval

### **Webhook Authentication**

#### **JWT Token Generation for Webhooks**
```javascript
// config/webhook.js
function generateJwtToken() {
    const payload = {
        sub: 'https://card-auth-staging.ryvyl.eu',
        iss: 'https://your-issuer-url.com',
        exp: Math.floor(Date.now() / 1000) + (60 * 60), // 1 hour validity
    };
    return jwt.sign(payload, PRIVATE_KEY, { algorithm: 'ES512' });
}
```

#### **Private Key**: `private.pem` (ES512 algorithm)

#### **Webhook Headers**:
```javascript
headers: {
    "Content-Type": "application/json",
    "Authorization": `Bearer ${token}`,
    "X-Webhook-ID": webhookLog.webhookId,
    "X-Webhook-Event": webhookLog.triggerEvent
}
```

### **Legacy Service Authentication**

#### **JWT Token for Legacy APIs**
```javascript
// config/LegecyService.js
function generateJwtToken() {
    const payload = {
        sub: 'https://card-auth-staging.ryvyl.eu',
        iss: 'https://your-issuer-url.com',
        exp: Math.floor(Date.now() / 1000) + 3600, // 1 hour
    };
    return jwt.sign(payload, PRIVATE_KEY, { algorithm: 'ES512' });
}
```

#### **API Endpoint**: `https://card-auth-staging.ryvyl.eu/api/itcard/balance-check`

## 🔒 3D Secure (3DS) Implementation

### **3DS Protocol Version**: 2.1.0

#### **3DS Transaction Fields** (from `views/sample-requests.ejs`):
```xml
<threeDSTransId>e6e9bf9b-af70-4f20-ad3a-52ce69de1c15</threeDSTransId>
<dsTransId>2d5e6910-4d86-4d84-b6a3-9125b80b9ba1</dsTransId>
<acsTransId>36e6c66a-3dc6-4a59-a722-96c718824981</acsTransId>
<threeDSRequestorID>123456789.visa</threeDSRequestorID>
<threeDSRequestorName>3dsclient.local.visa</threeDSRequestorName>
<threeDSServerRefNumber>3DS_LOA_SER_GPPL_020100_00075</threeDSServerRefNumber>
<threeDSServerOperatorID>1jpeeLAWgGFgS1Ri9tX9</threeDSServerOperatorID>
<threeDSRequestorURL>http://gpayments.com</threeDSRequestorURL>
<threeDSServerURL>https://amir-test3.testlab.3dsecure.cloud:9605/api/v2/ds/result/request</threeDSServerURL>
<deviceChannel>02</deviceChannel>
```

#### **3DS Server Configuration**:
- **3DS Server URL**: `https://amir-test3.testlab.3dsecure.cloud:9605/api/v2/ds/result/request`
- **3DS Server Reference**: `3DS_LOA_SER_GPPL_020100_00075`
- **3DS Server Operator ID**: `1jpeeLAWgGFgS1Ri9tX9`

#### **3DS Requestor Configuration**:
- **Requestor ID**: `123456789.visa`
- **Requestor Name**: `3dsclient.local.visa`
- **Requestor URL**: `http://gpayments.com`

#### **Device Channel**: `02` (Browser-based)

### **3DS Authentication Flow**:
1. **Transaction Initiation**: Card transaction starts
2. **3DS Check**: Determine if 3DS authentication required
3. **Challenge Request**: If required, initiate challenge flow
4. **ACS Interaction**: Communicate with Access Control Server
5. **Authentication Result**: Process authentication outcome
6. **Transaction Completion**: Complete or decline transaction

## 🚨 Security Vulnerabilities

### **Critical Authentication Issues**

#### **1. Unauthenticated File Upload**
```javascript
// index.js:252-259 - NO AUTHENTICATION!
app.post(
    "/api/clients/:clientCode/id-documents",
    upload.fields([
        { name: "idFront", maxCount: 1 },
        { name: "idBack", maxCount: 1 },
    ]),
    uploadIdDocumentImages
);
```
**Risk**: Anyone can upload files to the server

#### **2. Hardcoded Credentials**
```javascript
// example.env - EXPOSED IN REPOSITORY
DATABASE_URL="mongodb+srv://hostingstratified20:<EMAIL>/ryvyl"
JWT_SECRET=4d7ce71813193994a54b604d6b023133
EMAIL_PASS=vsayhsggiklklewb
```

#### **3. Weak JWT Secret**
- Current JWT secret appears to be MD5 hash
- Only 32 characters (should be 64+ for production)

#### **4. Insecure CORS Configuration**
```javascript
const allowedOrigins = [
    "*", // Allows any origin!
    'https://apicip.ryvyl.eu',
    // ... other origins
];
```

#### **5. No Input Validation on Authentication**
- Login endpoints vulnerable to NoSQL injection
- No rate limiting on authentication attempts

### **Certificate Security Issues**

#### **1. Client Certificate Validation**
```javascript
// rejectUnauthorized: false - SECURITY RISK
const options = {
    requestCert: true,
    rejectUnauthorized: false // Should be true in production
};
```

#### **2. Certificate Files in Repository**
- SSL certificates stored in repository
- Private keys potentially exposed

## 🔧 Authentication Configuration

### **Environment Variables Required**
```bash
# JWT Configuration
JWT_SECRET=your-super-secure-jwt-secret-64-chars-minimum

# Database
DATABASE_URL=***************************************

# Email
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-secure-email-password

# External Services
CARD_WEBHOOK=https://your-webhook-url.com
```

### **Certificate Files Required**
```
ssl/
├── server.crt      # Server SSL certificate
├── server.key      # Server SSL private key
└── ca.crt          # Certificate Authority

config/
├── RYVL_API.crt    # Client certificate for ITCardPaymentService
├── RYVL_API.key    # Client private key for ITCardPaymentService
└── private.pem     # Private key for webhook JWT signing
```

## 📊 Authentication Summary

### **Authentication Methods Used**
1. **JWT Bearer Tokens** (Primary)
2. **Client Certificates** (ITCardPaymentService)
3. **Two-Factor Authentication** (TOTP)
4. **Session-based** (Legacy)

### **External Service Authentication**
1. **ITCardPaymentService**: Client certificate + ECDH key exchange
2. **Webhook Endpoints**: ES512 JWT tokens
3. **Legacy APIs**: ES512 JWT tokens

### **3DS Integration**
- **Protocol Version**: 2.1.0
- **Test Environment**: Sandbox
- **Device Channel**: Browser-based (02)

### **Critical Security Actions Required**
1. **Add authentication** to file upload endpoints
2. **Remove hardcoded credentials** from repository
3. **Generate strong JWT secret** (64+ characters)
4. **Fix CORS configuration** (remove wildcard)
5. **Implement rate limiting** on authentication
6. **Add input validation** to prevent injection
7. **Enable certificate validation** in production
8. **Move certificates** out of repository

This authentication analysis reveals significant security vulnerabilities that must be addressed before production deployment.
