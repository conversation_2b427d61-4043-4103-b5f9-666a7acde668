/*
	Follows ISO 4217, https://www.iso.org/iso-4217-currency-codes.html
	See https://www.currency-iso.org/dam/downloads/lists/list_one.xml
	Data last updated 2024-06-25
*/



export const currencies =  [
    {
        "code": "AED",
        "number": "784",
        "digits": 2,
        "currency": "UAE Dirham",
        "countries": [
            "United Arab Emirates (The)"
        ]
    },
    {
        "code": "AFN",
        "number": "971",
        "digits": 2,
        "currency": "Afghani",
        "countries": [
            "Afghanistan"
        ]
    },
    {
        "code": "ALL",
        "number": "008",
        "digits": 2,
        "currency": "Lek",
        "countries": [
            "Albania"
        ]
    },
    {
        "code": "AMD",
        "number": "051",
        "digits": 2,
        "currency": "Armenian Dram",
        "countries": [
            "Armenia"
        ]
    },
    {
        "code": "ANG",
        "number": "532",
        "digits": 2,
        "currency": "Netherlands Antillean Guilder",
        "countries": [
            "Curaçao",
            "<PERSON><PERSON> (Dutch Part)"
        ]
    },
    {
        "code": "AOA",
        "number": "973",
        "digits": 2,
        "currency": "Kwanza",
        "countries": [
            "Angola"
        ]
    },
    {
        "code": "ARS",
        "number": "032",
        "digits": 2,
        "currency": "Argentine Peso",
        "countries": [
            "Argentina"
        ]
    },
    {
        "code": "AUD",
        "number": "036",
        "digits": 2,
        "currency": "Australian Dollar",
        "countries": [
            "Australia",
            "Christmas Island",
            "Cocos (Keeling) Islands (The)",
            "Heard Island and Mcdonald Islands",
            "Kiribati",
            "Nauru",
            "Norfolk Island",
            "Tuvalu"
        ]
    },
    {
        "code": "AWG",
        "number": "533",
        "digits": 2,
        "currency": "Aruban Florin",
        "countries": [
            "Aruba"
        ]
    },
    {
        "code": "AZN",
        "number": "944",
        "digits": 2,
        "currency": "Azerbaijan Manat",
        "countries": [
            "Azerbaijan"
        ]
    },
    {
        "code": "BAM",
        "number": "977",
        "digits": 2,
        "currency": "Convertible Mark",
        "countries": [
            "Bosnia and Herzegovina"
        ]
    },
    {
        "code": "BBD",
        "number": "052",
        "digits": 2,
        "currency": "Barbados Dollar",
        "countries": [
            "Barbados"
        ]
    },
    {
        "code": "BDT",
        "number": "050",
        "digits": 2,
        "currency": "Taka",
        "countries": [
            "Bangladesh"
        ]
    },
    {
        "code": "BGN",
        "number": "975",
        "digits": 2,
        "currency": "Bulgarian Lev",
        "countries": [
            "Bulgaria"
        ]
    },
    {
        "code": "BHD",
        "number": "048",
        "digits": 3,
        "currency": "Bahraini Dinar",
        "countries": [
            "Bahrain"
        ]
    },
    {
        "code": "BIF",
        "number": "108",
        "digits": 0,
        "currency": "Burundi Franc",
        "countries": [
            "Burundi"
        ]
    },
    {
        "code": "BMD",
        "number": "060",
        "digits": 2,
        "currency": "Bermudian Dollar",
        "countries": [
            "Bermuda"
        ]
    },
    {
        "code": "BND",
        "number": "096",
        "digits": 2,
        "currency": "Brunei Dollar",
        "countries": [
            "Brunei Darussalam"
        ]
    },
    {
        "code": "BOB",
        "number": "068",
        "digits": 2,
        "currency": "Boliviano",
        "countries": [
            "Bolivia (Plurinational State Of)"
        ]
    },
    {
        "code": "BOV",
        "number": "984",
        "digits": 2,
        "currency": "Mvdol",
        "countries": [
            "Bolivia (Plurinational State Of)"
        ]
    },
    {
        "code": "BRL",
        "number": "986",
        "digits": 2,
        "currency": "Brazilian Real",
        "countries": [
            "Brazil"
        ]
    },
    {
        "code": "BSD",
        "number": "044",
        "digits": 2,
        "currency": "Bahamian Dollar",
        "countries": [
            "Bahamas (The)"
        ]
    },
    {
        "code": "BTN",
        "number": "064",
        "digits": 2,
        "currency": "Ngultrum",
        "countries": [
            "Bhutan"
        ]
    },
    {
        "code": "BWP",
        "number": "072",
        "digits": 2,
        "currency": "Pula",
        "countries": [
            "Botswana"
        ]
    },
    {
        "code": "BYN",
        "number": "933",
        "digits": 2,
        "currency": "Belarusian Ruble",
        "countries": [
            "Belarus"
        ]
    },
    {
        "code": "BZD",
        "number": "084",
        "digits": 2,
        "currency": "Belize Dollar",
        "countries": [
            "Belize"
        ]
    },
    {
        "code": "CAD",
        "number": "124",
        "digits": 2,
        "currency": "Canadian Dollar",
        "countries": [
            "Canada"
        ]
    },
    {
        "code": "CDF",
        "number": "976",
        "digits": 2,
        "currency": "Congolese Franc",
        "countries": [
            "Congo (The Democratic Republic of The)"
        ]
    },
    {
        "code": "CHE",
        "number": "947",
        "digits": 2,
        "currency": "WIR Euro",
        "countries": [
            "Switzerland"
        ]
    },
    {
        "code": "CHF",
        "number": "756",
        "digits": 2,
        "currency": "Swiss Franc",
        "countries": [
            "Liechtenstein",
            "Switzerland"
        ]
    },
    {
        "code": "CHW",
        "number": "948",
        "digits": 2,
        "currency": "WIR Franc",
        "countries": [
            "Switzerland"
        ]
    },
    {
        "code": "CLF",
        "number": "990",
        "digits": 4,
        "currency": "Unidad de Fomento",
        "countries": [
            "Chile"
        ]
    },
    {
        "code": "CLP",
        "number": "152",
        "digits": 0,
        "currency": "Chilean Peso",
        "countries": [
            "Chile"
        ]
    },
    {
        "code": "CNY",
        "number": "156",
        "digits": 2,
        "currency": "Yuan Renminbi",
        "countries": [
            "China"
        ]
    },
    {
        "code": "COP",
        "number": "170",
        "digits": 2,
        "currency": "Colombian Peso",
        "countries": [
            "Colombia"
        ]
    },
    {
        "code": "COU",
        "number": "970",
        "digits": 2,
        "currency": "Unidad de Valor Real",
        "countries": [
            "Colombia"
        ]
    },
    {
        "code": "CRC",
        "number": "188",
        "digits": 2,
        "currency": "Costa Rican Colon",
        "countries": [
            "Costa Rica"
        ]
    },
    {
        "code": "CUC",
        "number": "931",
        "digits": 2,
        "currency": "Peso Convertible",
        "countries": [
            "Cuba"
        ]
    },
    {
        "code": "CUP",
        "number": "192",
        "digits": 2,
        "currency": "Cuban Peso",
        "countries": [
            "Cuba"
        ]
    },
    {
        "code": "CVE",
        "number": "132",
        "digits": 2,
        "currency": "Cabo Verde Escudo",
        "countries": [
            "Cabo Verde"
        ]
    },
    {
        "code": "CZK",
        "number": "203",
        "digits": 2,
        "currency": "Czech Koruna",
        "countries": [
            "Czechia"
        ]
    },
    {
        "code": "DJF",
        "number": "262",
        "digits": 0,
        "currency": "Djibouti Franc",
        "countries": [
            "Djibouti"
        ]
    },
    {
        "code": "DKK",
        "number": "208",
        "digits": 2,
        "currency": "Danish Krone",
        "countries": [
            "Denmark",
            "Faroe Islands (The)",
            "Greenland"
        ]
    },
    {
        "code": "DOP",
        "number": "214",
        "digits": 2,
        "currency": "Dominican Peso",
        "countries": [
            "Dominican Republic (The)"
        ]
    },
    {
        "code": "DZD",
        "number": "012",
        "digits": 2,
        "currency": "Algerian Dinar",
        "countries": [
            "Algeria"
        ]
    },
    {
        "code": "EGP",
        "number": "818",
        "digits": 2,
        "currency": "Egyptian Pound",
        "countries": [
            "Egypt"
        ]
    },
    {
        "code": "ERN",
        "number": "232",
        "digits": 2,
        "currency": "Nakfa",
        "countries": [
            "Eritrea"
        ]
    },
    {
        "code": "ETB",
        "number": "230",
        "digits": 2,
        "currency": "Ethiopian Birr",
        "countries": [
            "Ethiopia"
        ]
    },
    {
        "code": "EUR",
        "number": "978",
        "digits": 2,
        "currency": "Euro",
        "countries": [
            "Åland Islands",
            "Andorra",
            "Austria",
            "Belgium",
            "Croatia",
            "Cyprus",
            "Estonia",
            "European Union",
            "Finland",
            "France",
            "French Guiana",
            "French Southern Territories (The)",
            "Germany",
            "Greece",
            "Guadeloupe",
            "Holy See (The)",
            "Ireland",
            "Italy",
            "Latvia",
            "Lithuania",
            "Luxembourg",
            "Malta",
            "Martinique",
            "Mayotte",
            "Monaco",
            "Montenegro",
            "Netherlands (The)",
            "Portugal",
            "Réunion",
            "Saint Barthélemy",
            "Saint Martin (French Part)",
            "Saint Pierre and Miquelon",
            "San Marino",
            "Slovakia",
            "Slovenia",
            "Spain"
        ]
    },
    {
        "code": "FJD",
        "number": "242",
        "digits": 2,
        "currency": "Fiji Dollar",
        "countries": [
            "Fiji"
        ]
    },
    {
        "code": "FKP",
        "number": "238",
        "digits": 2,
        "currency": "Falkland Islands Pound",
        "countries": [
            "Falkland Islands (The) [Malvinas]"
        ]
    },
    {
        "code": "GBP",
        "number": "826",
        "digits": 2,
        "currency": "Pound Sterling",
        "countries": [
            "Guernsey",
            "Isle of Man",
            "Jersey",
            "United Kingdom of Great Britain and Northern Ireland (The)"
        ]
    },
    {
        "code": "GEL",
        "number": "981",
        "digits": 2,
        "currency": "Lari",
        "countries": [
            "Georgia"
        ]
    },
    {
        "code": "GHS",
        "number": "936",
        "digits": 2,
        "currency": "Ghana Cedi",
        "countries": [
            "Ghana"
        ]
    },
    {
        "code": "GIP",
        "number": "292",
        "digits": 2,
        "currency": "Gibraltar Pound",
        "countries": [
            "Gibraltar"
        ]
    },
    {
        "code": "GMD",
        "number": "270",
        "digits": 2,
        "currency": "Dalasi",
        "countries": [
            "Gambia (The)"
        ]
    },
    {
        "code": "GNF",
        "number": "324",
        "digits": 0,
        "currency": "Guinean Franc",
        "countries": [
            "Guinea"
        ]
    },
    {
        "code": "GTQ",
        "number": "320",
        "digits": 2,
        "currency": "Quetzal",
        "countries": [
            "Guatemala"
        ]
    },
    {
        "code": "GYD",
        "number": "328",
        "digits": 2,
        "currency": "Guyana Dollar",
        "countries": [
            "Guyana"
        ]
    },
    {
        "code": "HKD",
        "number": "344",
        "digits": 2,
        "currency": "Hong Kong Dollar",
        "countries": [
            "Hong Kong"
        ]
    },
    {
        "code": "HNL",
        "number": "340",
        "digits": 2,
        "currency": "Lempira",
        "countries": [
            "Honduras"
        ]
    },
    {
        "code": "HTG",
        "number": "332",
        "digits": 2,
        "currency": "Gourde",
        "countries": [
            "Haiti"
        ]
    },
    {
        "code": "HUF",
        "number": "348",
        "digits": 2,
        "currency": "Forint",
        "countries": [
            "Hungary"
        ]
    },
    {
        "code": "IDR",
        "number": "360",
        "digits": 2,
        "currency": "Rupiah",
        "countries": [
            "Indonesia"
        ]
    },
    {
        "code": "ILS",
        "number": "376",
        "digits": 2,
        "currency": "New Israeli Sheqel",
        "countries": [
            "Israel"
        ]
    },
    {
        "code": "INR",
        "number": "356",
        "digits": 2,
        "currency": "Indian Rupee",
        "countries": [
            "Bhutan",
            "India"
        ]
    },
    {
        "code": "IQD",
        "number": "368",
        "digits": 3,
        "currency": "Iraqi Dinar",
        "countries": [
            "Iraq"
        ]
    },
    {
        "code": "IRR",
        "number": "364",
        "digits": 2,
        "currency": "Iranian Rial",
        "countries": [
            "Iran (Islamic Republic Of)"
        ]
    },
    {
        "code": "ISK",
        "number": "352",
        "digits": 0,
        "currency": "Iceland Krona",
        "countries": [
            "Iceland"
        ]
    },
    {
        "code": "JMD",
        "number": "388",
        "digits": 2,
        "currency": "Jamaican Dollar",
        "countries": [
            "Jamaica"
        ]
    },
    {
        "code": "JOD",
        "number": "400",
        "digits": 3,
        "currency": "Jordanian Dinar",
        "countries": [
            "Jordan"
        ]
    },
    {
        "code": "JPY",
        "number": "392",
        "digits": 0,
        "currency": "Yen",
        "countries": [
            "Japan"
        ]
    },
    {
        "code": "KES",
        "number": "404",
        "digits": 2,
        "currency": "Kenyan Shilling",
        "countries": [
            "Kenya"
        ]
    },
    {
        "code": "KGS",
        "number": "417",
        "digits": 2,
        "currency": "Som",
        "countries": [
            "Kyrgyzstan"
        ]
    },
    {
        "code": "KHR",
        "number": "116",
        "digits": 2,
        "currency": "Riel",
        "countries": [
            "Cambodia"
        ]
    },
    {
        "code": "KMF",
        "number": "174",
        "digits": 0,
        "currency": "Comorian Franc ",
        "countries": [
            "Comoros (The)"
        ]
    },
    {
        "code": "KPW",
        "number": "408",
        "digits": 2,
        "currency": "North Korean Won",
        "countries": [
            "Korea (The Democratic People’s Republic Of)"
        ]
    },
    {
        "code": "KRW",
        "number": "410",
        "digits": 0,
        "currency": "Won",
        "countries": [
            "Korea (The Republic Of)"
        ]
    },
    {
        "code": "KWD",
        "number": "414",
        "digits": 3,
        "currency": "Kuwaiti Dinar",
        "countries": [
            "Kuwait"
        ]
    },
    {
        "code": "KYD",
        "number": "136",
        "digits": 2,
        "currency": "Cayman Islands Dollar",
        "countries": [
            "Cayman Islands (The)"
        ]
    },
    {
        "code": "KZT",
        "number": "398",
        "digits": 2,
        "currency": "Tenge",
        "countries": [
            "Kazakhstan"
        ]
    },
    {
        "code": "LAK",
        "number": "418",
        "digits": 2,
        "currency": "Lao Kip",
        "countries": [
            "Lao People’s Democratic Republic (The)"
        ]
    },
    {
        "code": "LBP",
        "number": "422",
        "digits": 2,
        "currency": "Lebanese Pound",
        "countries": [
            "Lebanon"
        ]
    },
    {
        "code": "LKR",
        "number": "144",
        "digits": 2,
        "currency": "Sri Lanka Rupee",
        "countries": [
            "Sri Lanka"
        ]
    },
    {
        "code": "LRD",
        "number": "430",
        "digits": 2,
        "currency": "Liberian Dollar",
        "countries": [
            "Liberia"
        ]
    },
    {
        "code": "LSL",
        "number": "426",
        "digits": 2,
        "currency": "Loti",
        "countries": [
            "Lesotho"
        ]
    },
    {
        "code": "LYD",
        "number": "434",
        "digits": 3,
        "currency": "Libyan Dinar",
        "countries": [
            "Libya"
        ]
    },
    {
        "code": "MAD",
        "number": "504",
        "digits": 2,
        "currency": "Moroccan Dirham",
        "countries": [
            "Morocco",
            "Western Sahara"
        ]
    },
    {
        "code": "MDL",
        "number": "498",
        "digits": 2,
        "currency": "Moldovan Leu",
        "countries": [
            "Moldova (The Republic Of)"
        ]
    },
    {
        "code": "MGA",
        "number": "969",
        "digits": 2,
        "currency": "Malagasy Ariary",
        "countries": [
            "Madagascar"
        ]
    },
    {
        "code": "MKD",
        "number": "807",
        "digits": 2,
        "currency": "Denar",
        "countries": [
            "North Macedonia"
        ]
    },
    {
        "code": "MMK",
        "number": "104",
        "digits": 2,
        "currency": "Kyat",
        "countries": [
            "Myanmar"
        ]
    },
    {
        "code": "MNT",
        "number": "496",
        "digits": 2,
        "currency": "Tugrik",
        "countries": [
            "Mongolia"
        ]
    },
    {
        "code": "MOP",
        "number": "446",
        "digits": 2,
        "currency": "Pataca",
        "countries": [
            "Macao"
        ]
    },
    {
        "code": "MRU",
        "number": "929",
        "digits": 2,
        "currency": "Ouguiya",
        "countries": [
            "Mauritania"
        ]
    },
    {
        "code": "MUR",
        "number": "480",
        "digits": 2,
        "currency": "Mauritius Rupee",
        "countries": [
            "Mauritius"
        ]
    },
    {
        "code": "MVR",
        "number": "462",
        "digits": 2,
        "currency": "Rufiyaa",
        "countries": [
            "Maldives"
        ]
    },
    {
        "code": "MWK",
        "number": "454",
        "digits": 2,
        "currency": "Malawi Kwacha",
        "countries": [
            "Malawi"
        ]
    },
    {
        "code": "MXN",
        "number": "484",
        "digits": 2,
        "currency": "Mexican Peso",
        "countries": [
            "Mexico"
        ]
    },
    {
        "code": "MXV",
        "number": "979",
        "digits": 2,
        "currency": "Mexican Unidad de Inversion (UDI)",
        "countries": [
            "Mexico"
        ]
    },
    {
        "code": "MYR",
        "number": "458",
        "digits": 2,
        "currency": "Malaysian Ringgit",
        "countries": [
            "Malaysia"
        ]
    },
    {
        "code": "MZN",
        "number": "943",
        "digits": 2,
        "currency": "Mozambique Metical",
        "countries": [
            "Mozambique"
        ]
    },
    {
        "code": "NAD",
        "number": "516",
        "digits": 2,
        "currency": "Namibia Dollar",
        "countries": [
            "Namibia"
        ]
    },
    {
        "code": "NGN",
        "number": "566",
        "digits": 2,
        "currency": "Naira",
        "countries": [
            "Nigeria"
        ]
    },
    {
        "code": "NIO",
        "number": "558",
        "digits": 2,
        "currency": "Cordoba Oro",
        "countries": [
            "Nicaragua"
        ]
    },
    {
        "code": "NOK",
        "number": "578",
        "digits": 2,
        "currency": "Norwegian Krone",
        "countries": [
            "Bouvet Island",
            "Norway",
            "Svalbard and Jan Mayen"
        ]
    },
    {
        "code": "NPR",
        "number": "524",
        "digits": 2,
        "currency": "Nepalese Rupee",
        "countries": [
            "Nepal"
        ]
    },
    {
        "code": "NZD",
        "number": "554",
        "digits": 2,
        "currency": "New Zealand Dollar",
        "countries": [
            "Cook Islands (The)",
            "New Zealand",
            "Niue",
            "Pitcairn",
            "Tokelau"
        ]
    },
    {
        "code": "OMR",
        "number": "512",
        "digits": 3,
        "currency": "Rial Omani",
        "countries": [
            "Oman"
        ]
    },
    {
        "code": "PAB",
        "number": "590",
        "digits": 2,
        "currency": "Balboa",
        "countries": [
            "Panama"
        ]
    },
    {
        "code": "PEN",
        "number": "604",
        "digits": 2,
        "currency": "Sol",
        "countries": [
            "Peru"
        ]
    },
    {
        "code": "PGK",
        "number": "598",
        "digits": 2,
        "currency": "Kina",
        "countries": [
            "Papua New Guinea"
        ]
    },
    {
        "code": "PHP",
        "number": "608",
        "digits": 2,
        "currency": "Philippine Peso",
        "countries": [
            "Philippines (The)"
        ]
    },
    {
        "code": "PKR",
        "number": "586",
        "digits": 2,
        "currency": "Pakistan Rupee",
        "countries": [
            "Pakistan"
        ]
    },
    {
        "code": "PLN",
        "number": "985",
        "digits": 2,
        "currency": "Zloty",
        "countries": [
            "Poland"
        ]
    },
    {
        "code": "PYG",
        "number": "600",
        "digits": 0,
        "currency": "Guarani",
        "countries": [
            "Paraguay"
        ]
    },
    {
        "code": "QAR",
        "number": "634",
        "digits": 2,
        "currency": "Qatari Rial",
        "countries": [
            "Qatar"
        ]
    },
    {
        "code": "RON",
        "number": "946",
        "digits": 2,
        "currency": "Romanian Leu",
        "countries": [
            "Romania"
        ]
    },
    {
        "code": "RSD",
        "number": "941",
        "digits": 2,
        "currency": "Serbian Dinar",
        "countries": [
            "Serbia"
        ]
    },
    {
        "code": "RUB",
        "number": "643",
        "digits": 2,
        "currency": "Russian Ruble",
        "countries": [
            "Russian Federation (The)"
        ]
    },
    {
        "code": "RWF",
        "number": "646",
        "digits": 0,
        "currency": "Rwanda Franc",
        "countries": [
            "Rwanda"
        ]
    },
    {
        "code": "SAR",
        "number": "682",
        "digits": 2,
        "currency": "Saudi Riyal",
        "countries": [
            "Saudi Arabia"
        ]
    },
    {
        "code": "SBD",
        "number": "090",
        "digits": 2,
        "currency": "Solomon Islands Dollar",
        "countries": [
            "Solomon Islands"
        ]
    },
    {
        "code": "SCR",
        "number": "690",
        "digits": 2,
        "currency": "Seychelles Rupee",
        "countries": [
            "Seychelles"
        ]
    },
    {
        "code": "SDG",
        "number": "938",
        "digits": 2,
        "currency": "Sudanese Pound",
        "countries": [
            "Sudan (The)"
        ]
    },
    {
        "code": "SEK",
        "number": "752",
        "digits": 2,
        "currency": "Swedish Krona",
        "countries": [
            "Sweden"
        ]
    },
    {
        "code": "SGD",
        "number": "702",
        "digits": 2,
        "currency": "Singapore Dollar",
        "countries": [
            "Singapore"
        ]
    },
    {
        "code": "SHP",
        "number": "654",
        "digits": 2,
        "currency": "Saint Helena Pound",
        "countries": [
            "Saint Helena, Ascension and Tristan Da Cunha"
        ]
    },
    {
        "code": "SLE",
        "number": "925",
        "digits": 2,
        "currency": "Leone",
        "countries": [
            "Sierra Leone"
        ]
    },
    {
        "code": "SOS",
        "number": "706",
        "digits": 2,
        "currency": "Somali Shilling",
        "countries": [
            "Somalia"
        ]
    },
    {
        "code": "SRD",
        "number": "968",
        "digits": 2,
        "currency": "Surinam Dollar",
        "countries": [
            "Suriname"
        ]
    },
    {
        "code": "SSP",
        "number": "728",
        "digits": 2,
        "currency": "South Sudanese Pound",
        "countries": [
            "South Sudan"
        ]
    },
    {
        "code": "STN",
        "number": "930",
        "digits": 2,
        "currency": "Dobra",
        "countries": [
            "Sao Tome and Principe"
        ]
    },
    {
        "code": "SVC",
        "number": "222",
        "digits": 2,
        "currency": "El Salvador Colon",
        "countries": [
            "El Salvador"
        ]
    },
    {
        "code": "SYP",
        "number": "760",
        "digits": 2,
        "currency": "Syrian Pound",
        "countries": [
            "Syrian Arab Republic"
        ]
    },
    {
        "code": "SZL",
        "number": "748",
        "digits": 2,
        "currency": "Lilangeni",
        "countries": [
            "Eswatini"
        ]
    },
    {
        "code": "THB",
        "number": "764",
        "digits": 2,
        "currency": "Baht",
        "countries": [
            "Thailand"
        ]
    },
    {
        "code": "TJS",
        "number": "972",
        "digits": 2,
        "currency": "Somoni",
        "countries": [
            "Tajikistan"
        ]
    },
    {
        "code": "TMT",
        "number": "934",
        "digits": 2,
        "currency": "Turkmenistan New Manat",
        "countries": [
            "Turkmenistan"
        ]
    },
    {
        "code": "TND",
        "number": "788",
        "digits": 3,
        "currency": "Tunisian Dinar",
        "countries": [
            "Tunisia"
        ]
    },
    {
        "code": "TOP",
        "number": "776",
        "digits": 2,
        "currency": "Pa’anga",
        "countries": [
            "Tonga"
        ]
    },
    {
        "code": "TRY",
        "number": "949",
        "digits": 2,
        "currency": "Turkish Lira",
        "countries": [
            "Türki̇ye"
        ]
    },
    {
        "code": "TTD",
        "number": "780",
        "digits": 2,
        "currency": "Trinidad and Tobago Dollar",
        "countries": [
            "Trinidad and Tobago"
        ]
    },
    {
        "code": "TWD",
        "number": "901",
        "digits": 2,
        "currency": "New Taiwan Dollar",
        "countries": [
            "Taiwan (Province of China)"
        ]
    },
    {
        "code": "TZS",
        "number": "834",
        "digits": 2,
        "currency": "Tanzanian Shilling",
        "countries": [
            "Tanzania, United Republic Of"
        ]
    },
    {
        "code": "UAH",
        "number": "980",
        "digits": 2,
        "currency": "Hryvnia",
        "countries": [
            "Ukraine"
        ]
    },
    {
        "code": "UGX",
        "number": "800",
        "digits": 0,
        "currency": "Uganda Shilling",
        "countries": [
            "Uganda"
        ]
    },
    {
        "code": "USD",
        "number": "840",
        "digits": 2,
        "currency": "US Dollar",
        "countries": [
            "American Samoa",
            "Bonaire, Sint Eustatius and Saba",
            "British Indian Ocean Territory (The)",
            "Ecuador",
            "El Salvador",
            "Guam",
            "Haiti",
            "Marshall Islands (The)",
            "Micronesia (Federated States Of)",
            "Northern Mariana Islands (The)",
            "Palau",
            "Panama",
            "Puerto Rico",
            "Timor-Leste",
            "Turks and Caicos Islands (The)",
            "United States Minor Outlying Islands (The)",
            "United States of America (The)",
            "Virgin Islands (British)",
            "Virgin Islands (u.s.)"
        ]
    },
    {
        "code": "USN",
        "number": "997",
        "digits": 2,
        "currency": "US Dollar (Next day)",
        "countries": [
            "United States of America (The)"
        ]
    },
    {
        "code": "UYI",
        "number": "940",
        "digits": 0,
        "currency": "Uruguay Peso en Unidades Indexadas (UI)",
        "countries": [
            "Uruguay"
        ]
    },
    {
        "code": "UYU",
        "number": "858",
        "digits": 2,
        "currency": "Peso Uruguayo",
        "countries": [
            "Uruguay"
        ]
    },
    {
        "code": "UYW",
        "number": "927",
        "digits": 4,
        "currency": "Unidad Previsional",
        "countries": [
            "Uruguay"
        ]
    },
    {
        "code": "UZS",
        "number": "860",
        "digits": 2,
        "currency": "Uzbekistan Sum",
        "countries": [
            "Uzbekistan"
        ]
    },
    {
        "code": "VED",
        "number": "926",
        "digits": 2,
        "currency": "Bolívar Soberano",
        "countries": [
            "Venezuela (Bolivarian Republic Of)"
        ]
    },
    {
        "code": "VES",
        "number": "928",
        "digits": 2,
        "currency": "Bolívar Soberano",
        "countries": [
            "Venezuela (Bolivarian Republic Of)"
        ]
    },
    {
        "code": "VND",
        "number": "704",
        "digits": 0,
        "currency": "Dong",
        "countries": [
            "Viet Nam"
        ]
    },
    {
        "code": "VUV",
        "number": "548",
        "digits": 0,
        "currency": "Vatu",
        "countries": [
            "Vanuatu"
        ]
    },
    {
        "code": "WST",
        "number": "882",
        "digits": 2,
        "currency": "Tala",
        "countries": [
            "Samoa"
        ]
    },
    {
        "code": "XAF",
        "number": "950",
        "digits": 0,
        "currency": "CFA Franc BEAC",
        "countries": [
            "Cameroon",
            "Central African Republic (The)",
            "Chad",
            "Congo (The)",
            "Equatorial Guinea",
            "Gabon"
        ]
    },
    {
        "code": "XAG",
        "number": "961",
        "digits": 0,
        "currency": "Silver",
        "countries": [
            "Zz11_silver"
        ]
    },
    {
        "code": "XAU",
        "number": "959",
        "digits": 0,
        "currency": "Gold",
        "countries": [
            "Zz08_gold"
        ]
    },
    {
        "code": "XBA",
        "number": "955",
        "digits": 0,
        "currency": "Bond Markets Unit European Composite Unit (EURCO)",
        "countries": [
            "Zz01_bond Markets Unit European_eurco"
        ]
    },
    {
        "code": "XBB",
        "number": "956",
        "digits": 0,
        "currency": "Bond Markets Unit European Monetary Unit (E.M.U.-6)",
        "countries": [
            "Zz02_bond Markets Unit European_emu-6"
        ]
    },
    {
        "code": "XBC",
        "number": "957",
        "digits": 0,
        "currency": "Bond Markets Unit European Unit of Account 9 (E.U.A.-9)",
        "countries": [
            "Zz03_bond Markets Unit European_eua-9"
        ]
    },
    {
        "code": "XBD",
        "number": "958",
        "digits": 0,
        "currency": "Bond Markets Unit European Unit of Account 17 (E.U.A.-17)",
        "countries": [
            "Zz04_bond Markets Unit European_eua-17"
        ]
    },
    {
        "code": "XCD",
        "number": "951",
        "digits": 2,
        "currency": "East Caribbean Dollar",
        "countries": [
            "Anguilla",
            "Antigua and Barbuda",
            "Dominica",
            "Grenada",
            "Montserrat",
            "Saint Kitts and Nevis",
            "Saint Lucia",
            "Saint Vincent and the Grenadines"
        ]
    },
    {
        "code": "XDR",
        "number": "960",
        "digits": 0,
        "currency": "SDR (Special Drawing Right)",
        "countries": [
            "International Monetary Fund (Imf) "
        ]
    },
    {
        "code": "XOF",
        "number": "952",
        "digits": 0,
        "currency": "CFA Franc BCEAO",
        "countries": [
            "Benin",
            "Burkina Faso",
            "Côte D'ivoire",
            "Guinea-Bissau",
            "Mali",
            "Niger (The)",
            "Senegal",
            "Togo"
        ]
    },
    {
        "code": "XPD",
        "number": "964",
        "digits": 0,
        "currency": "Palladium",
        "countries": [
            "Zz09_palladium"
        ]
    },
    {
        "code": "XPF",
        "number": "953",
        "digits": 0,
        "currency": "CFP Franc",
        "countries": [
            "French Polynesia",
            "New Caledonia",
            "Wallis and Futuna"
        ]
    },
    {
        "code": "XPT",
        "number": "962",
        "digits": 0,
        "currency": "Platinum",
        "countries": [
            "Zz10_platinum"
        ]
    },
    {
        "code": "XSU",
        "number": "994",
        "digits": 0,
        "currency": "Sucre",
        "countries": [
            "Sistema Unitario De Compensacion Regional De Pagos \"Sucre\""
        ]
    },
    {
        "code": "XTS",
        "number": "963",
        "digits": 0,
        "currency": "Codes specifically reserved for testing purposes",
        "countries": [
            "Zz06_testing_code"
        ]
    },
    {
        "code": "XUA",
        "number": "965",
        "digits": 0,
        "currency": "ADB Unit of Account",
        "countries": [
            "Member Countries of the African Development Bank Group"
        ]
    },
    {
        "code": "XXX",
        "number": "999",
        "digits": 0,
        "currency": "The codes assigned for transactions where no currency is involved",
        "countries": [
            "Zz07_no_currency"
        ]
    },
    {
        "code": "YER",
        "number": "886",
        "digits": 2,
        "currency": "Yemeni Rial",
        "countries": [
            "Yemen"
        ]
    },
    {
        "code": "ZAR",
        "number": "710",
        "digits": 2,
        "currency": "Rand",
        "countries": [
            "Lesotho",
            "Namibia",
            "South Africa"
        ]
    },
    {
        "code": "ZMW",
        "number": "967",
        "digits": 2,
        "currency": "Zambian Kwacha",
        "countries": [
            "Zambia"
        ]
    },
    {
        "code": "ZWG",
        "number": "924",
        "digits": 2,
        "currency": "Zimbabwe Gold",
        "countries": [
            "Zimbabwe"
        ]
    }
];

export const country_currency =[
    {
        "entity": "ANGUILLA",
        "currency": "East Caribbean Dollar",
        "alphabeticcode": "XCD",
        "numericcode": "951",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "CAMBODIA",
        "currency": "Riel",
        "alphabeticcode": "KHR",
        "numericcode": "116",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "COOK ISLANDS (THE)",
        "currency": "New Zealand Dollar",
        "alphabeticcode": "NZD",
        "numericcode": "554",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "COSTA RICA",
        "currency": "Costa Rican Colon",
        "alphabeticcode": "CRC",
        "numericcode": "188",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "ECUADOR",
        "currency": "US Dollar",
        "alphabeticcode": "USD",
        "numericcode": "840",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "HAITI",
        "currency": "US Dollar",
        "alphabeticcode": "USD",
        "numericcode": "840",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "INDIA",
        "currency": "Indian Rupee",
        "alphabeticcode": "INR",
        "numericcode": "356",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "MONTENEGRO",
        "currency": "Euro",
        "alphabeticcode": "EUR",
        "numericcode": "978",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "NEW CALEDONIA",
        "currency": "CFP Franc",
        "alphabeticcode": "XPF",
        "numericcode": "953",
        "minorunit": "0",
        "withdrawaldate": null
    },
    {
        "entity": "NICARAGUA",
        "currency": "Cordoba Oro",
        "alphabeticcode": "NIO",
        "numericcode": "558",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "SAINT PIERRE AND MIQUELON",
        "currency": "Euro",
        "alphabeticcode": "EUR",
        "numericcode": "978",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "SRI LANKA",
        "currency": "Sri Lanka Rupee",
        "alphabeticcode": "LKR",
        "numericcode": "144",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "SURINAME",
        "currency": "Surinam Dollar",
        "alphabeticcode": "SRD",
        "numericcode": "968",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "TURKS AND CAICOS ISLANDS (THE)",
        "currency": "US Dollar",
        "alphabeticcode": "USD",
        "numericcode": "840",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "URUGUAY",
        "currency": "Unidad Previsional",
        "alphabeticcode": "UYW",
        "numericcode": "927",
        "minorunit": "4",
        "withdrawaldate": null
    },
    {
        "entity": "VIRGIN ISLANDS (BRITISH)",
        "currency": "US Dollar",
        "alphabeticcode": "USD",
        "numericcode": "840",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "ZZ11_Silver",
        "currency": "Silver",
        "alphabeticcode": "XAG",
        "numericcode": "961",
        "minorunit": "-",
        "withdrawaldate": null
    },
    {
        "entity": "GERMANY",
        "currency": "Deutsche Mark",
        "alphabeticcode": "DEM",
        "numericcode": "276",
        "minorunit": null,
        "withdrawaldate": "2002-03"
    },
    {
        "entity": "LESOTHO",
        "currency": "Financial Rand",
        "alphabeticcode": "ZAL",
        "numericcode": "991",
        "minorunit": null,
        "withdrawaldate": "1995-03"
    },
    {
        "entity": "ZZ05_UIC-Franc",
        "currency": "UIC-Franc",
        "alphabeticcode": "XFU",
        "numericcode": null,
        "minorunit": null,
        "withdrawaldate": "2013-11"
    },
    {
        "entity": "BONAIRE, SINT EUSTATIUS AND SABA",
        "currency": "US Dollar",
        "alphabeticcode": "USD",
        "numericcode": "840",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "CAMEROON",
        "currency": "CFA Franc BEAC",
        "alphabeticcode": "XAF",
        "numericcode": "950",
        "minorunit": "0",
        "withdrawaldate": null
    },
    {
        "entity": "DJIBOUTI",
        "currency": "Djibouti Franc",
        "alphabeticcode": "DJF",
        "numericcode": "262",
        "minorunit": "0",
        "withdrawaldate": null
    },
    {
        "entity": "GUATEMALA",
        "currency": "Quetzal",
        "alphabeticcode": "GTQ",
        "numericcode": "320",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "IRELAND",
        "currency": "Euro",
        "alphabeticcode": "EUR",
        "numericcode": "978",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "NETHERLANDS (THE)",
        "currency": "Euro",
        "alphabeticcode": "EUR",
        "numericcode": "978",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "NORTHERN MARIANA ISLANDS (THE)",
        "currency": "US Dollar",
        "alphabeticcode": "USD",
        "numericcode": "840",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "PANAMA",
        "currency": "US Dollar",
        "alphabeticcode": "USD",
        "numericcode": "840",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "SOUTH GEORGIA AND THE SOUTH SANDWICH ISLANDS",
        "currency": "No universal currency",
        "alphabeticcode": null,
        "numericcode": null,
        "minorunit": null,
        "withdrawaldate": null
    },
    {
        "entity": "TOKELAU",
        "currency": "New Zealand Dollar",
        "alphabeticcode": "NZD",
        "numericcode": "554",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "BURMA",
        "currency": "Kyat",
        "alphabeticcode": "BUK",
        "numericcode": "104",
        "minorunit": null,
        "withdrawaldate": "1990-02"
    },
    {
        "entity": "FRANCE",
        "currency": "French Franc",
        "alphabeticcode": "FRF",
        "numericcode": "250",
        "minorunit": null,
        "withdrawaldate": "2002-03"
    },
    {
        "entity": "MEXICO",
        "currency": "Mexican Peso",
        "alphabeticcode": "MXP",
        "numericcode": "484",
        "minorunit": null,
        "withdrawaldate": "1993-01"
    },
    {
        "entity": "NETHERLANDS",
        "currency": "Netherlands Guilder",
        "alphabeticcode": "NLG",
        "numericcode": "528",
        "minorunit": null,
        "withdrawaldate": "2002-03"
    },
    {
        "entity": "UGANDA",
        "currency": "Old Shilling",
        "alphabeticcode": "UGW",
        "numericcode": "800",
        "minorunit": null,
        "withdrawaldate": "1989 to 1990"
    },
    {
        "entity": "YUGOSLAVIA",
        "currency": "Yugoslavian Dinar",
        "alphabeticcode": "YUN",
        "numericcode": "890",
        "minorunit": null,
        "withdrawaldate": "1995-11"
    },
    {
        "entity": "BARBADOS",
        "currency": "Barbados Dollar",
        "alphabeticcode": "BBD",
        "numericcode": "052",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "CHAD",
        "currency": "CFA Franc BEAC",
        "alphabeticcode": "XAF",
        "numericcode": "950",
        "minorunit": "0",
        "withdrawaldate": null
    },
    {
        "entity": "COCOS (KEELING) ISLANDS (THE)",
        "currency": "Australian Dollar",
        "alphabeticcode": "AUD",
        "numericcode": "036",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "EL SALVADOR",
        "currency": "US Dollar",
        "alphabeticcode": "USD",
        "numericcode": "840",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "LUXEMBOURG",
        "currency": "Euro",
        "alphabeticcode": "EUR",
        "numericcode": "978",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "POLAND",
        "currency": "Zloty",
        "alphabeticcode": "PLN",
        "numericcode": "985",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "PUERTO RICO",
        "currency": "US Dollar",
        "alphabeticcode": "USD",
        "numericcode": "840",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "SAO TOME AND PRINCIPE",
        "currency": "Dobra",
        "alphabeticcode": "STN",
        "numericcode": "930",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "SVALBARD AND JAN MAYEN",
        "currency": "Norwegian Krone",
        "alphabeticcode": "NOK",
        "numericcode": "578",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "ZZ01_Bond Markets Unit European_EURCO",
        "currency": "Bond Markets Unit European Composite Unit (EURCO)",
        "alphabeticcode": "XBA",
        "numericcode": "955",
        "minorunit": "-",
        "withdrawaldate": null
    },
    {
        "entity": "ZZ02_Bond Markets Unit European_EMU-6",
        "currency": "Bond Markets Unit European Monetary Unit (E.M.U.-6)",
        "alphabeticcode": "XBB",
        "numericcode": "956",
        "minorunit": "-",
        "withdrawaldate": null
    },
    {
        "entity": "ANDORRA",
        "currency": "Andorran Peseta",
        "alphabeticcode": "ADP",
        "numericcode": "020",
        "minorunit": null,
        "withdrawaldate": "2003-07"
    },
    {
        "entity": "GUINEA-BISSAU",
        "currency": "Guinea-Bissau Peso",
        "alphabeticcode": "GWP",
        "numericcode": "624",
        "minorunit": null,
        "withdrawaldate": "1997-05"
    },
    {
        "entity": "HOLY SEE (VATICAN CITY STATE)",
        "currency": "Italian Lira",
        "alphabeticcode": "ITL",
        "numericcode": "380",
        "minorunit": null,
        "withdrawaldate": "2002-03"
    },
    {
        "entity": "IRELAND",
        "currency": "Irish Pound",
        "alphabeticcode": "IEP",
        "numericcode": "372",
        "minorunit": null,
        "withdrawaldate": "2002-03"
    },
    {
        "entity": "ISRAEL",
        "currency": "Old Shekel",
        "alphabeticcode": "ILR",
        "numericcode": "376",
        "minorunit": null,
        "withdrawaldate": "1989 to 1990"
    },
    {
        "entity": "LUXEMBOURG",
        "currency": "Luxembourg Convertible Franc",
        "alphabeticcode": "LUC",
        "numericcode": "989",
        "minorunit": null,
        "withdrawaldate": "1990-03"
    },
    {
        "entity": "LUXEMBOURG",
        "currency": "Luxembourg Financial Franc",
        "alphabeticcode": "LUL",
        "numericcode": "988",
        "minorunit": null,
        "withdrawaldate": "1990-03"
    },
    {
        "entity": "ROMANIA",
        "currency": "Old Leu",
        "alphabeticcode": "ROL",
        "numericcode": "642",
        "minorunit": null,
        "withdrawaldate": "2005-06"
    },
    {
        "entity": "TAJIKISTAN",
        "currency": "Russian Ruble",
        "alphabeticcode": "RUR",
        "numericcode": "810",
        "minorunit": null,
        "withdrawaldate": "1995-05"
    },
    {
        "entity": "VENEZUELA",
        "currency": "Bolivar Fuerte",
        "alphabeticcode": "VEF",
        "numericcode": "937",
        "minorunit": null,
        "withdrawaldate": "2011-12"
    },
    {
        "entity": "ZIMBABWE",
        "currency": "Zimbabwe Dollar (new)",
        "alphabeticcode": "ZWN",
        "numericcode": "942",
        "minorunit": null,
        "withdrawaldate": "2006-09"
    },
    {
        "entity": "DOMINICA",
        "currency": "East Caribbean Dollar",
        "alphabeticcode": "XCD",
        "numericcode": "951",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "MALDIVES",
        "currency": "Rufiyaa",
        "alphabeticcode": "MVR",
        "numericcode": "462",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "NEW ZEALAND",
        "currency": "New Zealand Dollar",
        "alphabeticcode": "NZD",
        "numericcode": "554",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "PAPUA NEW GUINEA",
        "currency": "Kina",
        "alphabeticcode": "PGK",
        "numericcode": "598",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "PITCAIRN",
        "currency": "New Zealand Dollar",
        "alphabeticcode": "NZD",
        "numericcode": "554",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "UNITED ARAB EMIRATES (THE)",
        "currency": "UAE Dirham",
        "alphabeticcode": "AED",
        "numericcode": "784",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "UZBEKISTAN",
        "currency": "Uzbekistan Sum",
        "alphabeticcode": "UZS",
        "numericcode": "860",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "ZIMBABWE",
        "currency": "Zimbabwe Gold",
        "alphabeticcode": "ZWG",
        "numericcode": "924",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "AZERBAIJAN",
        "currency": "Azerbaijan Manat",
        "alphabeticcode": "AYM",
        "numericcode": "945",
        "minorunit": null,
        "withdrawaldate": "2005-10"
    },
    {
        "entity": "BELARUS",
        "currency": "Russian Ruble",
        "alphabeticcode": "RUR",
        "numericcode": "810",
        "minorunit": null,
        "withdrawaldate": "1994-06"
    },
    {
        "entity": "BOLIVIA",
        "currency": "Peso boliviano",
        "alphabeticcode": "BOP",
        "numericcode": "068",
        "minorunit": null,
        "withdrawaldate": "1987-02"
    },
    {
        "entity": "BULGARIA",
        "currency": "Lev A/52",
        "alphabeticcode": "BGJ",
        "numericcode": "100",
        "minorunit": null,
        "withdrawaldate": "1989 to 1990"
    },
    {
        "entity": "GUADELOUPE",
        "currency": "French Franc",
        "alphabeticcode": "FRF",
        "numericcode": "250",
        "minorunit": null,
        "withdrawaldate": "2002-03"
    },
    {
        "entity": "SINT MAARTEN (DUTCH PART)",
        "currency": "Netherlands Antillean Guilder",
        "alphabeticcode": "ANG",
        "numericcode": "532",
        "minorunit": null,
        "withdrawaldate": "2025-03"
    },
    {
        "entity": "SLOVAKIA",
        "currency": "Slovak Koruna",
        "alphabeticcode": "SKK",
        "numericcode": "703",
        "minorunit": null,
        "withdrawaldate": "2009-01"
    },
    {
        "entity": "BAHRAIN",
        "currency": "Bahraini Dinar",
        "alphabeticcode": "BHD",
        "numericcode": "048",
        "minorunit": "3",
        "withdrawaldate": null
    },
    {
        "entity": "CHILE",
        "currency": "Unidad de Fomento",
        "alphabeticcode": "CLF",
        "numericcode": "990",
        "minorunit": "4",
        "withdrawaldate": null
    },
    {
        "entity": "ERITREA",
        "currency": "Nakfa",
        "alphabeticcode": "ERN",
        "numericcode": "232",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "FINLAND",
        "currency": "Euro",
        "alphabeticcode": "EUR",
        "numericcode": "978",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "GUYANA",
        "currency": "Guyana Dollar",
        "alphabeticcode": "GYD",
        "numericcode": "328",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "MALTA",
        "currency": "Euro",
        "alphabeticcode": "EUR",
        "numericcode": "978",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "NORFOLK ISLAND",
        "currency": "Australian Dollar",
        "alphabeticcode": "AUD",
        "numericcode": "036",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "NORWAY",
        "currency": "Norwegian Krone",
        "alphabeticcode": "NOK",
        "numericcode": "578",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "PANAMA",
        "currency": "Balboa",
        "alphabeticcode": "PAB",
        "numericcode": "590",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "SENEGAL",
        "currency": "CFA Franc BCEAO",
        "alphabeticcode": "XOF",
        "numericcode": "952",
        "minorunit": "0",
        "withdrawaldate": null
    },
    {
        "entity": "ZZ06_Testing_Code",
        "currency": "Codes specifically reserved for testing purposes",
        "alphabeticcode": "XTS",
        "numericcode": "963",
        "minorunit": "-",
        "withdrawaldate": null
    },
    {
        "entity": "ARGENTINA",
        "currency": "Peso Argentino",
        "alphabeticcode": "ARP",
        "numericcode": "032",
        "minorunit": null,
        "withdrawaldate": "1985-07"
    },
    {
        "entity": "LITHUANIA",
        "currency": "Talonas",
        "alphabeticcode": "LTT",
        "numericcode": "440",
        "minorunit": null,
        "withdrawaldate": "1993-07"
    },
    {
        "entity": "MOZAMBIQUE",
        "currency": "Mozambique Metical",
        "alphabeticcode": "MZM",
        "numericcode": "508",
        "minorunit": null,
        "withdrawaldate": "2006-06"
    },
    {
        "entity": "PORTUGAL",
        "currency": "Portuguese Escudo",
        "alphabeticcode": "PTE",
        "numericcode": "620",
        "minorunit": null,
        "withdrawaldate": "2002-03"
    },
    {
        "entity": "SERBIA AND MONTENEGRO",
        "currency": "Serbian Dinar",
        "alphabeticcode": "CSD",
        "numericcode": "891",
        "minorunit": null,
        "withdrawaldate": "2006-10"
    },
    {
        "entity": "SIERRA LEONE",
        "currency": "Leone",
        "alphabeticcode": "SLL",
        "numericcode": "694",
        "minorunit": null,
        "withdrawaldate": "2023-12"
    },
    {
        "entity": "ZAMBIA",
        "currency": "Zambian Kwacha",
        "alphabeticcode": "ZMK",
        "numericcode": "894",
        "minorunit": null,
        "withdrawaldate": "2012-12"
    },
    {
        "entity": "AUSTRIA",
        "currency": "Euro",
        "alphabeticcode": "EUR",
        "numericcode": "978",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "DENMARK",
        "currency": "Danish Krone",
        "alphabeticcode": "DKK",
        "numericcode": "208",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "ITALY",
        "currency": "Euro",
        "alphabeticcode": "EUR",
        "numericcode": "978",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "PERU",
        "currency": "Sol",
        "alphabeticcode": "PEN",
        "numericcode": "604",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "R\u00c9UNION",
        "currency": "Euro",
        "alphabeticcode": "EUR",
        "numericcode": "978",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "SAINT KITTS AND NEVIS",
        "currency": "East Caribbean Dollar",
        "alphabeticcode": "XCD",
        "numericcode": "951",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "SISTEMA UNITARIO DE COMPENSACION REGIONAL DE PAGOS \"SUCRE\"",
        "currency": "Sucre",
        "alphabeticcode": "XSU",
        "numericcode": "994",
        "minorunit": "-",
        "withdrawaldate": null
    },
    {
        "entity": "TUNISIA",
        "currency": "Tunisian Dinar",
        "alphabeticcode": "TND",
        "numericcode": "788",
        "minorunit": "3",
        "withdrawaldate": null
    },
    {
        "entity": "UNITED STATES MINOR OUTLYING ISLANDS (THE)",
        "currency": "US Dollar",
        "alphabeticcode": "USD",
        "numericcode": "840",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "VANUATU",
        "currency": "Vatu",
        "alphabeticcode": "VUV",
        "numericcode": "548",
        "minorunit": "0",
        "withdrawaldate": null
    },
    {
        "entity": "PERU",
        "currency": "Inti",
        "alphabeticcode": "PEI",
        "numericcode": "604",
        "minorunit": null,
        "withdrawaldate": "1991-07"
    },
    {
        "entity": "POLAND",
        "currency": "Zloty",
        "alphabeticcode": "PLZ",
        "numericcode": "616",
        "minorunit": null,
        "withdrawaldate": "1997-01"
    },
    {
        "entity": "SPAIN",
        "currency": "Spanish Peseta",
        "alphabeticcode": "ESA",
        "numericcode": "996",
        "minorunit": null,
        "withdrawaldate": "1978 to 1981"
    },
    {
        "entity": "BOUVET ISLAND",
        "currency": "Norwegian Krone",
        "alphabeticcode": "NOK",
        "numericcode": "578",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "C\u00d4TE D'IVOIRE",
        "currency": "CFA Franc BCEAO",
        "alphabeticcode": "XOF",
        "numericcode": "952",
        "minorunit": "0",
        "withdrawaldate": null
    },
    {
        "entity": "CUBA",
        "currency": "Cuban Peso",
        "alphabeticcode": "CUP",
        "numericcode": "192",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "GERMANY",
        "currency": "Euro",
        "alphabeticcode": "EUR",
        "numericcode": "978",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "MOROCCO",
        "currency": "Moroccan Dirham",
        "alphabeticcode": "MAD",
        "numericcode": "504",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "NIGERIA",
        "currency": "Naira",
        "alphabeticcode": "NGN",
        "numericcode": "566",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "AFGHANISTAN",
        "currency": "Afghani",
        "alphabeticcode": "AFA",
        "numericcode": "004",
        "minorunit": null,
        "withdrawaldate": "2003-01"
    },
    {
        "entity": "ECUADOR",
        "currency": "Sucre",
        "alphabeticcode": "ECS",
        "numericcode": "218",
        "minorunit": null,
        "withdrawaldate": "2000-09"
    },
    {
        "entity": "FINLAND",
        "currency": "Markka",
        "alphabeticcode": "FIM",
        "numericcode": "246",
        "minorunit": null,
        "withdrawaldate": "2002-03"
    },
    {
        "entity": "PERU",
        "currency": "Sol",
        "alphabeticcode": "PES",
        "numericcode": "604",
        "minorunit": null,
        "withdrawaldate": "1986-02"
    },
    {
        "entity": "SAINT-BARTH\u00c9LEMY",
        "currency": "French Franc",
        "alphabeticcode": "FRF",
        "numericcode": "250",
        "minorunit": null,
        "withdrawaldate": "1999-01"
    },
    {
        "entity": "ANDORRA",
        "currency": "Euro",
        "alphabeticcode": "EUR",
        "numericcode": "978",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "BELARUS",
        "currency": "Belarusian Ruble",
        "alphabeticcode": "BYN",
        "numericcode": "933",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "ICELAND",
        "currency": "Iceland Krona",
        "alphabeticcode": "ISK",
        "numericcode": "352",
        "minorunit": "0",
        "withdrawaldate": null
    },
    {
        "entity": "KOREA (THE DEMOCRATIC PEOPLE\u2019S REPUBLIC OF)",
        "currency": "North Korean Won",
        "alphabeticcode": "KPW",
        "numericcode": "408",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "LIBERIA",
        "currency": "Liberian Dollar",
        "alphabeticcode": "LRD",
        "numericcode": "430",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "MARTINIQUE",
        "currency": "Euro",
        "alphabeticcode": "EUR",
        "numericcode": "978",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "SAINT LUCIA",
        "currency": "East Caribbean Dollar",
        "alphabeticcode": "XCD",
        "numericcode": "951",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "UNITED KINGDOM OF GREAT BRITAIN AND NORTHERN IRELAND (THE)",
        "currency": "Pound Sterling",
        "alphabeticcode": "GBP",
        "numericcode": "826",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "ARGENTINA",
        "currency": "Austral",
        "alphabeticcode": "ARA",
        "numericcode": "032",
        "minorunit": null,
        "withdrawaldate": "1992-01"
    },
    {
        "entity": "GERMAN DEMOCRATIC REPUBLIC",
        "currency": "Mark der DDR",
        "alphabeticcode": "DDM",
        "numericcode": "278",
        "minorunit": null,
        "withdrawaldate": "1990-07 to 1990-09"
    },
    {
        "entity": "GHANA",
        "currency": "Cedi",
        "alphabeticcode": "GHC",
        "numericcode": "288",
        "minorunit": null,
        "withdrawaldate": "2008-01"
    },
    {
        "entity": "MOLDOVA, REPUBLIC OF",
        "currency": "Russian Ruble",
        "alphabeticcode": "RUR",
        "numericcode": "810",
        "minorunit": null,
        "withdrawaldate": "1993-12"
    },
    {
        "entity": "NETHERLANDS ANTILLES",
        "currency": "Netherlands Antillean Guilder",
        "alphabeticcode": "ANG",
        "numericcode": "532",
        "minorunit": null,
        "withdrawaldate": "2010-10"
    },
    {
        "entity": "SUDAN",
        "currency": "Sudanese Pound",
        "alphabeticcode": "SDP",
        "numericcode": "736",
        "minorunit": null,
        "withdrawaldate": "1998-06"
    },
    {
        "entity": "UKRAINE",
        "currency": "Karbovanet",
        "alphabeticcode": "UAK",
        "numericcode": "804",
        "minorunit": null,
        "withdrawaldate": "1996-09"
    },
    {
        "entity": "\u00c5LAND ISLANDS",
        "currency": "Euro",
        "alphabeticcode": "EUR",
        "numericcode": "978",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "ANTARCTICA",
        "currency": "No universal currency",
        "alphabeticcode": null,
        "numericcode": null,
        "minorunit": null,
        "withdrawaldate": null
    },
    {
        "entity": "BOLIVIA (PLURINATIONAL STATE OF)",
        "currency": "Boliviano",
        "alphabeticcode": "BOB",
        "numericcode": "068",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "FRENCH SOUTHERN TERRITORIES (THE)",
        "currency": "Euro",
        "alphabeticcode": "EUR",
        "numericcode": "978",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "GUERNSEY",
        "currency": "Pound Sterling",
        "alphabeticcode": "GBP",
        "numericcode": "826",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "LITHUANIA",
        "currency": "Euro",
        "alphabeticcode": "EUR",
        "numericcode": "978",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "MADAGASCAR",
        "currency": "Malagasy Ariary",
        "alphabeticcode": "MGA",
        "numericcode": "969",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "OMAN",
        "currency": "Rial Omani",
        "alphabeticcode": "OMR",
        "numericcode": "512",
        "minorunit": "3",
        "withdrawaldate": null
    },
    {
        "entity": "RWANDA",
        "currency": "Rwanda Franc",
        "alphabeticcode": "RWF",
        "numericcode": "646",
        "minorunit": "0",
        "withdrawaldate": null
    },
    {
        "entity": "SAN MARINO",
        "currency": "Euro",
        "alphabeticcode": "EUR",
        "numericcode": "978",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "SINGAPORE",
        "currency": "Singapore Dollar",
        "alphabeticcode": "SGD",
        "numericcode": "702",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "TIMOR-LESTE",
        "currency": "US Dollar",
        "alphabeticcode": "USD",
        "numericcode": "840",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "TONGA",
        "currency": "Pa\u2019anga",
        "alphabeticcode": "TOP",
        "numericcode": "776",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "WALLIS AND FUTUNA",
        "currency": "CFP Franc",
        "alphabeticcode": "XPF",
        "numericcode": "953",
        "minorunit": "0",
        "withdrawaldate": null
    },
    {
        "entity": "ROMANIA",
        "currency": "New Romanian Leu",
        "alphabeticcode": "RON",
        "numericcode": "946",
        "minorunit": null,
        "withdrawaldate": "2015-06"
    },
    {
        "entity": "SOUTH AFRICA",
        "currency": "Financial Rand",
        "alphabeticcode": "ZAL",
        "numericcode": "991",
        "minorunit": null,
        "withdrawaldate": "1995-03"
    },
    {
        "entity": "SPAIN",
        "currency": "Spanish Peseta",
        "alphabeticcode": "ESP",
        "numericcode": "724",
        "minorunit": null,
        "withdrawaldate": "2002-03"
    },
    {
        "entity": "VENEZUELA",
        "currency": "Bolivar",
        "alphabeticcode": "VEB",
        "numericcode": "862",
        "minorunit": null,
        "withdrawaldate": "2008-01"
    },
    {
        "entity": "VIETNAM",
        "currency": "Old Dong",
        "alphabeticcode": "VNC",
        "numericcode": "704",
        "minorunit": null,
        "withdrawaldate": "1989-1990"
    },
    {
        "entity": "BENIN",
        "currency": "CFA Franc BCEAO",
        "alphabeticcode": "XOF",
        "numericcode": "952",
        "minorunit": "0",
        "withdrawaldate": null
    },
    {
        "entity": "CABO VERDE",
        "currency": "Cabo Verde Escudo",
        "alphabeticcode": "CVE",
        "numericcode": "132",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "CENTRAL AFRICAN REPUBLIC (THE)",
        "currency": "CFA Franc BEAC",
        "alphabeticcode": "XAF",
        "numericcode": "950",
        "minorunit": "0",
        "withdrawaldate": null
    },
    {
        "entity": "CHILE",
        "currency": "Chilean Peso",
        "alphabeticcode": "CLP",
        "numericcode": "152",
        "minorunit": "0",
        "withdrawaldate": null
    },
    {
        "entity": "CONGO (THE DEMOCRATIC REPUBLIC OF THE)",
        "currency": "Congolese Franc",
        "alphabeticcode": "CDF",
        "numericcode": "976",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "CONGO (THE)",
        "currency": "CFA Franc BEAC",
        "alphabeticcode": "XAF",
        "numericcode": "950",
        "minorunit": "0",
        "withdrawaldate": null
    },
    {
        "entity": "SWITZERLAND",
        "currency": "WIR Euro",
        "alphabeticcode": "CHE",
        "numericcode": "947",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "ZZ04_Bond Markets Unit European_EUA-17",
        "currency": "Bond Markets Unit European Unit of Account 17 (E.U.A.-17)",
        "alphabeticcode": "XBD",
        "numericcode": "958",
        "minorunit": "-",
        "withdrawaldate": null
    },
    {
        "entity": "ALBANIA",
        "currency": "Old Lek",
        "alphabeticcode": "ALK",
        "numericcode": "008",
        "minorunit": null,
        "withdrawaldate": "1989-12"
    },
    {
        "entity": "BULGARIA",
        "currency": "Lev A/62",
        "alphabeticcode": "BGK",
        "numericcode": "100",
        "minorunit": null,
        "withdrawaldate": "1989 to 1990"
    },
    {
        "entity": "ESTONIA",
        "currency": "Kroon",
        "alphabeticcode": "EEK",
        "numericcode": "233",
        "minorunit": null,
        "withdrawaldate": "2011-01"
    },
    {
        "entity": "GEORGIA",
        "currency": "Georgian Coupon",
        "alphabeticcode": "GEK",
        "numericcode": "268",
        "minorunit": null,
        "withdrawaldate": "1995-10"
    },
    {
        "entity": "LATVIA",
        "currency": "Latvian Ruble",
        "alphabeticcode": "LVR",
        "numericcode": "428",
        "minorunit": null,
        "withdrawaldate": "1994-12"
    },
    {
        "entity": "TURKEY",
        "currency": "Old Turkish Lira",
        "alphabeticcode": "TRL",
        "numericcode": "792",
        "minorunit": null,
        "withdrawaldate": "2005-12"
    },
    {
        "entity": "BERMUDA",
        "currency": "Bermudian Dollar",
        "alphabeticcode": "BMD",
        "numericcode": "060",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "BOLIVIA (PLURINATIONAL STATE OF)",
        "currency": "Mvdol",
        "alphabeticcode": "BOV",
        "numericcode": "984",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "BOTSWANA",
        "currency": "Pula",
        "alphabeticcode": "BWP",
        "numericcode": "072",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "BURUNDI",
        "currency": "Burundi Franc",
        "alphabeticcode": "BIF",
        "numericcode": "108",
        "minorunit": "0",
        "withdrawaldate": null
    },
    {
        "entity": "DOMINICAN REPUBLIC (THE)",
        "currency": "Dominican Peso",
        "alphabeticcode": "DOP",
        "numericcode": "214",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "FRENCH POLYNESIA",
        "currency": "CFP Franc",
        "alphabeticcode": "XPF",
        "numericcode": "953",
        "minorunit": "0",
        "withdrawaldate": null
    },
    {
        "entity": "GUINEA-BISSAU",
        "currency": "CFA Franc BCEAO",
        "alphabeticcode": "XOF",
        "numericcode": "952",
        "minorunit": "0",
        "withdrawaldate": null
    },
    {
        "entity": "KAZAKHSTAN",
        "currency": "Tenge",
        "alphabeticcode": "KZT",
        "numericcode": "398",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "SERBIA",
        "currency": "Serbian Dinar",
        "alphabeticcode": "RSD",
        "numericcode": "941",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "ZAMBIA",
        "currency": "Zambian Kwacha",
        "alphabeticcode": "ZMW",
        "numericcode": "967",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "BELGIUM",
        "currency": "Belgian Franc",
        "alphabeticcode": "BEF",
        "numericcode": "056",
        "minorunit": null,
        "withdrawaldate": "2002-03"
    },
    {
        "entity": "BRAZIL",
        "currency": "Cruzado",
        "alphabeticcode": "BRC",
        "numericcode": "076",
        "minorunit": null,
        "withdrawaldate": "1989-02"
    },
    {
        "entity": "CZECHOSLOVAKIA",
        "currency": "Koruna",
        "alphabeticcode": "CSK",
        "numericcode": "200",
        "minorunit": null,
        "withdrawaldate": "1993-03"
    },
    {
        "entity": "EQUATORIAL GUINEA",
        "currency": "Ekwele",
        "alphabeticcode": "GQE",
        "numericcode": "226",
        "minorunit": null,
        "withdrawaldate": "1986-06"
    },
    {
        "entity": "GHANA",
        "currency": "Ghana Cedi",
        "alphabeticcode": "GHP",
        "numericcode": "939",
        "minorunit": null,
        "withdrawaldate": "2007-06"
    },
    {
        "entity": "ICELAND",
        "currency": "Old Krona",
        "alphabeticcode": "ISJ",
        "numericcode": "352",
        "minorunit": null,
        "withdrawaldate": "1989 to 1990"
    },
    {
        "entity": "LATVIA",
        "currency": "Latvian Lats",
        "alphabeticcode": "LVL",
        "numericcode": "428",
        "minorunit": null,
        "withdrawaldate": "2014-01"
    },
    {
        "entity": "RUSSIAN FEDERATION",
        "currency": "Russian Ruble",
        "alphabeticcode": "RUR",
        "numericcode": "810",
        "minorunit": null,
        "withdrawaldate": "2004-01"
    },
    {
        "entity": "SERBIA AND MONTENEGRO",
        "currency": "Euro",
        "alphabeticcode": "EUR",
        "numericcode": "978",
        "minorunit": null,
        "withdrawaldate": "2006-10"
    },
    {
        "entity": "TIMOR-LESTE",
        "currency": "Timor Escudo",
        "alphabeticcode": "TPE",
        "numericcode": "626",
        "minorunit": null,
        "withdrawaldate": "2002-11"
    },
    {
        "entity": "UGANDA",
        "currency": "Uganda Shilling",
        "alphabeticcode": "UGS",
        "numericcode": "800",
        "minorunit": null,
        "withdrawaldate": "1987-05"
    },
    {
        "entity": "ZZ01_Gold-Franc",
        "currency": "Gold-Franc",
        "alphabeticcode": "XFO",
        "numericcode": null,
        "minorunit": null,
        "withdrawaldate": "2006-10"
    },
    {
        "entity": "AFGHANISTAN",
        "currency": "Afghani",
        "alphabeticcode": "AFN",
        "numericcode": "971",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "COMOROS (THE)",
        "currency": "Comorian Franc",
        "alphabeticcode": "KMF",
        "numericcode": "174",
        "minorunit": "0",
        "withdrawaldate": null
    },
    {
        "entity": "GAMBIA (THE)",
        "currency": "Dalasi",
        "alphabeticcode": "GMD",
        "numericcode": "270",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "IRAN (ISLAMIC REPUBLIC OF)",
        "currency": "Iranian Rial",
        "alphabeticcode": "IRR",
        "numericcode": "364",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "ISRAEL",
        "currency": "New Israeli Sheqel",
        "alphabeticcode": "ILS",
        "numericcode": "376",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "KYRGYZSTAN",
        "currency": "Som",
        "alphabeticcode": "KGS",
        "numericcode": "417",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "NEPAL",
        "currency": "Nepalese Rupee",
        "alphabeticcode": "NPR",
        "numericcode": "524",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "SAUDI ARABIA",
        "currency": "Saudi Riyal",
        "alphabeticcode": "SAR",
        "numericcode": "682",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "SIERRA LEONE",
        "currency": "Leone",
        "alphabeticcode": "SLE",
        "numericcode": "925",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "VENEZUELA (BOLIVARIAN REPUBLIC OF)",
        "currency": "Bol\u00edvar Soberano",
        "alphabeticcode": "VES",
        "numericcode": "928",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "ZZ07_No_Currency",
        "currency": "The codes assigned for transactions where no currency is involved",
        "alphabeticcode": "XXX",
        "numericcode": "999",
        "minorunit": "-",
        "withdrawaldate": null
    },
    {
        "entity": "ANDORRA",
        "currency": "French Franc",
        "alphabeticcode": "FRF",
        "numericcode": "250",
        "minorunit": null,
        "withdrawaldate": "2002-03"
    },
    {
        "entity": "CROATIA",
        "currency": "Croatian Dinar",
        "alphabeticcode": "HRD",
        "numericcode": "191",
        "minorunit": null,
        "withdrawaldate": "1995-01"
    },
    {
        "entity": "MAYOTTE",
        "currency": "French Franc",
        "alphabeticcode": "FRF",
        "numericcode": "250",
        "minorunit": null,
        "withdrawaldate": "2002-03"
    },
    {
        "entity": "ROMANIA",
        "currency": "Leu A/52",
        "alphabeticcode": "ROK",
        "numericcode": "642",
        "minorunit": null,
        "withdrawaldate": "1989 to 1990"
    },
    {
        "entity": "AUSTRALIA",
        "currency": "Australian Dollar",
        "alphabeticcode": "AUD",
        "numericcode": "036",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "EGYPT",
        "currency": "Egyptian Pound",
        "alphabeticcode": "EGP",
        "numericcode": "818",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "ESWATINI",
        "currency": "Lilangeni",
        "alphabeticcode": "SZL",
        "numericcode": "748",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "ISLE OF MAN",
        "currency": "Pound Sterling",
        "alphabeticcode": "GBP",
        "numericcode": "826",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "JAPAN",
        "currency": "Yen",
        "alphabeticcode": "JPY",
        "numericcode": "392",
        "minorunit": "0",
        "withdrawaldate": null
    },
    {
        "entity": "JERSEY",
        "currency": "Pound Sterling",
        "alphabeticcode": "GBP",
        "numericcode": "826",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "MACAO",
        "currency": "Pataca",
        "alphabeticcode": "MOP",
        "numericcode": "446",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "MYANMAR",
        "currency": "Kyat",
        "alphabeticcode": "MMK",
        "numericcode": "104",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "ROMANIA",
        "currency": "Romanian Leu",
        "alphabeticcode": "RON",
        "numericcode": "946",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "SAINT MARTIN (FRENCH PART)",
        "currency": "Euro",
        "alphabeticcode": "EUR",
        "numericcode": "978",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "SLOVENIA",
        "currency": "Euro",
        "alphabeticcode": "EUR",
        "numericcode": "978",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "VENEZUELA (BOLIVARIAN REPUBLIC OF)",
        "currency": "Bol\u00edvar Soberano",
        "alphabeticcode": "VED",
        "numericcode": "926",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "VIET NAM",
        "currency": "Dong",
        "alphabeticcode": "VND",
        "numericcode": "704",
        "minorunit": "0",
        "withdrawaldate": null
    },
    {
        "entity": "ANGOLA",
        "currency": "New Kwanza",
        "alphabeticcode": "AON",
        "numericcode": "024",
        "minorunit": null,
        "withdrawaldate": "2000-02"
    },
    {
        "entity": "FRENCH  GUIANA",
        "currency": "French Franc",
        "alphabeticcode": "FRF",
        "numericcode": "250",
        "minorunit": null,
        "withdrawaldate": "2002-03"
    },
    {
        "entity": "MALTA",
        "currency": "Maltese Lira",
        "alphabeticcode": "MTL",
        "numericcode": "470",
        "minorunit": null,
        "withdrawaldate": "2008-01"
    },
    {
        "entity": "MAURITANIA",
        "currency": "Ouguiya",
        "alphabeticcode": "MRO",
        "numericcode": "478",
        "minorunit": null,
        "withdrawaldate": "2017-12"
    },
    {
        "entity": "MONACO",
        "currency": "French Franc",
        "alphabeticcode": "FRF",
        "numericcode": "250",
        "minorunit": null,
        "withdrawaldate": "2002-03"
    },
    {
        "entity": "MOZAMBIQUE",
        "currency": "Mozambique Escudo",
        "alphabeticcode": "MZE",
        "numericcode": "508",
        "minorunit": null,
        "withdrawaldate": "1978 to 1981"
    },
    {
        "entity": "SURINAME",
        "currency": "Surinam Guilder",
        "alphabeticcode": "SRG",
        "numericcode": "740",
        "minorunit": null,
        "withdrawaldate": "2003-12"
    },
    {
        "entity": "TURKEY",
        "currency": "New Turkish Lira",
        "alphabeticcode": "TRY",
        "numericcode": "949",
        "minorunit": null,
        "withdrawaldate": "2009-01"
    },
    {
        "entity": "YEMEN, DEMOCRATIC",
        "currency": "Yemeni Dinar",
        "alphabeticcode": "YDD",
        "numericcode": "720",
        "minorunit": null,
        "withdrawaldate": "1991-09"
    },
    {
        "entity": "ZIMBABWE",
        "currency": "Zimbabwe\u00a0Dollar",
        "alphabeticcode": "ZWL",
        "numericcode": "932",
        "minorunit": null,
        "withdrawaldate": "2024-09"
    },
    {
        "entity": "AZERBAIJAN",
        "currency": "Azerbaijan Manat",
        "alphabeticcode": "AZN",
        "numericcode": "944",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "CZECHIA",
        "currency": "Czech Koruna",
        "alphabeticcode": "CZK",
        "numericcode": "203",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "LAO PEOPLE\u2019S DEMOCRATIC REPUBLIC (THE)",
        "currency": "Lao Kip",
        "alphabeticcode": "LAK",
        "numericcode": "418",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "UGANDA",
        "currency": "Uganda Shilling",
        "alphabeticcode": "UGX",
        "numericcode": "800",
        "minorunit": "0",
        "withdrawaldate": null
    },
    {
        "entity": "UNITED STATES OF AMERICA (THE)",
        "currency": "US Dollar (Next day)",
        "alphabeticcode": "USN",
        "numericcode": "997",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "ZZ10_Platinum",
        "currency": "Platinum",
        "alphabeticcode": "XPT",
        "numericcode": "962",
        "minorunit": "-",
        "withdrawaldate": null
    },
    {
        "entity": "BRAZIL",
        "currency": "Cruzeiro",
        "alphabeticcode": "BRB",
        "numericcode": "076",
        "minorunit": null,
        "withdrawaldate": "1986-03"
    },
    {
        "entity": "BULGARIA",
        "currency": "Lev",
        "alphabeticcode": "BGL",
        "numericcode": "100",
        "minorunit": null,
        "withdrawaldate": "2003-11"
    },
    {
        "entity": "LESOTHO",
        "currency": "Loti",
        "alphabeticcode": "LSM",
        "numericcode": "426",
        "minorunit": null,
        "withdrawaldate": "1985-05"
    },
    {
        "entity": "PERU",
        "currency": "Sol",
        "alphabeticcode": "PEH",
        "numericcode": "604",
        "minorunit": null,
        "withdrawaldate": "1989 to 1990"
    },
    {
        "entity": "URUGUAY",
        "currency": "Uruguayan Peso",
        "alphabeticcode": "UYP",
        "numericcode": "858",
        "minorunit": null,
        "withdrawaldate": "1993-03"
    },
    {
        "entity": "ZZ02_RINET Funds Code",
        "currency": "RINET Funds Code",
        "alphabeticcode": "XRE",
        "numericcode": null,
        "minorunit": null,
        "withdrawaldate": "1999-11"
    },
    {
        "entity": "BULGARIA",
        "currency": "Bulgarian Lev",
        "alphabeticcode": "BGN",
        "numericcode": "975",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "CURA\u00c7AO",
        "currency": "Caribbean Guilder",
        "alphabeticcode": "XCG",
        "numericcode": "532",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "EUROPEAN UNION",
        "currency": "Euro",
        "alphabeticcode": "EUR",
        "numericcode": "978",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "FAROE ISLANDS (THE)",
        "currency": "Danish Krone",
        "alphabeticcode": "DKK",
        "numericcode": "208",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "GEORGIA",
        "currency": "Lari",
        "alphabeticcode": "GEL",
        "numericcode": "981",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "GUAM",
        "currency": "US Dollar",
        "alphabeticcode": "USD",
        "numericcode": "840",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "KENYA",
        "currency": "Kenyan Shilling",
        "alphabeticcode": "KES",
        "numericcode": "404",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "MONGOLIA",
        "currency": "Tugrik",
        "alphabeticcode": "MNT",
        "numericcode": "496",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "SAINT HELENA, ASCENSION AND TRISTAN DA CUNHA",
        "currency": "Saint Helena Pound",
        "alphabeticcode": "SHP",
        "numericcode": "654",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "SINT MAARTEN (DUTCH PART)",
        "currency": "Caribbean Guilder",
        "alphabeticcode": "XCG",
        "numericcode": "532",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "UKRAINE",
        "currency": "Hryvnia",
        "alphabeticcode": "UAH",
        "numericcode": "980",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "VENEZUELA (BOLIVARIAN REPUBLIC OF)",
        "currency": "Bol\u00edvar",
        "alphabeticcode": "VEF",
        "numericcode": "937",
        "minorunit": null,
        "withdrawaldate": "2018-08"
    },
    {
        "entity": "YUGOSLAVIA",
        "currency": "New Yugoslavian Dinar",
        "alphabeticcode": "YUD",
        "numericcode": "890",
        "minorunit": null,
        "withdrawaldate": "1990-01"
    },
    {
        "entity": "BHUTAN",
        "currency": "Indian Rupee",
        "alphabeticcode": "INR",
        "numericcode": "356",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "BOSNIA AND HERZEGOVINA",
        "currency": "Convertible Mark",
        "alphabeticcode": "BAM",
        "numericcode": "977",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "COLOMBIA",
        "currency": "Colombian Peso",
        "alphabeticcode": "COP",
        "numericcode": "170",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "ESTONIA",
        "currency": "Euro",
        "alphabeticcode": "EUR",
        "numericcode": "978",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "HUNGARY",
        "currency": "Forint",
        "alphabeticcode": "HUF",
        "numericcode": "348",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "JAMAICA",
        "currency": "Jamaican Dollar",
        "alphabeticcode": "JMD",
        "numericcode": "388",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "LIBYA",
        "currency": "Libyan Dinar",
        "alphabeticcode": "LYD",
        "numericcode": "434",
        "minorunit": "3",
        "withdrawaldate": null
    },
    {
        "entity": "NAURU",
        "currency": "Australian Dollar",
        "alphabeticcode": "AUD",
        "numericcode": "036",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "UNITED STATES OF AMERICA (THE)",
        "currency": "US Dollar",
        "alphabeticcode": "USD",
        "numericcode": "840",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "ZZ03_Bond Markets Unit European_EUA-9",
        "currency": "Bond Markets Unit European Unit of Account 9 (E.U.A.-9)",
        "alphabeticcode": "XBC",
        "numericcode": "957",
        "minorunit": "-",
        "withdrawaldate": null
    },
    {
        "entity": "MALI",
        "currency": "Mali Franc",
        "alphabeticcode": "MLF",
        "numericcode": "466",
        "minorunit": null,
        "withdrawaldate": "1984-11"
    },
    {
        "entity": "SUDAN",
        "currency": "Sudanese Dinar",
        "alphabeticcode": "SDD",
        "numericcode": "736",
        "minorunit": null,
        "withdrawaldate": "2007-07"
    },
    {
        "entity": "ZIMBABWE",
        "currency": "Rhodesian Dollar",
        "alphabeticcode": "ZWC",
        "numericcode": "716",
        "minorunit": null,
        "withdrawaldate": "1989-12"
    },
    {
        "entity": "BRUNEI DARUSSALAM",
        "currency": "Brunei Dollar",
        "alphabeticcode": "BND",
        "numericcode": "096",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "FRANCE",
        "currency": "Euro",
        "alphabeticcode": "EUR",
        "numericcode": "978",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "GHANA",
        "currency": "Ghana Cedi",
        "alphabeticcode": "GHS",
        "numericcode": "936",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "LATVIA",
        "currency": "Euro",
        "alphabeticcode": "EUR",
        "numericcode": "978",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "LIECHTENSTEIN",
        "currency": "Swiss Franc",
        "alphabeticcode": "CHF",
        "numericcode": "756",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "SOMALIA",
        "currency": "Somali Shilling",
        "alphabeticcode": "SOS",
        "numericcode": "706",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "SUDAN (THE)",
        "currency": "Sudanese Pound",
        "alphabeticcode": "SDG",
        "numericcode": "938",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "WESTERN SAHARA",
        "currency": "Moroccan Dirham",
        "alphabeticcode": "MAD",
        "numericcode": "504",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "CURA\u00c7AO",
        "currency": "Netherlands Antillean Guilder",
        "alphabeticcode": "ANG",
        "numericcode": "532",
        "minorunit": null,
        "withdrawaldate": "2025-03"
    },
    {
        "entity": "MARTINIQUE",
        "currency": "French Franc",
        "alphabeticcode": "FRF",
        "numericcode": "250",
        "minorunit": null,
        "withdrawaldate": "2002-03"
    },
    {
        "entity": "SAINT MARTIN",
        "currency": "French Franc",
        "alphabeticcode": "FRF",
        "numericcode": "250",
        "minorunit": null,
        "withdrawaldate": "1999-01"
    },
    {
        "entity": "SAN MARINO",
        "currency": "Italian Lira",
        "alphabeticcode": "ITL",
        "numericcode": "380",
        "minorunit": null,
        "withdrawaldate": "2002-03"
    },
    {
        "entity": "ZIMBABWE",
        "currency": "Zimbabwe Dollar (old)",
        "alphabeticcode": "ZWD",
        "numericcode": "716",
        "minorunit": null,
        "withdrawaldate": "2006-08"
    },
    {
        "entity": "ZIMBABWE",
        "currency": "Zimbabwe Dollar",
        "alphabeticcode": "ZWR",
        "numericcode": "935",
        "minorunit": null,
        "withdrawaldate": "2009-06"
    },
    {
        "entity": "BAHAMAS (THE)",
        "currency": "Bahamian Dollar",
        "alphabeticcode": "BSD",
        "numericcode": "044",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "BURKINA FASO",
        "currency": "CFA Franc BCEAO",
        "alphabeticcode": "XOF",
        "numericcode": "952",
        "minorunit": "0",
        "withdrawaldate": null
    },
    {
        "entity": "GABON",
        "currency": "CFA Franc BEAC",
        "alphabeticcode": "XAF",
        "numericcode": "950",
        "minorunit": "0",
        "withdrawaldate": null
    },
    {
        "entity": "HEARD ISLAND AND McDONALD ISLANDS",
        "currency": "Australian Dollar",
        "alphabeticcode": "AUD",
        "numericcode": "036",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "INDONESIA",
        "currency": "Rupiah",
        "alphabeticcode": "IDR",
        "numericcode": "360",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "MARSHALL ISLANDS (THE)",
        "currency": "US Dollar",
        "alphabeticcode": "USD",
        "numericcode": "840",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "MOLDOVA (THE REPUBLIC OF)",
        "currency": "Moldovan Leu",
        "alphabeticcode": "MDL",
        "numericcode": "498",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "MOZAMBIQUE",
        "currency": "Mozambique Metical",
        "alphabeticcode": "MZN",
        "numericcode": "943",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "SOUTH SUDAN",
        "currency": "South Sudanese Pound",
        "alphabeticcode": "SSP",
        "numericcode": "728",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "URUGUAY",
        "currency": "Uruguay Peso en Unidades Indexadas (UI)",
        "alphabeticcode": "UYI",
        "numericcode": "940",
        "minorunit": "0",
        "withdrawaldate": null
    },
    {
        "entity": "ARMENIA",
        "currency": "Russian Ruble",
        "alphabeticcode": "RUR",
        "numericcode": "810",
        "minorunit": null,
        "withdrawaldate": "1994-08"
    },
    {
        "entity": "BELARUS",
        "currency": "Belarusian Ruble",
        "alphabeticcode": "BYR",
        "numericcode": "974",
        "minorunit": null,
        "withdrawaldate": "2017-01"
    },
    {
        "entity": "CZECHOSLOVAKIA",
        "currency": "Krona A/53",
        "alphabeticcode": "CSJ",
        "numericcode": "203",
        "minorunit": null,
        "withdrawaldate": "1989 to 1990"
    },
    {
        "entity": "GUINEA-BISSAU",
        "currency": "Guinea Escudo",
        "alphabeticcode": "GWE",
        "numericcode": "624",
        "minorunit": null,
        "withdrawaldate": "1978 to 1981"
    },
    {
        "entity": "LUXEMBOURG",
        "currency": "Luxembourg Franc",
        "alphabeticcode": "LUF",
        "numericcode": "442",
        "minorunit": null,
        "withdrawaldate": "2002-03"
    },
    {
        "entity": "ARGENTINA",
        "currency": "Argentine Peso",
        "alphabeticcode": "ARS",
        "numericcode": "032",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "ETHIOPIA",
        "currency": "Ethiopian Birr",
        "alphabeticcode": "ETB",
        "numericcode": "230",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "FRENCH GUIANA",
        "currency": "Euro",
        "alphabeticcode": "EUR",
        "numericcode": "978",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "INTERNATIONAL MONETARY FUND (IMF)",
        "currency": "SDR (Special Drawing Right)",
        "alphabeticcode": "XDR",
        "numericcode": "960",
        "minorunit": "-",
        "withdrawaldate": null
    },
    {
        "entity": "IRAQ",
        "currency": "Iraqi Dinar",
        "alphabeticcode": "IQD",
        "numericcode": "368",
        "minorunit": "3",
        "withdrawaldate": null
    },
    {
        "entity": "KIRIBATI",
        "currency": "Australian Dollar",
        "alphabeticcode": "AUD",
        "numericcode": "036",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "MALI",
        "currency": "CFA Franc BCEAO",
        "alphabeticcode": "XOF",
        "numericcode": "952",
        "minorunit": "0",
        "withdrawaldate": null
    },
    {
        "entity": "MEMBER COUNTRIES OF THE AFRICAN DEVELOPMENT BANK GROUP",
        "currency": "ADB Unit of Account",
        "alphabeticcode": "XUA",
        "numericcode": "965",
        "minorunit": "-",
        "withdrawaldate": null
    },
    {
        "entity": "MONTSERRAT",
        "currency": "East Caribbean Dollar",
        "alphabeticcode": "XCD",
        "numericcode": "951",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "NAMIBIA",
        "currency": "Namibia Dollar",
        "alphabeticcode": "NAD",
        "numericcode": "516",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "PAKISTAN",
        "currency": "Pakistan Rupee",
        "alphabeticcode": "PKR",
        "numericcode": "586",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "SAINT VINCENT AND THE GRENADINES",
        "currency": "East Caribbean Dollar",
        "alphabeticcode": "XCD",
        "numericcode": "951",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "TAJIKISTAN",
        "currency": "Somoni",
        "alphabeticcode": "TJS",
        "numericcode": "972",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "YEMEN",
        "currency": "Yemeni Rial",
        "alphabeticcode": "YER",
        "numericcode": "886",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "ANGOLA",
        "currency": "Kwanza",
        "alphabeticcode": "AOK",
        "numericcode": "024",
        "minorunit": null,
        "withdrawaldate": "1991-03"
    },
    {
        "entity": "SOUTHERN RHODESIA",
        "currency": "Rhodesian Dollar",
        "alphabeticcode": "RHD",
        "numericcode": "716",
        "minorunit": null,
        "withdrawaldate": "1978 to 1981"
    },
    {
        "entity": "VENEZUELA (BOLIVARIAN REPUBLIC OF)",
        "currency": "Bolivar",
        "alphabeticcode": "VEF",
        "numericcode": "937",
        "minorunit": null,
        "withdrawaldate": "2016-02"
    },
    {
        "entity": "YUGOSLAVIA",
        "currency": "New Dinar",
        "alphabeticcode": "YUM",
        "numericcode": "891",
        "minorunit": null,
        "withdrawaldate": "2003-07"
    },
    {
        "entity": "BELIZE",
        "currency": "Belize Dollar",
        "alphabeticcode": "BZD",
        "numericcode": "084",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "CYPRUS",
        "currency": "Euro",
        "alphabeticcode": "EUR",
        "numericcode": "978",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "FALKLAND ISLANDS (THE) [MALVINAS]",
        "currency": "Falkland Islands Pound",
        "alphabeticcode": "FKP",
        "numericcode": "238",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "MEXICO",
        "currency": "Mexican Unidad de Inversion (UDI)",
        "alphabeticcode": "MXV",
        "numericcode": "979",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "PALESTINE, STATE OF",
        "currency": "No universal currency",
        "alphabeticcode": null,
        "numericcode": null,
        "minorunit": null,
        "withdrawaldate": null
    },
    {
        "entity": "SOLOMON ISLANDS",
        "currency": "Solomon Islands Dollar",
        "alphabeticcode": "SBD",
        "numericcode": "090",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "SWITZERLAND",
        "currency": "WIR Franc",
        "alphabeticcode": "CHW",
        "numericcode": "948",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "TAIWAN (PROVINCE OF CHINA)",
        "currency": "New Taiwan Dollar",
        "alphabeticcode": "TWD",
        "numericcode": "901",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "TRINIDAD AND TOBAGO",
        "currency": "Trinidad and Tobago Dollar",
        "alphabeticcode": "TTD",
        "numericcode": "780",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "URUGUAY",
        "currency": "Peso Uruguayo",
        "alphabeticcode": "UYU",
        "numericcode": "858",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "ANDORRA",
        "currency": "Spanish Peseta",
        "alphabeticcode": "ESP",
        "numericcode": "724",
        "minorunit": null,
        "withdrawaldate": "2002-03"
    },
    {
        "entity": "BELGIUM",
        "currency": "Convertible Franc",
        "alphabeticcode": "BEC",
        "numericcode": "993",
        "minorunit": null,
        "withdrawaldate": "1990-03"
    },
    {
        "entity": "BELGIUM",
        "currency": "Financial Franc",
        "alphabeticcode": "BEL",
        "numericcode": "992",
        "minorunit": null,
        "withdrawaldate": "1990-03"
    },
    {
        "entity": "GEORGIA",
        "currency": "Russian Ruble",
        "alphabeticcode": "RUR",
        "numericcode": "810",
        "minorunit": null,
        "withdrawaldate": "1994-04"
    },
    {
        "entity": "GREECE",
        "currency": "Drachma",
        "alphabeticcode": "GRD",
        "numericcode": "300",
        "minorunit": null,
        "withdrawaldate": "2002-03"
    },
    {
        "entity": "PERU",
        "currency": "Nuevo Sol",
        "alphabeticcode": "PEN",
        "numericcode": "604",
        "minorunit": null,
        "withdrawaldate": "2015-12"
    },
    {
        "entity": "TIMOR-LESTE",
        "currency": "Rupiah",
        "alphabeticcode": "IDR",
        "numericcode": "360",
        "minorunit": null,
        "withdrawaldate": "2002-07"
    },
    {
        "entity": "ZAIRE",
        "currency": "Zaire",
        "alphabeticcode": "ZRZ",
        "numericcode": "180",
        "minorunit": null,
        "withdrawaldate": "1994-02"
    },
    {
        "entity": "BHUTAN",
        "currency": "Ngultrum",
        "alphabeticcode": "BTN",
        "numericcode": "064",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "CHINA",
        "currency": "Yuan Renminbi",
        "alphabeticcode": "CNY",
        "numericcode": "156",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "EL SALVADOR",
        "currency": "El Salvador Colon",
        "alphabeticcode": "SVC",
        "numericcode": "222",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "GREECE",
        "currency": "Euro",
        "alphabeticcode": "EUR",
        "numericcode": "978",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "HONG KONG",
        "currency": "Hong Kong Dollar",
        "alphabeticcode": "HKD",
        "numericcode": "344",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "LESOTHO",
        "currency": "Loti",
        "alphabeticcode": "LSL",
        "numericcode": "426",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "MAURITIUS",
        "currency": "Mauritius Rupee",
        "alphabeticcode": "MUR",
        "numericcode": "480",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "NAMIBIA",
        "currency": "Rand",
        "alphabeticcode": "ZAR",
        "numericcode": "710",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "NIUE",
        "currency": "New Zealand Dollar",
        "alphabeticcode": "NZD",
        "numericcode": "554",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "ZZ08_Gold",
        "currency": "Gold",
        "alphabeticcode": "XAU",
        "numericcode": "959",
        "minorunit": "-",
        "withdrawaldate": null
    },
    {
        "entity": "CROATIA",
        "currency": "Croatian Kuna",
        "alphabeticcode": "HRK",
        "numericcode": "191",
        "minorunit": null,
        "withdrawaldate": "2015-06"
    },
    {
        "entity": "CUBA",
        "currency": "Peso Convertible",
        "alphabeticcode": "CUC",
        "numericcode": "931",
        "minorunit": null,
        "withdrawaldate": "2021-06"
    },
    {
        "entity": "LAO",
        "currency": "Pathet Lao Kip",
        "alphabeticcode": "LAJ",
        "numericcode": "418",
        "minorunit": null,
        "withdrawaldate": "1979-12"
    },
    {
        "entity": "UZBEKISTAN",
        "currency": "Russian Ruble",
        "alphabeticcode": "RUR",
        "numericcode": "810",
        "minorunit": null,
        "withdrawaldate": "1994-07"
    },
    {
        "entity": "ARMENIA",
        "currency": "Armenian Dram",
        "alphabeticcode": "AMD",
        "numericcode": "051",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "BELGIUM",
        "currency": "Euro",
        "alphabeticcode": "EUR",
        "numericcode": "978",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "GIBRALTAR",
        "currency": "Gibraltar Pound",
        "alphabeticcode": "GIP",
        "numericcode": "292",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "NORTH MACEDONIA",
        "currency": "Denar",
        "alphabeticcode": "MKD",
        "numericcode": "807",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "MALAYSIA",
        "currency": "Malaysian Ringgit",
        "alphabeticcode": "MYR",
        "numericcode": "458",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "MEXICO",
        "currency": "Mexican Peso",
        "alphabeticcode": "MXN",
        "numericcode": "484",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "MICRONESIA (FEDERATED STATES OF)",
        "currency": "US Dollar",
        "alphabeticcode": "USD",
        "numericcode": "840",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "NIGER (THE)",
        "currency": "CFA Franc BCEAO",
        "alphabeticcode": "XOF",
        "numericcode": "952",
        "minorunit": "0",
        "withdrawaldate": null
    },
    {
        "entity": "RUSSIAN FEDERATION (THE)",
        "currency": "Russian Ruble",
        "alphabeticcode": "RUB",
        "numericcode": "643",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "SOUTH AFRICA",
        "currency": "Rand",
        "alphabeticcode": "ZAR",
        "numericcode": "710",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "SWITZERLAND",
        "currency": "Swiss Franc",
        "alphabeticcode": "CHF",
        "numericcode": "756",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "THAILAND",
        "currency": "Baht",
        "alphabeticcode": "THB",
        "numericcode": "764",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "TOGO",
        "currency": "CFA Franc BCEAO",
        "alphabeticcode": "XOF",
        "numericcode": "952",
        "minorunit": "0",
        "withdrawaldate": null
    },
    {
        "entity": "TURKMENISTAN",
        "currency": "Turkmenistan New Manat",
        "alphabeticcode": "TMT",
        "numericcode": "934",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "ARGENTINA",
        "currency": "Peso",
        "alphabeticcode": "ARY",
        "numericcode": "032",
        "minorunit": null,
        "withdrawaldate": "1989 to 1990"
    },
    {
        "entity": "EUROPEAN MONETARY CO-OPERATION FUND (EMCF)",
        "currency": "European Currency Unit (E.C.U)",
        "alphabeticcode": "XEU",
        "numericcode": "954",
        "minorunit": null,
        "withdrawaldate": "1999-01"
    },
    {
        "entity": "ITALY",
        "currency": "Italian Lira",
        "alphabeticcode": "ITL",
        "numericcode": "380",
        "minorunit": null,
        "withdrawaldate": "2002-03"
    },
    {
        "entity": "ALBANIA",
        "currency": "Lek",
        "alphabeticcode": "ALL",
        "numericcode": "008",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "AMERICAN SAMOA",
        "currency": "US Dollar",
        "alphabeticcode": "USD",
        "numericcode": "840",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "ANTIGUA AND BARBUDA",
        "currency": "East Caribbean Dollar",
        "alphabeticcode": "XCD",
        "numericcode": "951",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "CHRISTMAS ISLAND",
        "currency": "Australian Dollar",
        "alphabeticcode": "AUD",
        "numericcode": "036",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "GRENADA",
        "currency": "East Caribbean Dollar",
        "alphabeticcode": "XCD",
        "numericcode": "951",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "GUINEA",
        "currency": "Guinean Franc",
        "alphabeticcode": "GNF",
        "numericcode": "324",
        "minorunit": "0",
        "withdrawaldate": null
    },
    {
        "entity": "JORDAN",
        "currency": "Jordanian Dinar",
        "alphabeticcode": "JOD",
        "numericcode": "400",
        "minorunit": "3",
        "withdrawaldate": null
    },
    {
        "entity": "LESOTHO",
        "currency": "Rand",
        "alphabeticcode": "ZAR",
        "numericcode": "710",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "MALAWI",
        "currency": "Malawi Kwacha",
        "alphabeticcode": "MWK",
        "numericcode": "454",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "SLOVAKIA",
        "currency": "Euro",
        "alphabeticcode": "EUR",
        "numericcode": "978",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "SWEDEN",
        "currency": "Swedish Krona",
        "alphabeticcode": "SEK",
        "numericcode": "752",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "TUVALU",
        "currency": "Australian Dollar",
        "alphabeticcode": "AUD",
        "numericcode": "036",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "ZZ09_Palladium",
        "currency": "Palladium",
        "alphabeticcode": "XPD",
        "numericcode": "964",
        "minorunit": "-",
        "withdrawaldate": null
    },
    {
        "entity": "BRAZIL",
        "currency": "Cruzeiro",
        "alphabeticcode": "BRE",
        "numericcode": "076",
        "minorunit": null,
        "withdrawaldate": "1993-03"
    },
    {
        "entity": "ECUADOR",
        "currency": "Unidad de Valor Constante (UVC)",
        "alphabeticcode": "ECV",
        "numericcode": "983",
        "minorunit": null,
        "withdrawaldate": "2000-09"
    },
    {
        "entity": "R\u00c9UNION",
        "currency": "French Franc",
        "alphabeticcode": "FRF",
        "numericcode": "250",
        "minorunit": null,
        "withdrawaldate": "2002-03"
    },
    {
        "entity": "SAO TOME AND PRINCIPE",
        "currency": "Dobra",
        "alphabeticcode": "STD",
        "numericcode": "678",
        "minorunit": null,
        "withdrawaldate": "2017-12"
    },
    {
        "entity": "TURKMENISTAN",
        "currency": "Turkmenistan Manat",
        "alphabeticcode": "TMM",
        "numericcode": "795",
        "minorunit": null,
        "withdrawaldate": "2009-01"
    },
    {
        "entity": "BANGLADESH",
        "currency": "Taka",
        "alphabeticcode": "BDT",
        "numericcode": "050",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "BRAZIL",
        "currency": "Brazilian Real",
        "alphabeticcode": "BRL",
        "numericcode": "986",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "FIJI",
        "currency": "Fiji Dollar",
        "alphabeticcode": "FJD",
        "numericcode": "242",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "HONDURAS",
        "currency": "Lempira",
        "alphabeticcode": "HNL",
        "numericcode": "340",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "MAURITANIA",
        "currency": "Ouguiya",
        "alphabeticcode": "MRU",
        "numericcode": "929",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "MONACO",
        "currency": "Euro",
        "alphabeticcode": "EUR",
        "numericcode": "978",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "PALAU",
        "currency": "US Dollar",
        "alphabeticcode": "USD",
        "numericcode": "840",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "PARAGUAY",
        "currency": "Guarani",
        "alphabeticcode": "PYG",
        "numericcode": "600",
        "minorunit": "0",
        "withdrawaldate": null
    },
    {
        "entity": "SEYCHELLES",
        "currency": "Seychelles Rupee",
        "alphabeticcode": "SCR",
        "numericcode": "690",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "ANGOLA",
        "currency": "Kwanza Reajustado",
        "alphabeticcode": "AOR",
        "numericcode": "982",
        "minorunit": null,
        "withdrawaldate": "2000-02"
    },
    {
        "entity": "AZERBAIJAN",
        "currency": "Azerbaijanian Manat",
        "alphabeticcode": "AZM",
        "numericcode": "031",
        "minorunit": null,
        "withdrawaldate": "2005-12"
    },
    {
        "entity": "CROATIA",
        "currency": "Kuna",
        "alphabeticcode": "HRK",
        "numericcode": "191",
        "minorunit": null,
        "withdrawaldate": "2023-01"
    },
    {
        "entity": "GUINEA",
        "currency": "Syli",
        "alphabeticcode": "GNE",
        "numericcode": "324",
        "minorunit": null,
        "withdrawaldate": "1989-12"
    },
    {
        "entity": "ISRAEL",
        "currency": "Pound",
        "alphabeticcode": "ILP",
        "numericcode": "376",
        "minorunit": null,
        "withdrawaldate": "1978 to 1981"
    },
    {
        "entity": "MADAGASCAR",
        "currency": "Malagasy Franc",
        "alphabeticcode": "MGF",
        "numericcode": "450",
        "minorunit": null,
        "withdrawaldate": "2004-12"
    },
    {
        "entity": "SAINT PIERRE AND MIQUELON",
        "currency": "French Franc",
        "alphabeticcode": "FRF",
        "numericcode": "250",
        "minorunit": null,
        "withdrawaldate": "2002-03"
    },
    {
        "entity": "SPAIN",
        "currency": "\"A\" Account (convertible Peseta Account)",
        "alphabeticcode": "ESB",
        "numericcode": "995",
        "minorunit": null,
        "withdrawaldate": "1994-12"
    },
    {
        "entity": "SWAZILAND",
        "currency": "Lilangeni",
        "alphabeticcode": "SZL",
        "numericcode": "748",
        "minorunit": null,
        "withdrawaldate": "2018-08"
    },
    {
        "entity": "TAJIKISTAN",
        "currency": "Tajik Ruble",
        "alphabeticcode": "TJR",
        "numericcode": "762",
        "minorunit": null,
        "withdrawaldate": "2001-04"
    },
    {
        "entity": "COLOMBIA",
        "currency": "Unidad de Valor Real",
        "alphabeticcode": "COU",
        "numericcode": "970",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "GUADELOUPE",
        "currency": "Euro",
        "alphabeticcode": "EUR",
        "numericcode": "978",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "HOLY SEE (THE)",
        "currency": "Euro",
        "alphabeticcode": "EUR",
        "numericcode": "978",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "KUWAIT",
        "currency": "Kuwaiti Dinar",
        "alphabeticcode": "KWD",
        "numericcode": "414",
        "minorunit": "3",
        "withdrawaldate": null
    },
    {
        "entity": "LEBANON",
        "currency": "Lebanese Pound",
        "alphabeticcode": "LBP",
        "numericcode": "422",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "SAINT BARTH\u00c9LEMY",
        "currency": "Euro",
        "alphabeticcode": "EUR",
        "numericcode": "978",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "T\u00dcRK\u0130YE",
        "currency": "Turkish Lira",
        "alphabeticcode": "TRY",
        "numericcode": "949",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "AUSTRIA",
        "currency": "Schilling",
        "alphabeticcode": "ATS",
        "numericcode": "040",
        "minorunit": null,
        "withdrawaldate": "2002-03"
    },
    {
        "entity": "AZERBAIJAN",
        "currency": "Russian Ruble",
        "alphabeticcode": "RUR",
        "numericcode": "810",
        "minorunit": null,
        "withdrawaldate": "1994-08"
    },
    {
        "entity": "BELARUS",
        "currency": "Belarusian Ruble",
        "alphabeticcode": "BYB",
        "numericcode": "112",
        "minorunit": null,
        "withdrawaldate": "2001-01"
    },
    {
        "entity": "MALAWI",
        "currency": "Kwacha",
        "alphabeticcode": "MWK",
        "numericcode": "454",
        "minorunit": null,
        "withdrawaldate": "2016-02"
    },
    {
        "entity": "ZIMBABWE",
        "currency": "Zimbabwe Dollar",
        "alphabeticcode": "ZWD",
        "numericcode": "716",
        "minorunit": null,
        "withdrawaldate": "2008-08"
    },
    {
        "entity": "ALGERIA",
        "currency": "Algerian Dinar",
        "alphabeticcode": "DZD",
        "numericcode": "012",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "ARUBA",
        "currency": "Aruban Florin",
        "alphabeticcode": "AWG",
        "numericcode": "533",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "CANADA",
        "currency": "Canadian Dollar",
        "alphabeticcode": "CAD",
        "numericcode": "124",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "CAYMAN ISLANDS (THE)",
        "currency": "Cayman Islands Dollar",
        "alphabeticcode": "KYD",
        "numericcode": "136",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "EQUATORIAL GUINEA",
        "currency": "CFA Franc BEAC",
        "alphabeticcode": "XAF",
        "numericcode": "950",
        "minorunit": "0",
        "withdrawaldate": null
    },
    {
        "entity": "GREENLAND",
        "currency": "Danish Krone",
        "alphabeticcode": "DKK",
        "numericcode": "208",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "SPAIN",
        "currency": "Euro",
        "alphabeticcode": "EUR",
        "numericcode": "978",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "SYRIAN ARAB REPUBLIC",
        "currency": "Syrian Pound",
        "alphabeticcode": "SYP",
        "numericcode": "760",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "TANZANIA, UNITED REPUBLIC OF",
        "currency": "Tanzanian Shilling",
        "alphabeticcode": "TZS",
        "numericcode": "834",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "\u00c5LAND ISLANDS",
        "currency": "Markka",
        "alphabeticcode": "FIM",
        "numericcode": "246",
        "minorunit": null,
        "withdrawaldate": "2002-03"
    },
    {
        "entity": "KYRGYZSTAN",
        "currency": "Russian Ruble",
        "alphabeticcode": "RUR",
        "numericcode": "810",
        "minorunit": null,
        "withdrawaldate": "1993-01"
    },
    {
        "entity": "MALDIVES",
        "currency": "Maldive Rupee",
        "alphabeticcode": "MVQ",
        "numericcode": "462",
        "minorunit": null,
        "withdrawaldate": "1989-12"
    },
    {
        "entity": "UNION OF SOVIET SOCIALIST REPUBLICS",
        "currency": "Rouble",
        "alphabeticcode": "SUR",
        "numericcode": "810",
        "minorunit": null,
        "withdrawaldate": "1990-12"
    },
    {
        "entity": "UNITED STATES",
        "currency": "US Dollar (Same day)",
        "alphabeticcode": "USS",
        "numericcode": "998",
        "minorunit": null,
        "withdrawaldate": "2014-03"
    },
    {
        "entity": "ZAIRE",
        "currency": "New Zaire",
        "alphabeticcode": "ZRN",
        "numericcode": "180",
        "minorunit": null,
        "withdrawaldate": "1999-06"
    },
    {
        "entity": "ANGOLA",
        "currency": "Kwanza",
        "alphabeticcode": "AOA",
        "numericcode": "973",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "BRITISH INDIAN OCEAN TERRITORY (THE)",
        "currency": "US Dollar",
        "alphabeticcode": "USD",
        "numericcode": "840",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "PHILIPPINES (THE)",
        "currency": "Philippine Peso",
        "alphabeticcode": "PHP",
        "numericcode": "608",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "SAMOA",
        "currency": "Tala",
        "alphabeticcode": "WST",
        "numericcode": "882",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "BOSNIA AND HERZEGOVINA",
        "currency": "Dinar",
        "alphabeticcode": "BAD",
        "numericcode": "070",
        "minorunit": null,
        "withdrawaldate": "1998-07"
    },
    {
        "entity": "BRAZIL",
        "currency": "New Cruzado",
        "alphabeticcode": "BRN",
        "numericcode": "076",
        "minorunit": null,
        "withdrawaldate": "1990-03"
    },
    {
        "entity": "BRAZIL",
        "currency": "Cruzeiro Real",
        "alphabeticcode": "BRR",
        "numericcode": "987",
        "minorunit": null,
        "withdrawaldate": "1994-07"
    },
    {
        "entity": "CYPRUS",
        "currency": "Cyprus Pound",
        "alphabeticcode": "CYP",
        "numericcode": "196",
        "minorunit": null,
        "withdrawaldate": "2008-01"
    },
    {
        "entity": "GUINEA",
        "currency": "Syli",
        "alphabeticcode": "GNS",
        "numericcode": "324",
        "minorunit": null,
        "withdrawaldate": "1986-02"
    },
    {
        "entity": "KAZAKHSTAN",
        "currency": "Russian Ruble",
        "alphabeticcode": "RUR",
        "numericcode": "810",
        "minorunit": null,
        "withdrawaldate": "1994-05"
    },
    {
        "entity": "LITHUANIA",
        "currency": "Lithuanian Litas",
        "alphabeticcode": "LTL",
        "numericcode": "440",
        "minorunit": null,
        "withdrawaldate": "2014-12"
    },
    {
        "entity": "MALTA",
        "currency": "Maltese Pound",
        "alphabeticcode": "MTP",
        "numericcode": "470",
        "minorunit": null,
        "withdrawaldate": "1983-06"
    },
    {
        "entity": "TURKMENISTAN",
        "currency": "Russian Ruble",
        "alphabeticcode": "RUR",
        "numericcode": "810",
        "minorunit": null,
        "withdrawaldate": "1993-10"
    },
    {
        "entity": "URUGUAY",
        "currency": "Old Uruguay Peso",
        "alphabeticcode": "UYN",
        "numericcode": "858",
        "minorunit": null,
        "withdrawaldate": "1989-12"
    },
    {
        "entity": "CROATIA",
        "currency": "Euro",
        "alphabeticcode": "EUR",
        "numericcode": "978",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "HAITI",
        "currency": "Gourde",
        "alphabeticcode": "HTG",
        "numericcode": "332",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "KOREA (THE REPUBLIC OF)",
        "currency": "Won",
        "alphabeticcode": "KRW",
        "numericcode": "410",
        "minorunit": "0",
        "withdrawaldate": null
    },
    {
        "entity": "MAYOTTE",
        "currency": "Euro",
        "alphabeticcode": "EUR",
        "numericcode": "978",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "PORTUGAL",
        "currency": "Euro",
        "alphabeticcode": "EUR",
        "numericcode": "978",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "QATAR",
        "currency": "Qatari Rial",
        "alphabeticcode": "QAR",
        "numericcode": "634",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "VIRGIN ISLANDS (U.S.)",
        "currency": "US Dollar",
        "alphabeticcode": "USD",
        "numericcode": "840",
        "minorunit": "2",
        "withdrawaldate": null
    },
    {
        "entity": "FRENCH SOUTHERN TERRITORIES",
        "currency": "French Franc",
        "alphabeticcode": "FRF",
        "numericcode": "250",
        "minorunit": null,
        "withdrawaldate": "2002-03"
    },
    {
        "entity": "NICARAGUA",
        "currency": "Cordoba",
        "alphabeticcode": "NIC",
        "numericcode": "558",
        "minorunit": null,
        "withdrawaldate": "1990-10"
    },
    {
        "entity": "SLOVENIA",
        "currency": "Tolar",
        "alphabeticcode": "SIT",
        "numericcode": "705",
        "minorunit": null,
        "withdrawaldate": "2007-01"
    },
    {
        "entity": "SOUTH SUDAN",
        "currency": "Sudanese Pound",
        "alphabeticcode": "SDG",
        "numericcode": "938",
        "minorunit": null,
        "withdrawaldate": "2012-09"
    },
    {
        "entity": "SWITZERLAND",
        "currency": "WIR Franc (for electronic)",
        "alphabeticcode": "CHC",
        "numericcode": "948",
        "minorunit": null,
        "withdrawaldate": "2004-11"
    }
]


export const countries = [
    {code: "AUT", name: "Austria", iso2: "AT", isoNumeric: "040"},
    {code: "BEL", name: "Belgium", iso2: "BE", isoNumeric: "056"},
    {code: "BGR", name: "Bulgaria", iso2: "BG", isoNumeric: "100"},
    {code: "HRV", name: "Croatia", iso2: "HR", isoNumeric: "191"},
    {code: "CYP", name: "Cyprus", iso2: "CY", isoNumeric: "196"},
    {code: "CZE", name: "Czech Republic", iso2: "CZ", isoNumeric: "203"},
    {code: "DNK", name: "Denmark", iso2: "DK", isoNumeric: "208"},
    {code: "DEU", name: "Germany", iso2: "DE", isoNumeric: "276"},
    {code: "EST", name: "Estonia", iso2: "EE", isoNumeric: "233"},
    {code: "FIN", name: "Finland", iso2: "FI", isoNumeric: "246"},
    {code: "FRA", name: "France", iso2: "FR", isoNumeric: "250"},
    {code: "GRC", name: "Greece", iso2: "GR", isoNumeric: "300"},
    {code: "HUN", name: "Hungary", iso2: "HU", isoNumeric: "348"},
    {code: "ISL", name: "Iceland", iso2: "IS", isoNumeric: "352"},
    {code: "IRL", name: "Ireland", iso2: "IE", isoNumeric: "372"},
    {code: "ITA", name: "Italy", iso2: "IT", isoNumeric: "380"},
    {code: "LVA", name: "Latvia", iso2: "LV", isoNumeric: "428"},
    {code: "LIE", name: "Liechtenstein", iso2: "LI", isoNumeric: "438"},
    {code: "LTU", name: "Lithuania", iso2: "LT", isoNumeric: "440"},
    {code: "LUX", name: "Luxembourg", iso2: "LU", isoNumeric: "442"},
    {code: "MLT", name: "Malta", iso2: "MT", isoNumeric: "470"},
    {code: "NLD", name: "Netherlands", iso2: "NL", isoNumeric: "528"},
    {code: "NOR", name: "Norway", iso2: "NO", isoNumeric: "578"},
    {code: "POL", name: "Poland", iso2: "PL", isoNumeric: "616"},
    {code: "PRT", name: "Portugal", iso2: "PT", isoNumeric: "620"},
    {code: "ROU", name: "Romania", iso2: "RO", isoNumeric: "642"},
    {code: "SVN", name: "Slovenia", iso2: "SI", isoNumeric: "705"},
    {code: "SVK", name: "Slovakia", iso2: "SK", isoNumeric: "703"},
    {code: "ESP", name: "Spain", iso2: "ES", isoNumeric: "724"},
    {code: "SWE", name: "Sweden", iso2: "SE", isoNumeric: "752"},
    {code: "CHE", name: "Switzerland", iso2: "CH", isoNumeric: "756"},
    {code: "GUF", name: "French Guiana", iso2: "GF", isoNumeric: "254"},
    {code: "GLP", name: "Guadeloupe", iso2: "GP", isoNumeric: "312"},
    {code: "MTQ", name: "Martinique", iso2: "MQ", isoNumeric: "474"},
    {code: "MYT", name: "Mayotte", iso2: "YT", isoNumeric: "175"},
    {code: "REU", name: "Réunion", iso2: "RE", isoNumeric: "638"},
    {code: "MAF", name: "Saint Martin", iso2: "MF", isoNumeric: "663"},
];

export const currencyOptions = [
    {name: "Afghan Afghani", code: "AFA"},
    {name: "Albanian Lek", code: "ALL"},
    {name: "Algerian Dinar", code: "DZD"},
    {name: "Angolan Kwanza", code: "AOA"},
    {name: "Argentine Peso", code: "ARS"},
    {name: "Armenian Dram", code: "AMD"},
    {name: "Aruban Florin", code: "AWG"},
    {name: "Australian Dollar", code: "AUD"},
    {name: "Azerbaijani Manat", code: "AZN"},
    {name: "Bahamian Dollar", code: "BSD"},
    {name: "Bahraini Dinar", code: "BHD"},
    {name: "Bangladeshi Taka", code: "BDT"},
    {name: "Barbadian Dollar", code: "BBD"},
    {name: "Belarusian Ruble", code: "BYR"},
    {name: "Belgian Franc", code: "BEF"},
    {name: "Belize Dollar", code: "BZD"},
    {name: "Bermudan Dollar", code: "BMD"},
    {name: "Bhutanese Ngultrum", code: "BTN"},
    {name: "Bitcoin", code: "BTC"},
    {name: "Bolivian Boliviano", code: "BOB"},
    {name: "Bosnia-Herzegovina Convertible Mark", code: "BAM"},
    {name: "Botswanan Pula", code: "BWP"},
    {name: "Brazilian Real", code: "BRL"},
    {name: "British Pound Sterling", code: "GBP"},
    {name: "Brunei Dollar", code: "BND"},
    {name: "Bulgarian Lev", code: "BGN"},
    {name: "Burundian Franc", code: "BIF"},
    {name: "Cambodian Riel", code: "KHR"},
    {name: "Canadian Dollar", code: "CAD"},
    {name: "Cape Verdean Escudo", code: "CVE"},
    {name: "Cayman Islands Dollar", code: "KYD"},
    {name: "CFA Franc BCEAO", code: "XOF"},
    {name: "CFA Franc BEAC", code: "XAF"},
    {name: "CFP Franc", code: "XPF"},
    {name: "Chilean Peso", code: "CLP"},
    {name: "Chilean Unit of Account", code: "CLF"},
    {name: "Chinese Yuan", code: "CNY"},
    {name: "Colombian Peso", code: "COP"},
    {name: "Comorian Franc", code: "KMF"},
    {name: "Congolese Franc", code: "CDF"},
    {name: "Costa Rican Colón", code: "CRC"},
    {name: "Croatian Kuna", code: "HRK"},
    {name: "Cuban Convertible Peso", code: "CUC"},
    {name: "Czech Republic Koruna", code: "CZK"},
    {name: "Danish Krone", code: "DKK"},
    {name: "Djiboutian Franc", code: "DJF"},
    {name: "Dominican Peso", code: "DOP"},
    {name: "East Caribbean Dollar", code: "XCD"},
    {name: "Egyptian Pound", code: "EGP"},
    {name: "Eritrean Nakfa", code: "ERN"},
    {name: "Estonian Kroon", code: "EEK"},
    {name: "Ethiopian Birr", code: "ETB"},
    {name: "Euro", code: "EUR"},
    {name: "Falkland Islands Pound", code: "FKP"},
    {name: "Fijian Dollar", code: "FJD"},
    {name: "Gambian Dalasi", code: "GMD"},
    {name: "Georgian Lari", code: "GEL"},
    {name: "German Mark", code: "DEM"},
    {name: "Ghanaian Cedi", code: "GHS"},
    {name: "Gibraltar Pound", code: "GIP"},
    {name: "Greek Drachma", code: "GRD"},
    {name: "Guatemalan Quetzal", code: "GTQ"},
    {name: "Guinean Franc", code: "GNF"},
    {name: "Guyanaese Dollar", code: "GYD"},
    {name: "Haitian Gourde", code: "HTG"},
    {name: "Honduran Lempira", code: "HNL"},
    {name: "Hong Kong Dollar", code: "HKD"},
    {name: "Hungarian Forint", code: "HUF"},
    {name: "Icelandic Króna", code: "ISK"},
    {name: "Indian Rupee", code: "INR"},
    {name: "Indonesian Rupiah", code: "IDR"},
    {name: "Iranian Rial", code: "IRR"},
    {name: "Iraqi Dinar", code: "IQD"},
    {name: "Israeli New Sheqel", code: "ILS"},
    {name: "Italian Lira", code: "ITL"},
    {name: "Jamaican Dollar", code: "JMD"},
    {name: "Japanese Yen", code: "JPY"},
    {name: "Jordanian Dinar", code: "JOD"},
    {name: "Kazakhstani Tenge", code: "KZT"},
    {name: "Kenyan Shilling", code: "KES"},
    {name: "Kuwaiti Dinar", code: "KWD"},
    {name: "Kyrgystani Som", code: "KGS"},
    {name: "Laotian Kip", code: "LAK"},
    {name: "Latvian Lats", code: "LVL"},
    {name: "Lebanese Pound", code: "LBP"},
    {name: "Lesotho Loti", code: "LSL"},
    {name: "Liberian Dollar", code: "LRD"},
    {name: "Libyan Dinar", code: "LYD"},
    {name: "Litecoin", code: "LTC"},
    {name: "Lithuanian Litas", code: "LTL"},
    {name: "Macanese Pataca", code: "MOP"},
    {name: "Macedonian Denar", code: "MKD"},
    {name: "Malagasy Ariary", code: "MGA"},
    {name: "Malawian Kwacha", code: "MWK"},
    {name: "Malaysian Ringgit", code: "MYR"},
    {name: "Maldivian Rufiyaa", code: "MVR"},
    {name: "Mauritanian Ouguiya", code: "MRO"},
    {name: "Mauritian Rupee", code: "MUR"},
    {name: "Mexican Peso", code: "MXN"},
    {name: "Moldovan Leu", code: "MDL"},
    {name: "Mongolian Tugrik", code: "MNT"},
    {name: "Moroccan Dirham", code: "MAD"},
    {name: "Mozambican Metical", code: "MZM"},
    {name: "Myanmar Kyat", code: "MMK"},
    {name: "Namibian Dollar", code: "NAD"},
    {name: "Nepalese Rupee", code: "NPR"},
    {name: "Netherlands Antillean Guilder", code: "ANG"},
    {name: "New Taiwan Dollar", code: "TWD"},
    {name: "New Zealand Dollar", code: "NZD"},
    {name: "Nicaraguan Córdoba", code: "NIO"},
    {name: "Nigerian Naira", code: "NGN"},
    {name: "North Korean Won", code: "KPW"},
    {name: "Norwegian Krone", code: "NOK"},
    {name: "Omani Rial", code: "OMR"},
    {name: "Pakistani Rupee", code: "PKR"},
    {name: "Panamanian Balboa", code: "PAB"},
    {name: "Papua New Guinean Kina", code: "PGK"},
    {name: "Paraguayan Guarani", code: "PYG"},
    {name: "Peruvian Nuevo Sol", code: "PEN"},
    {name: "Philippine Peso", code: "PHP"},
    {name: "Polish Zloty", code: "PLN"},
    {name: "Qatari Rial", code: "QAR"},
    {name: "Romanian Leu", code: "RON"},
    {name: "Russian Ruble", code: "RUB"},
    {name: "Rwandan Franc", code: "RWF"},
    {name: "Salvadoran Colón", code: "SVC"},
    {name: "Samoan Tala", code: "WST"},
    {name: "São Tomé and Príncipe Dobra", code: "STD"},
    {name: "Saudi Riyal", code: "SAR"},
    {name: "Serbian Dinar", code: "RSD"},
    {name: "Seychellois Rupee", code: "SCR"},
    {name: "Sierra Leonean Leone", code: "SLL"},
    {name: "Singapore Dollar", code: "SGD"},
    {name: "Slovak Koruna", code: "SKK"},
    {name: "Solomon Islands Dollar", code: "SBD"},
    {name: "Somali Shilling", code: "SOS"},
    {name: "South African Rand", code: "ZAR"},
    {name: "South Korean Won", code: "KRW"},
    {name: "South Sudanese Pound", code: "SSP"},
    {name: "Special Drawing Rights", code: "XDR"},
    {name: "Sri Lankan Rupee", code: "LKR"},
    {name: "St. Helena Pound", code: "SHP"},
    {name: "Sudanese Pound", code: "SDG"},
    {name: "Surinamese Dollar", code: "SRD"},
    {name: "Swazi Lilangeni", code: "SZL"},
    {name: "Swedish Krona", code: "SEK"},
    {name: "Swiss Franc", code: "CHF"},
    {name: "Syrian Pound", code: "SYP"},
    {name: "Tajikistani Somoni", code: "TJS"},
    {name: "Tanzanian Shilling", code: "TZS"},
    {name: "Thai Baht", code: "THB"},
    {name: "Tongan Pa'anga", code: "TOP"},
    {name: "Trinidad & Tobago Dollar", code: "TTD"},
    {name: "Tunisian Dinar", code: "TND"},
    {name: "Turkish Lira", code: "TRY"},
    {name: "Turkmenistani Manat", code: "TMT"},
    {name: "Ugandan Shilling", code: "UGX"},
    {name: "Ukrainian Hryvnia", code: "UAH"},
    {name: "United Arab Emirates Dirham", code: "AED"},
    {name: "Uruguayan Peso", code: "UYU"},
    {name: "US Dollar", code: "USD"},
    {name: "Uzbekistan Som", code: "UZS"},
    {name: "Vanuatu Vatu", code: "VUV"},
    {name: "Venezuelan BolÃvar", code: "VEF"},
    {name: "Vietnamese Dong", code: "VND"},
    {name: "Yemeni Rial", code: "YER"},
    {name: "Zambian Kwacha", code: "ZMK"},
    {name: "Zimbabwean dollar", code: "ZWL"}
];



const restrictedWords = [
    // English
    'password', 'admin', '123456', 'qwerty', 'letmein', 'monkey', 'abc123', 'welcome',
    'fuck', 'shit', 'ass', 'asshole', 'bitch', 'bastard', 'cunt', 'dick', 'cock', 'pussy',
    'whore', 'slut', 'piss', 'damn', 'goddamn', 'motherfucker', 'wanker', 'bullshit',
    'fuckoff', 'twat', 'prick',

    // Bulgarian
    'парола', 'админ', 'мамка', 'глупак', 'простак', 'шибан', 'курва', 'путка', 'пичка',
    'гъз', 'дупе', 'копеле', 'шибаняк', 'педераст', 'лайно', 'гомнар', 'скапаняк',
    'кур', 'пишка', 'задник',

    // Serbian/Croatian/Bosnian
    'lozinka', 'sranje', 'govno', 'jebati', 'kurac', 'pička', 'dupe', 'šupak', 'sisa', 'kurčina',
    'pizda', 'jebač', 'jebeno', 'jebi', 'jebote', 'izdrkati', 'pizdarija', 'guzica', 'srati',

    // Czech
    'heslo', 'kurva', 'hovno', 'prdel', 'čurák', 'píča', 'kokot', 'prdět', 'posrat', 'zmrd',
    'zkurvysyn', 'do piče', 'pičus', 'curak', 'jebat', 'mrdka', 'čůrák', 'vyjebaný', 'nasrat',

    // Danish
    'kodeord', 'fanden', 'helvede', 'lort', 'pik', 'fisse', 'røv', 'røvhul', 'skid', 'knep',
    'fuck', 'pis', 'kraftedeme', 'satan', 'svin', 'kælling', 'kusse', 'patter', 'skide',

    // Dutch
    'wachtwoord', 'kut', 'lul', 'klootzak', 'eikel', 'kanker', 'tyfus', 'godverdomme',
    'klote', 'neuk', 'neuken', 'hoer', 'slet', 'trut', 'reet', 'kontgat', 'kakker', 'schijt', 'pik',

    // Estonian
    'parool', 'perse', 'sitt', 'munn', 'vittu', 'türa', 'pask', 'persevest', 'lirva', 'lits',
    'jobu', 'puts', 'sitapea', 'nuss', 'vastik', 'kuradi', 'persse', 'sitane', 'munni',

    // Finnish
    'salasana', 'perkele', 'vittu', 'paska', 'kyrpä', 'helvetti', 'saatana', 'jumalauta',
    'pillu', 'kusipää', 'mulkku', 'perse', 'paskainen', 'runkata', 'vittumainen',
    'paskapää', 'huora', 'lutka', 'persereikä',

    // French
    'motdepasse', 'merde', 'putain', 'connard', 'enculé', 'salope', 'con', 'foutre', 'bite',
    'cul', 'couilles', 'nique', 'niquer', 'branler', 'bordel', 'chier', 'baiser', 'pute', 'branleur',

    // German
    'passwort', 'scheiße', 'arschloch', 'wichser', 'fotze', 'hurensohn', 'schwanz', 'fick',
    'ficken', 'scheißkerl', 'kacke', 'muschi', 'schwuchtel', 'hure', 'nutte', 'pisser',
    'scheisse', 'verpiss', 'mistkerl',

    // Greek
    'κωδικός', 'μαλάκας', 'σκατά', 'πούστης', 'καριόλης', 'γαμώ', 'γαμιέσαι', 'κώλος',
    'βλάκας', 'μουνί', 'μουνόπανο', 'παλιομαλάκας', 'γαμημένος', 'καργιόλης', 'τσόλι',
    'αρχίδια', 'γαμιόλης', 'πουτάνα', 'κωλοτρυπίδα', 'παπάρας',

    // Hungarian
    'jelszó', 'szar', 'fasz', 'kurva', 'baszik', 'picsa', 'pina', 'segg', 'seggfej', 'baszd',
    'geci', 'köcsög', 'anyád', 'baszott', 'szaros', 'fasszopó', 'bazdmeg', 'ribanc', 'faszfej',

    // Icelandic
    'lykilorð', 'skítur', 'rassgat', 'fokk', 'tík', 'andskotinn', 'djöfull', 'helvíti', 'hóra',
    'píka', 'typpi', 'drullusokkur', 'fífl', 'mella', 'fáviti', 'hálfviti', 'skítseiði', 'skíthæll',

    // Italian
    'password', 'cazzo', 'merda', 'stronzo', 'vaffanculo', 'puttana', 'troia', 'fica', 'figa',
    'minchia', 'coglione', 'fottere', 'inculare', 'porca', 'pompino', 'mignotta', 'tette',
    'figlio di puttana', 'stronza',

    // Latvian
    'parole', 'sūds', 'dirsa', 'pimpis', 'mauka', 'pēdējs', 'pisties', 'pakaļa', 'sūkāt',
    'izpist', 'maukas dēls', 'pizdec', 'mēsls', 'stulbenis', 'mūlāps', 'pidars', 'kuce',
    'dirsā', 'sūdīgs',

    // Lithuanian
    'slaptažodis', 'šūdas', 'kalė', 'žaltys', 'pyzdiec', 'bybi', 'subinė', 'šiknius', 'kurva',
    'šūdinas', 'pisti', 'išpisti', 'nachui', 'pize', 'šikti', 'bybis', 'kalės vaikas', 'kekšė', 'nahui',

    // Maltese
    'password', 'għoxx', 'hara', 'żobb', 'liba', 'ħara', 'qaħba', 'nieklu', 'żobbok', 'ommok',
    'liva', 'żobbi', 'pajjiz', 'għoxx ommok', 'għoxxok', 'foxx', 'tikomli', 'kilba', 'tiżżobb',

    // Norwegian
    'passord', 'faen', 'helvete', 'dritt', 'kukk', 'fitte', 'ræva', 'rasshøl', 'føkk', 'satan',
    'hore', 'pikk', 'kuk', 'drittsekk', 'hestkuk', 'jævla', 'forpult', 'kødd', 'fittetryne',

    // Polish
    'hasło', 'kurwa', 'gówno', 'dupa', 'chuj', 'pierdolić', 'jebać', 'pizda', 'kutas',
    'skurwysyn', 'pojeb', 'szmata', 'cipa', 'dziwka', 'fiut', 'huj', 'spierdalaj', 'spermojad', 'zjeb',

    // Portuguese
    'senha', 'merda', 'caralho', 'puta', 'foder', 'porra', 'filho da puta', 'cu', 'bosta',
    'cagar', 'buceta', 'punheta', 'vadia', 'cacete', 'piça', 'cuzão', 'fodido', 'rapariga', 'pica',

    // Romanian
    'parolă', 'căcat', 'pulă', 'pizdă', 'fut', 'futu-te', 'sugaci', 'muie', 'curva', 'măta',
    'găoz', 'poponar', 'pula', 'sugi', 'sugi pula', 'morții', 'pizda', 'papagal', 'prost',

    // Slovak
    'heslo', 'hovno', 'kokot', 'kurva', 'jebať', 'piča', 'kokotina', 'chuj', 'riť', 'jebnutý',
    'čurák', 'hajzel', 'pojebať', 'mrdka', 'skurvysyn', 'prdel', 'šukať', 'sračka', 'zmrd',

    // Slovenian
    'geslo', 'drek', 'pizda', 'kurac', 'jebati', 'sranje', 'rit', 'kurec', 'fukati', 'pička',
    'prasec', 'fuk', 'jebi se', 'kurba', 'prasica', 'kurčev', 'zajebati',

    // Spanish
    'contraseña', 'mierda', 'joder', 'cojones', 'puta', 'follar', 'cabrón', 'coño',
    'gilipollas', 'hostia', 'jodete', 'mamón', 'marica', 'pendejo', 'pollas', 'puto',
    'zorra', 'imbécil', 'hijoputa',

    // Swedish
    'lösenord', 'skit', 'fan', 'helvete', 'fitta', 'kuk', 'röv', 'arsle', 'jävlar', 'satan',
    'hora', 'knulla', 'bajs', 'balle', 'tusan', 'rövhål', 'kuksugare', 'fan i helvete', 'surfitta'
];
