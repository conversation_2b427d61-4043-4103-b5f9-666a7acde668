//
// const express = require('express');
// const axios = require('axios');
// const jwt = require('jsonwebtoken');
// const fs = require('fs');
// const path = require("path");
//
//
//
//
// const PRIVATE_KEY = fs.readFileSync( path.join(__dirname,"..","private.pem"), 'utf8');
//
// // Issuer URL
// const ISSUER = 'https://your-issuer-url.com';
//
// // Function to generate JWT token
// function generateJwtToken() {
//     const payload = {
//         sub: 'https://card-auth-staging.ryvyl.eu',
//         iss: ISSUER,
//         iat: Math.floor(Date.now() / 1000),
//         exp: Math.floor(Date.now() / 1000) + (60 * 60), // 1 hour validity
//     };
//
//     return jwt.sign(payload, PRIVATE_KEY, { algorithm: 'ES512' });
// }
//
// // ✅ Reusable function
// async function fetchBalance(accountIdentification) {
//
//     if (!accountIdentification) {
//         throw new Error('accountIdentification is required');
//     }
//
//     const token = generateJwtToken();
//     const apiUrl = 'https://card-auth-staging.ryvyl.eu/api/itcard/balance-check';
//
//     const response = await axios.get(apiUrl, {
//         headers: {
//             Authorization: `Bearer ${token}`
//         },
//         params: { accountIdentification }
//     });
//
//     return response.data;
// }
// module.exports = {
//     generateJwtToken,fetchBalance
//  };
//
