const axios = require("axios");
const WebhookLog = require("../models/WebhookLog");
const jwt = require('jsonwebtoken');
const fs = require('fs');
const {join} = require("path");

// Read private key
const PRIVATE_KEY = fs.readFileSync(join(__dirname, '..', '', 'private.pem'), 'utf8');

// Issuer URL
const ISSUER = 'https://your-issuer-url.com';

// Function to generate JWT token
function generateJwtToken() {
    const payload = {
        sub: 'https://card-auth-staging.ryvyl.eu',
        iss: ISSUER,
        exp: Math.floor(Date.now() / 1000) + (60 * 60), // 1 hour validity
    };

    return jwt.sign(payload, PRIVATE_KEY, { algorithm: 'ES512' });
}


async function sendCardCreationWebhook(cardData) {
    const webhookUrl = process.env.CARD_WEBHOOK;
    const payload = { event: "card_created", cardData };

    let responseStatus = null;
    let responseBody = null;
    let errorData = null;

    try {
        const token = generateJwtToken();
        console.log("Sending webhook notification token:", token);

        const response = await axios.post(webhookUrl, payload, {
            headers: {
                "Content-Type": "application/json",
                "Authorization": `Bearer ${token}`
            }
        });

        responseStatus = response.status;
        responseBody = response.data;

        console.log("Webhook sent successfully:", response.data);
    } catch (error) {
        responseStatus = error.response?.status || 500;
        errorData = {
            message: error.message,
            data: error.response?.data || null
        };

        console.error("Failed to send webhook:", errorData);
    }

    // Log webhook attempt to MongoDB
    try {
        await WebhookLog.create({
            event: "card_created",
            webhookUrl,
            payloadSent: payload,
            responseStatus,
            responseBody,
            error: errorData
        });
    } catch (logError) {
        console.error("Failed to log webhook attempt:", logError.message);
    }
}

module.exports = sendCardCreationWebhook;
