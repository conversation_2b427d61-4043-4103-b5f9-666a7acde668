const jwt = require('jsonwebtoken');
const axios = require('axios');
const fs = require('fs');
const { join } = require('path');

const PRIVATE_KEY = fs.readFileSync(join(__dirname, '..', 'private.pem'), 'utf8');
const ISSUER = 'https://your-issuer-url.com';

/**
 * Generates a JWT token signed with ES512 algorithm.
 * @returns {string} Signed JWT token
 */
function generateJwtToken() {
    const payload = {
        sub: 'https://card-auth-staging.ryvyl.eu',
        iss: ISSUER,
        exp: Math.floor(Date.now() / 1000) + 3600, // valid for 1 hour
    };

    return jwt.sign(payload, PRIVATE_KEY, { algorithm: 'ES512' });
}

/**
 * Fetches account balance from Ryvyl API
 * @param {string} accountIdentification
 * @returns {Promise<Object>} Response data from API
 */
async function fetchBalance(accountIdentification) {
    if (!accountIdentification) {
        throw new Error('accountIdentification is required');
    }

    const token = generateJwtToken();
    const apiUrl = 'https://card-auth-staging.ryvyl.eu/api/itcard/balance-check';

    const response = await axios.get(apiUrl, {
        headers: {
            Authorization: `Bearer ${token}`
        },
        params: { accountIdentification }
    });

    return response.data;
}




module.exports = {
    generateJwtToken,
    fetchBalance
};
