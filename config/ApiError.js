/**
 * Custom API Error class
 * Used for consistent error handling across the application
 */
class ApiError extends Error {
    /**
     * Create a new API error
     * @param {number} statusCode - HTTP status code
     * @param {string} message - Error message
     * @param {Array} errors - Array of validation errors
     * @param {string} stack - Error stack trace
     */
    constructor(statusCode, message, errors = [], stack = "") {
        super(message);
        this.statusCode = statusCode;
        this.errors = errors;
        this.success = false;

        if (stack) {
            this.stack = stack;
        } else {
            Error.captureStackTrace(this, this.constructor);
        }
    }
}

/**
 * Error handler middleware
 * @param {Error} err - Error object
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const errorHandler = (err, req, res, next) => {
    let error = { ...err };
    error.message = err.message;

    // Log error for debugging
    console.error("Error:", err);

    // Mongoose validation error
    if (err.name === "ValidationError") {
        const message = Object.values(err.errors).map((val) => val.message);
        error = new ApiError(400, "Validation Error", message);
    }

    // Mongoose duplicate key error
    if (err.code === 11000) {
        const field = Object.keys(err.keyValue)[0];
        const value = err.keyValue[field];
        const message = `Duplicate field value: ${field} with value: ${value}. Please use another value.`;
        error = new ApiError(400, message);
    }

    // Mongoose cast error (invalid ID)
    if (err.name === "CastError") {
        const message = `Invalid ${err.path}: ${err.value}`;
        error = new ApiError(400, message);
    }

    // JWT errors
    if (err.name === "JsonWebTokenError") {
        error = new ApiError(401, "Invalid token. Please log in again.");
    }

    if (err.name === "TokenExpiredError") {
        error = new ApiError(401, "Your token has expired. Please log in again.");
    }

    // Send response
    res.status(error.statusCode || 500).json({
        success: false,
        message: error.message || "Server Error",
        errors: error.errors || [],
        stack: process.env.NODE_ENV === "development" ? err.stack : undefined,
    });
};

// Export both the ApiError class and errorHandler function
module.exports = {
    ApiError,
    errorHandler
};